import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  ArrowLeft,
  ArrowRight,
  Search,
  Filter,
  CheckCircle,
  AlertTriangle,
  Edit,
  Save,
  ChevronDown,
  ChevronUp,
  Calendar,
  DollarSign,
  Tag,
  Settings,
  FileCheck,
  Loader2
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Import matching components
import MatchingConfigForm from '@/components/Reconciliation/MatchingConfigForm';
import MatchingResultsDisplay from '@/components/Reconciliation/MatchingResultsDisplay';
import matchingService, { MatchingResults } from '@/services/matchingService';
import { formValuesToApiRequest } from '@/components/Reconciliation/validationSchema';

// Mock data for reconciliation
const invoiceData = [
  { id: 'INV-001', date: '2023-09-01', amount: 1250.00, customer: 'Acme Corp', status: 'Matched', confidence: 98, notes: '' },
  { id: 'INV-002', date: '2023-09-05', amount: 750.50, customer: 'Globex Inc', status: 'Matched', confidence: 95, notes: '' },
  { id: 'INV-003', date: '2023-09-10', amount: 2100.75, customer: 'Stark Industries', status: 'Anomaly', confidence: 65, notes: 'Amount mismatch' },
  { id: 'INV-004', date: '2023-09-15', amount: 1800.25, customer: 'Wayne Enterprises', status: 'Unmatched', confidence: 0, notes: 'No matching payment found' },
  { id: 'INV-005', date: '2023-09-20', amount: 950.00, customer: 'Umbrella Corp', status: 'Matched', confidence: 92, notes: '' },
  { id: 'INV-006', date: '2023-09-22', amount: 1500.00, customer: 'Acme Corp', status: 'Anomaly', confidence: 72, notes: 'Date mismatch' },
  { id: 'INV-007', date: '2023-09-25', amount: 3200.50, customer: 'Globex Inc', status: 'Matched', confidence: 97, notes: '' },
  { id: 'INV-008', date: '2023-09-28', amount: 875.25, customer: 'Stark Industries', status: 'Matched', confidence: 94, notes: '' },
];

const paymentData = [
  { id: 'PMT-001', date: '2023-09-02', amount: 1250.00, customer: 'Acme Corp', status: 'Matched', confidence: 98, notes: '' },
  { id: 'PMT-002', date: '2023-09-06', amount: 750.50, customer: 'Globex Inc', status: 'Matched', confidence: 95, notes: '' },
  { id: 'PMT-003', date: '2023-09-11', amount: 2000.00, customer: 'Stark Industries', status: 'Anomaly', confidence: 65, notes: 'Amount mismatch' },
  { id: 'PMT-005', date: '2023-09-21', amount: 950.00, customer: 'Umbrella Corp', status: 'Matched', confidence: 92, notes: '' },
  { id: 'PMT-006', date: '2023-09-25', amount: 1500.00, customer: 'Acme Corp', status: 'Anomaly', confidence: 72, notes: 'Date mismatch' },
  { id: 'PMT-007', date: '2023-09-26', amount: 3200.50, customer: 'Globex Inc', status: 'Matched', confidence: 97, notes: '' },
  { id: 'PMT-008', date: '2023-09-29', amount: 875.25, customer: 'Stark Industries', status: 'Matched', confidence: 94, notes: '' },
];

const Reconciliation = () => {
  const navigate = useNavigate();

  // Traditional reconciliation state
  const [searchTerm, setSearchTerm] = useState('');
  const [confidenceThreshold, setConfidenceThreshold] = useState([70]);
  const [showOnlyAnomalies, setShowOnlyAnomalies] = useState(false);
  const [showOnlyUnmatched, setShowOnlyUnmatched] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<string | null>(null);
  const [editMode, setEditMode] = useState(false);
  const [editedAmount, setEditedAmount] = useState('');
  const [editedDate, setEditedDate] = useState('');
  const [editedNotes, setEditedNotes] = useState('');

  // Advanced matching state
  const [activeTab, setActiveTab] = useState('traditional');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [matchingResults, setMatchingResults] = useState<MatchingResults | null>(null);
  const [matchingId, setMatchingId] = useState<string | null>(null);

  // Filter data based on search and filters
  const filteredInvoices = invoiceData.filter(invoice => {
    // Search filter
    const matchesSearch = searchTerm === '' ||
      invoice.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.customer.toLowerCase().includes(searchTerm.toLowerCase());

    // Confidence filter
    const matchesConfidence = invoice.confidence >= confidenceThreshold[0];

    // Status filters
    const matchesAnomalies = !showOnlyAnomalies || invoice.status === 'Anomaly';
    const matchesUnmatched = !showOnlyUnmatched || invoice.status === 'Unmatched';

    return matchesSearch && matchesConfidence && matchesAnomalies && matchesUnmatched;
  });

  const filteredPayments = paymentData.filter(payment => {
    // Search filter
    const matchesSearch = searchTerm === '' ||
      payment.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.customer.toLowerCase().includes(searchTerm.toLowerCase());

    // Confidence filter
    const matchesConfidence = payment.confidence >= confidenceThreshold[0];

    // Status filters
    const matchesAnomalies = !showOnlyAnomalies || payment.status === 'Anomaly';

    return matchesSearch && matchesConfidence && matchesAnomalies;
  });

  const handleRecordSelect = (id: string) => {
    if (selectedRecord === id) {
      setSelectedRecord(null);
      setEditMode(false);
    } else {
      setSelectedRecord(id);
      setEditMode(false);

      // Find the record and set initial edit values
      const invoice = invoiceData.find(inv => inv.id === id);
      if (invoice) {
        setEditedAmount(invoice.amount.toString());
        setEditedDate(invoice.date);
        setEditedNotes(invoice.notes);
      }
    }
  };

  const handleEditToggle = () => {
    setEditMode(!editMode);
  };

  const handleSaveChanges = () => {
    // In a real app, this would update the data
    setEditMode(false);
    // Show success message or update UI
  };

  // Advanced matching handlers
  const handleSubmitMatching = async (data: ReturnType<typeof formValuesToApiRequest>) => {
    setIsLoading(true);
    setError(null);

    try {
      // Call the matching API
      const results = await matchingService.runMatching(data);

      // Set the results and switch to the results tab
      setMatchingResults(results);
      setActiveTab('results');

      // Generate a matching ID (this would normally come from the backend)
      setMatchingId(`match-${Date.now()}`);
    } catch (err: any) {
      console.error('Error running matching:', err);
      setError(err.response?.data?.detail || err.message || 'An error occurred while running matching');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveResults = async () => {
    if (!matchingResults || !matchingId) return;

    setIsLoading(true);
    setError(null);

    try {
      // Save the matching results
      await matchingService.saveMatchingResults(matchingId, matchingResults);

      // Navigate to the summary page
      navigate('/reconciliation-summary', {
        state: { matchingId, stats: matchingResults.stats }
      });
    } catch (err: any) {
      console.error('Error saving matching results:', err);
      setError(err.response?.data?.detail || err.message || 'An error occurred while saving results');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportResults = () => {
    if (!matchingResults) return;

    // Create a JSON blob and download it
    const blob = new Blob([JSON.stringify(matchingResults, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `matching-results-${matchingId || Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Matched':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Matched</Badge>;
      case 'Anomaly':
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Anomaly</Badge>;
      case 'Unmatched':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Unmatched</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 90) {
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">{confidence}%</Badge>;
    } else if (confidence >= 70) {
      return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">{confidence}%</Badge>;
    } else if (confidence > 0) {
      return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">{confidence}%</Badge>;
    } else {
      return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">N/A</Badge>;
    }
  };

  return (
    <div className="pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              className="mr-4"
              onClick={() => navigate('/data-pipeline')}
            >
              <ArrowLeft size={16} className="mr-2" />
              Back to Pipeline
            </Button>
            <h1 className="text-3xl font-bold text-navy">Reconciliation Dashboard</h1>
          </div>

          <div className="flex space-x-2">
            <Button
              variant={activeTab === 'traditional' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('traditional')}
            >
              Traditional View
            </Button>
            <Button
              variant={activeTab === 'advanced' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('advanced')}
            >
              Advanced Matching
            </Button>
          </div>
        </div>

        {activeTab === 'traditional' ? (
          <>
          {/* Traditional Reconciliation View */}
          {/* Global Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Records Processed</p>
                  <p className="text-2xl font-bold">1,240</p>
                </div>
                <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Tag className="h-5 w-5 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Matches Found</p>
                  <p className="text-2xl font-bold">1,170 <span className="text-sm text-green-600 font-normal">(94%)</span></p>
                </div>
                <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Anomalies</p>
                  <p className="text-2xl font-bold">28 <span className="text-sm text-amber-600 font-normal">(2.3%)</span></p>
                </div>
                <div className="h-10 w-10 bg-amber-100 rounded-full flex items-center justify-center">
                  <AlertTriangle className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Unmatched</p>
                  <p className="text-2xl font-bold">42 <span className="text-sm text-red-600 font-normal">(3.4%)</span></p>
                </div>
                <div className="h-10 w-10 bg-red-100 rounded-full flex items-center justify-center">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="relative flex-grow">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                <Input
                  placeholder="Search by ID, customer, or amount..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              <div className="flex flex-wrap gap-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="anomalies"
                    checked={showOnlyAnomalies}
                    onCheckedChange={(checked) => setShowOnlyAnomalies(checked as boolean)}
                  />
                  <Label htmlFor="anomalies" className="text-sm">Show only anomalies</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="unmatched"
                    checked={showOnlyUnmatched}
                    onCheckedChange={(checked) => setShowOnlyUnmatched(checked as boolean)}
                  />
                  <Label htmlFor="unmatched" className="text-sm">Show only unmatched</Label>
                </div>
              </div>
            </div>

            <div className="mt-4">
              <Label className="text-sm">Confidence Threshold: {confidenceThreshold}%</Label>
              <Slider
                value={confidenceThreshold}
                onValueChange={setConfidenceThreshold}
                min={0}
                max={100}
                step={5}
                className="mt-2"
              />
            </div>
          </CardContent>
        </Card>

        {/* Side-by-Side Data Matching View */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Source A: Invoices */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <FileText className="mr-2 h-5 w-5 text-blue-600" />
                Source A: Invoices
              </CardTitle>
              <CardDescription>
                {filteredInvoices.length} records displayed
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-50 border-y border-gray-200">
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Confidence</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredInvoices.map((invoice) => (
                      <tr
                        key={invoice.id}
                        className={`border-b border-gray-200 hover:bg-gray-50 cursor-pointer ${selectedRecord === invoice.id ? 'bg-blue-50' : ''}`}
                        onClick={() => handleRecordSelect(invoice.id)}
                      >
                        <td className="px-4 py-2 text-sm">{invoice.id}</td>
                        <td className="px-4 py-2 text-sm">{invoice.date}</td>
                        <td className="px-4 py-2 text-sm">${invoice.amount.toFixed(2)}</td>
                        <td className="px-4 py-2 text-sm">{getStatusBadge(invoice.status)}</td>
                        <td className="px-4 py-2 text-sm">{getConfidenceBadge(invoice.confidence)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Source B: Payments */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <DollarSign className="mr-2 h-5 w-5 text-green-600" />
                Source B: Payments
              </CardTitle>
              <CardDescription>
                {filteredPayments.length} records displayed
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-50 border-y border-gray-200">
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Confidence</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredPayments.map((payment) => (
                      <tr
                        key={payment.id}
                        className={`border-b border-gray-200 hover:bg-gray-50 cursor-pointer ${
                          // Highlight the corresponding payment if an invoice is selected
                          selectedRecord && invoiceData.find(inv => inv.id === selectedRecord)?.customer === payment.customer ? 'bg-blue-50' : ''
                        }`}
                      >
                        <td className="px-4 py-2 text-sm">{payment.id}</td>
                        <td className="px-4 py-2 text-sm">{payment.date}</td>
                        <td className="px-4 py-2 text-sm">${payment.amount.toFixed(2)}</td>
                        <td className="px-4 py-2 text-sm">{getStatusBadge(payment.status)}</td>
                        <td className="px-4 py-2 text-sm">{getConfidenceBadge(payment.confidence)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Record Details Panel */}
        {selectedRecord && (
          <Card className="mb-6">
            <CardHeader className="pb-2 flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-lg">Record Details</CardTitle>
                <CardDescription>
                  {selectedRecord}
                </CardDescription>
              </div>
              <div className="flex gap-2">
                {editMode ? (
                  <Button
                    variant="default"
                    size="sm"
                    onClick={handleSaveChanges}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <Save size={16} className="mr-2" />
                    Save Changes
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleEditToggle}
                  >
                    <Edit size={16} className="mr-2" />
                    Edit
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {editMode ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-amount">Amount</Label>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                      <Input
                        id="edit-amount"
                        value={editedAmount}
                        onChange={(e) => setEditedAmount(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="edit-date">Date</Label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                      <Input
                        id="edit-date"
                        type="date"
                        value={editedDate}
                        onChange={(e) => setEditedDate(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div className="md:col-span-2">
                    <Label htmlFor="edit-notes">Notes</Label>
                    <Input
                      id="edit-notes"
                      value={editedNotes}
                      onChange={(e) => setEditedNotes(e.target.value)}
                      placeholder="Add notes about this record..."
                    />
                  </div>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Find the selected invoice */}
                  {(() => {
                    const invoice = invoiceData.find(inv => inv.id === selectedRecord);
                    if (!invoice) return null;

                    // Find the matching payment
                    const payment = paymentData.find(pmt =>
                      pmt.customer === invoice.customer &&
                      (invoice.status !== 'Unmatched')
                    );

                    return (
                      <>
                        <div className="space-y-4">
                          <div>
                            <h3 className="text-sm font-medium text-gray-500">Invoice Details</h3>
                            <div className="mt-2 space-y-2">
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">ID:</span>
                                <span className="text-sm font-medium">{invoice.id}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Date:</span>
                                <span className="text-sm font-medium">{invoice.date}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Amount:</span>
                                <span className="text-sm font-medium">${invoice.amount.toFixed(2)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Customer:</span>
                                <span className="text-sm font-medium">{invoice.customer}</span>
                              </div>
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Status:</span>
                                <span>{getStatusBadge(invoice.status)}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {payment && (
                          <div className="space-y-4">
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Matching Payment</h3>
                              <div className="mt-2 space-y-2">
                                <div className="flex justify-between">
                                  <span className="text-sm text-gray-600">ID:</span>
                                  <span className="text-sm font-medium">{payment.id}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm text-gray-600">Date:</span>
                                  <span className="text-sm font-medium">{payment.date}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm text-gray-600">Amount:</span>
                                  <span className="text-sm font-medium">${payment.amount.toFixed(2)}</span>
                                </div>
                                <div className="flex justify-between">
                                  <span className="text-sm text-gray-600">Customer:</span>
                                  <span className="text-sm font-medium">{payment.customer}</span>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}

                        <div className="md:col-span-2">
                          <h3 className="text-sm font-medium text-gray-500">Match Details</h3>
                          <div className="mt-2 space-y-2">
                            <div className="flex justify-between">
                              <span className="text-sm text-gray-600">Confidence:</span>
                              <span>{getConfidenceBadge(invoice.confidence)}</span>
                            </div>
                            {invoice.status === 'Anomaly' && (
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Issue:</span>
                                <span className="text-sm font-medium text-amber-600">{invoice.notes}</span>
                              </div>
                            )}
                            {invoice.status === 'Unmatched' && (
                              <div className="flex justify-between">
                                <span className="text-sm text-gray-600">Issue:</span>
                                <span className="text-sm font-medium text-red-600">No matching payment found</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </>
                    );
                  })()}
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between">
          <Button variant="outline" onClick={() => navigate('/data-pipeline')}>
            <ArrowLeft size={16} className="mr-2" />
            Back to Pipeline
          </Button>
          <Button
            className="bg-teal hover:bg-teal/90"
            onClick={() => navigate('/reconciliation-summary')}
          >
            Proceed to Summary
            <ArrowRight size={16} className="ml-2" />
          </Button>
        </div>
        </>
        ) : (
        <>
        {/* Advanced Matching UI */}
        <div className="grid grid-cols-1 gap-6">
          <Tabs value={activeTab === 'advanced' ? 'config' : 'results'} onValueChange={(value) => setActiveTab(value === 'config' ? 'advanced' : 'results')}>
            <TabsList className="mb-6">
              <TabsTrigger value="config" className="flex items-center">
                <Settings size={16} className="mr-2" />
                Configuration
              </TabsTrigger>
              <TabsTrigger
                value="results"
                className="flex items-center"
                disabled={!matchingResults}
              >
                <FileCheck size={16} className="mr-2" />
                Results
              </TabsTrigger>
            </TabsList>

            <TabsContent value="config">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Settings size={20} className="mr-2" />
                    Advanced Matching Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure sophisticated matching algorithms
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <MatchingConfigForm
                    onSubmit={handleSubmitMatching}
                    isLoading={isLoading}
                    error={error}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="results">
              {matchingResults ? (
                <MatchingResultsDisplay
                  results={matchingResults}
                  onSave={handleSaveResults}
                  onExport={handleExportResults}
                  isLoading={isLoading}
                />
              ) : (
                <Alert>
                  <AlertTitle>No Results</AlertTitle>
                  <AlertDescription>
                    Configure and run matching to see results.
                  </AlertDescription>
                </Alert>
              )}
            </TabsContent>
          </Tabs>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={() => navigate('/data-cleaning')}
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Data Cleaning
          </Button>

          {matchingResults ? (
            <Button
              className="bg-teal hover:bg-teal/90"
              onClick={handleSaveResults}
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 size={16} className="mr-2 animate-spin" />
              ) : (
                <FileCheck size={16} className="mr-2" />
              )}
              Save and Continue
            </Button>
          ) : (
            <Button
              variant="outline"
              disabled
            >
              Proceed to Summary
              <ArrowRight size={16} className="ml-2" />
            </Button>
          )}
        </div>
        </>
        )}
      </div>
    </div>
  );
};

export default Reconciliation;
