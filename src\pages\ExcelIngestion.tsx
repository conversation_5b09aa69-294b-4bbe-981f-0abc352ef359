import { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Progress } from '../components/ui/progress';
import { useToast } from '../components/ui/use-toast';
import { FileSpreadsheet, Upload, AlertCircle, ArrowRight, Layers } from 'lucide-react';
import fileService, { DataSource } from '../services/excelService';
import SheetSelectionModal from '../components/SheetSelectionModal';

const FileIngestion = () => {
  const [file, setFile] = useState<File | null>(null);
  const [fileName, setFileName] = useState<string>('');
  const [previewData, setPreviewData] = useState<any[] | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [dataSources, setDataSources] = useState<DataSource[]>([]);

  // Sheet selection state
  const [isExcelFile, setIsExcelFile] = useState<boolean>(false);
  const [showSheetModal, setShowSheetModal] = useState<boolean>(false);
  const [sheetNames, setSheetNames] = useState<string[]>([]);
  const [sheetPreviews, setSheetPreviews] = useState<Record<string, any>>({});
  const [defaultSheet, setDefaultSheet] = useState<string>('');
  const [selectedSheet, setSelectedSheet] = useState<string>('');

  const navigate = useNavigate();
  const { toast } = useToast();

  // Fetch existing data sources on component mount
  useEffect(() => {
    const fetchDataSources = async () => {
      try {
        const sources = await fileService.getDataSources();
        setDataSources(sources);
      } catch (error) {
        console.error('Error fetching data sources:', error);
      }
    };

    fetchDataSources();
  }, []);

  const handleFileChange = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (!selectedFile) return;

    // Check if file is an Excel or CSV file
    const validFileTypes = [
      'application/vnd.ms-excel',                                     // .xls
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel.sheet.macroEnabled.12',                // .xlsm
      'text/csv',                                                      // .csv
      'application/csv',                                               // .csv (alternative MIME)
      'text/x-csv',                                                    // .csv (alternative MIME)
      'application/x-csv'                                              // .csv (alternative MIME)
    ];

    // Also check file extension as a fallback
    const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();
    const isValidExtension = ['xls', 'xlsx', 'csv'].includes(fileExtension || '');

    if (!validFileTypes.includes(selectedFile.type) && !isValidExtension) {
      setValidationErrors(['Please upload a valid Excel (.xls or .xlsx) or CSV (.csv) file']);
      setFile(null);
      setPreviewData(null);
      return;
    }

    // Check if it's an Excel file
    const isExcel = fileExtension === 'xlsx' || fileExtension === 'xls';
    setIsExcelFile(isExcel);

    setFile(selectedFile);
    setFileName(selectedFile.name);
    setValidationErrors([]);

    // We'll handle Excel sheet detection during upload instead of here
    if (isExcel) {
      console.log('Excel file detected, will check for multiple sheets during upload');
    }

    // For a real implementation, you might want to read and preview the file here
    // For simplicity, we'll just set a placeholder
    const fileType = fileExtension === 'csv' ? 'CSV' : 'Excel';
    setPreviewData([{ message: `${fileType} file selected. Click "Upload" to process.` }]);
  }, []);

  const handleUpload = async () => {
    if (!file) {
      setValidationErrors(['Please select a file to upload']);
      return;
    }

    // If it's an Excel file with multiple sheets but no sheet is selected, show the sheet selection modal
    if (isExcelFile && sheetNames.length > 1 && !selectedSheet) {
      setShowSheetModal(true);
      return;
    }

    setIsLoading(true);
    setValidationErrors([]);

    try {
      // Set up progress tracking
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            return 90; // Hold at 90% until complete
          }
          return prev + 10;
        });
      }, 300);

      // Upload the file with the selected sheet if it's an Excel file
      const result = await fileService.uploadFile(
        file,
        fileName || undefined,
        isExcelFile ? selectedSheet || undefined : undefined
      );

      // Check if the result indicates multiple sheets that require selection
      if ('requires_sheet_selection' in result && result.requires_sheet_selection) {
        console.log('Multiple sheets detected, showing sheet selection modal');
        // Update state with sheet information
        setSheetNames(result.sheet_names);
        setSheetPreviews(result.previews);
        setDefaultSheet(result.default_sheet);
        setShowSheetModal(true);

        // Clear progress and loading state
        clearInterval(interval);
        setUploadProgress(0);
        setIsLoading(false);
        return;
      }

      // If we get here, result is a DataSource
      const dataSource = result as DataSource;

      // Verify that we have a valid data source with an ID
      if (!dataSource || !dataSource.id) {
        console.error('Invalid data source response:', dataSource);
        throw new Error('Invalid data source response from server');
      }

      // Complete the progress
      clearInterval(interval);
      setUploadProgress(100);

      toast({
        title: "File uploaded successfully",
        description: `Processed ${dataSource.stats.cleaned_rows} rows of data.`,
      });

      // Navigate to the data analysis page after a short delay
      setTimeout(() => {
        navigate(`/excel-analysis/${dataSource.id}`);
      }, 1000);
    } catch (error) {
      console.error('Error during Excel upload:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setValidationErrors([`Error during upload: ${errorMessage}`]);
      console.error('Upload error details:', error);
      setUploadProgress(0);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectSheet = (sheetName: string) => {
    setSelectedSheet(sheetName);
    setShowSheetModal(false);
  };

  const handleSelectExisting = (dataSource: DataSource) => {
    navigate(`/excel-analysis/${dataSource.id}`);
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Data Ingestion</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <Card className="mb-6">
            <CardContent className="pt-6">
              <h2 className="text-xl font-semibold mb-4">Upload New File</h2>

              <div className="mb-4">
                <Label htmlFor="file-upload">Select Excel or CSV File</Label>
                <Input
                  id="file-upload"
                  type="file"
                  accept=".xls,.xlsx,.csv"
                  onChange={handleFileChange}
                  disabled={isLoading}
                  className="mt-1"
                />
              </div>

              {file && (
                <div className="mb-4">
                  <Label htmlFor="file-name">File Name (Optional)</Label>
                  <Input
                    id="file-name"
                    value={fileName}
                    onChange={(e) => setFileName(e.target.value)}
                    disabled={isLoading}
                    placeholder="Enter a name for this file"
                    className="mt-1"
                  />
                </div>
              )}

              {validationErrors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                  <div className="flex items-center text-red-800 mb-1">
                    <AlertCircle size={16} className="mr-2" />
                    <span className="font-medium">Validation errors:</span>
                  </div>
                  <ul className="list-disc pl-5 text-sm text-red-700">
                    {validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {uploadProgress > 0 && (
                <div className="mb-4">
                  <Label className="block mb-1">Upload Progress</Label>
                  <Progress value={uploadProgress} className="h-2" />
                  <p className="text-sm text-gray-500 mt-1">{uploadProgress}% complete</p>
                </div>
              )}

              <Button
                onClick={handleUpload}
                disabled={!file || isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>Processing...</>
                ) : (
                  <>
                    <Upload size={16} className="mr-2" />
                    Upload and Process
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {previewData && (
            <Card>
              <CardContent className="pt-6">
                <h2 className="text-xl font-semibold mb-4">File Preview</h2>
                <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                  <div className="flex items-center mb-2">
                    <FileSpreadsheet size={20} className="mr-2 text-green-600" />
                    <span className="font-medium">{file?.name}</span>
                  </div>
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm text-gray-600">
                      {file && `Size: ${(file.size / 1024).toFixed(2)} KB`}
                    </p>
                    {isExcelFile && selectedSheet && (
                      <div className="flex items-center">
                        <Layers size={16} className="mr-1 text-blue-500" />
                        <span className="text-sm text-blue-600">Selected sheet: <strong>{selectedSheet}</strong></span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <div>
          <Card>
            <CardContent className="pt-6">
              <h2 className="text-xl font-semibold mb-4">Previously Uploaded Files</h2>

              {dataSources.length === 0 ? (
                <p className="text-gray-500">No files have been uploaded yet.</p>
              ) : (
                <div className="space-y-3">
                  {dataSources.map((dataSource) => (
                    <div
                      key={dataSource.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200 hover:bg-gray-100 cursor-pointer"
                      onClick={() => handleSelectExisting(dataSource)}
                    >
                      <div className="flex items-center">
                        <FileSpreadsheet size={20} className="mr-3 text-blue-600" />
                        <div>
                          <p className="font-medium">{dataSource.name}</p>
                          <p className="text-sm text-gray-500">
                            {new Date(dataSource.created_at).toLocaleString()}
                            {dataSource.stats && ` • ${dataSource.stats.cleaned_rows} rows`}
                          </p>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        <ArrowRight size={16} />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Sheet Selection Modal */}
      <SheetSelectionModal
        isOpen={showSheetModal}
        onClose={() => setShowSheetModal(false)}
        onSelectSheet={handleSelectSheet}
        sheetNames={sheetNames}
        sheetPreviews={sheetPreviews}
        defaultSheet={defaultSheet}
      />
    </div>
  );
};

export default FileIngestion;
