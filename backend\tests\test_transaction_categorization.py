import unittest
from unittest.mock import patch, MagicMock

import pandas as pd

from app.services.transaction_categorization import (
    categorize_transactions,
    extract_description_text,
    extract_amount_from_row,
    categorize_single_transaction,
    create_simple_category_clusters,
    DEFAULT_CATEGORIES,
    DEFAULT_KEYWORD_MAP
)


class TestTransactionCategorization(unittest.TestCase):
    def setUp(self):
        
        # Sample transaction data
        self.sample_data = [
            {
                "description": "ZOOM subscription payment",
                "amount": -29.99,
                "date": "2023-01-15"
            },
            {
                "description": "Client payment for services",
                "amount": 1500.00,
                "date": "2023-01-16"
            },
            {
                "description": "Office supplies purchase",
                "amount": -45.50,
                "date": "2023-01-17"
            },
            {
                "description": "Marketing campaign cost",
                "amount": -200.00,
                "date": "2023-01-18"
            },
            {
                "description": "Bank transfer fee",
                "amount": -5.00,
                "date": "2023-01-19"
            }
        ]
        
        # Configuration for categorization
        self.config = {
            "categories": DEFAULT_CATEGORIES,
            "min_cluster_size": 5,
            "min_samples": 2,
            "max_features": 10000
        }
        
        # Mock source ID
        self.source_id = "test-source-id"
    
    def test_extract_description_text(self):
        """Test extraction of description text from transaction data"""
        # Convert sample data to DataFrame
        df = pd.DataFrame(self.sample_data)
        
        # Extract description text
        description_texts = extract_description_text(df)
        
        # Check that description texts were extracted
        self.assertEqual(len(description_texts), len(df))
        self.assertTrue(all(isinstance(text, str) for text in description_texts))
        self.assertEqual(description_texts[0], "ZOOM subscription payment")
    
    def test_extract_amount_from_row(self):
        """Test extraction of amount from transaction row"""
        # Convert sample data to DataFrame
        df = pd.DataFrame(self.sample_data)
        
        # Extract amount from first row
        amount = extract_amount_from_row(df.iloc[0])
        
        # Check that amount was extracted
        self.assertIsInstance(amount, float)
        self.assertEqual(amount, -29.99)
    
    def test_categorize_single_transaction_keyword_match(self):
        """Test categorization of a single transaction with keyword matching"""
        # Test keyword matching
        category = categorize_single_transaction(
            "ZOOM subscription payment", 
            -29.99, 
            {}, 
            DEFAULT_KEYWORD_MAP, 
            DEFAULT_CATEGORIES
        )
        self.assertEqual(category, "Expense - Software")
    
    def test_categorize_single_transaction_user_label(self):
        """Test categorization with user label matching"""
        # Test user label matching
        user_labels = {"Custom transaction": "Expense - Office"}
        category = categorize_single_transaction(
            "Custom transaction", 
            -50.0, 
            user_labels, 
            DEFAULT_KEYWORD_MAP, 
            DEFAULT_CATEGORIES
        )
        self.assertEqual(category, "Expense - Office")
    
    @patch('app.services.transaction_categorization.get_llm')
    def test_categorize_single_transaction_ai_fallback(self, mock_get_llm):
        """Test categorization with AI fallback"""
        # Mock LLM response
        mock_llm = MagicMock()
        mock_llm.generate.return_value = "Expense - Other"
        mock_get_llm.return_value = mock_llm
        
        # Test AI fallback for unknown transaction
        category = categorize_single_transaction(
            "Unknown transaction type", 
            -100.0, 
            {}, 
            {}, 
            DEFAULT_CATEGORIES
        )
        self.assertEqual(category, "Expense - Other")
    
    def test_create_simple_category_clusters(self):
        """Test creation of simple category clusters"""
        # Create mock categorized transactions
        categorized_transactions = [
            {"category": "Expense - Software", "description_text": "ZOOM", "amount": -29.99, "date": "2023-01-15"},
            {"category": "Expense - Software", "description_text": "Adobe", "amount": -19.99, "date": "2023-01-16"},
            {"category": "Income - Fees", "description_text": "Client payment", "amount": 500.0, "date": "2023-01-17"}
        ]
        
        category_counts = {"Expense - Software": 2, "Income - Fees": 1}
        category_totals = {"Expense - Software": -49.98, "Income - Fees": 500.0}
        
        # Create category clusters
        clusters = create_simple_category_clusters(
            self.source_id, categorized_transactions, category_counts, category_totals
        )
        
        # Check that clusters were created
        self.assertEqual(len(clusters), 2)
        for cluster in clusters:
            self.assertIn("id", cluster)
            self.assertIn("label", cluster)
            self.assertIn("row_count", cluster)
            self.assertIn("amount_total", cluster)
            self.assertIn("is_income", cluster)
            self.assertIn("confidence", cluster)
            self.assertIn("sample_rows", cluster)
        
        # Check specific cluster properties
        software_cluster = next(c for c in clusters if c["label"] == "Expense - Software")
        self.assertEqual(software_cluster["row_count"], 2)
        self.assertEqual(software_cluster["amount_total"], -49.98)
        self.assertFalse(software_cluster["is_income"])
        
        income_cluster = next(c for c in clusters if c["label"] == "Income - Fees")
        self.assertEqual(income_cluster["row_count"], 1)
        self.assertEqual(income_cluster["amount_total"], 500.0)
        self.assertTrue(income_cluster["is_income"])
    
    @patch('app.services.transaction_categorization.save_user_labels')
    @patch('app.services.transaction_categorization.load_user_labels')
    def test_categorize_transactions(self, mock_load_labels, mock_save_labels):
        """Test the full simple categorization pipeline"""
        # Mock user labels
        mock_load_labels.return_value = {}
        
        # Categorize transactions
        result = categorize_transactions(self.source_id, self.sample_data, self.config)
        
        # Check that categorization was successful
        self.assertIn("total_rows", result)
        self.assertIn("clustered_rows", result)
        self.assertIn("cluster_count", result)
        self.assertIn("clusters", result)
        self.assertEqual(result["total_rows"], len(self.sample_data))
        self.assertTrue(result["cluster_count"] > 0)
        self.assertTrue(len(result["clusters"]) > 0)
        
        # Check that clusters have the expected structure
        for cluster in result["clusters"]:
            self.assertIn("id", cluster)
            self.assertIn("label", cluster)
            self.assertIn("row_count", cluster)
            self.assertIn("amount_total", cluster)
            self.assertIn("confidence", cluster)
            self.assertIn("sample_rows", cluster)
        
        # Verify that save_user_labels was called
        mock_save_labels.assert_called_once()
    
    def test_categorize_transactions_empty_data(self):
        """Test categorization with empty data"""
        with self.assertRaises(ValueError):
            categorize_transactions(self.source_id, [], self.config)
    
    def test_categorize_transactions_invalid_data(self):
        """Test categorization with invalid data"""
        with self.assertRaises(ValueError):
            categorize_transactions(self.source_id, "not a list", self.config)


if __name__ == "__main__":
    unittest.main()
