
import React from 'react';
import { 
  Database, 
  ChartBar, 
  ArrowRight, 
  <PERSON><PERSON>heck,
  Settings,
  BarChart3
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Link } from 'react-router-dom';
import CallToAction from '@/components/CallToAction';

const Solution = () => {
  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="section-padding bg-gradient-to-br from-navy to-[#051b38] text-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6">
                Our Solution
              </h1>
              <p className="text-xl opacity-90 mb-8 leading-relaxed">
                DataOracle provides an AI-driven data cleanup and forecasting platform that transforms how financial institutions
                handle their data, turning fragmented information into accurate, actionable insights.
              </p>
              <Link to="/contact">
                <Button size="lg" className="bg-teal hover:bg-teal/90 text-navy font-semibold">
                  Schedule a Demo
                </Button>
              </Link>
            </div>
            <div className="hidden md:block">
              <img 
                src="https://images.unsplash.com/photo-1531297484001-80022131f5a1?w=800&h=600&fit=crop" 
                alt="Data analysis dashboard" 
                className="rounded-xl shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Key Components Section */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-navy">Key Components</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our comprehensive solution addresses the most challenging aspects of financial data management.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <div className="h-12 w-12 bg-lightgray rounded-lg flex items-center justify-center text-navy mb-2">
                  <Database size={24} />
                </div>
                <CardTitle>Data Ingestion</CardTitle>
                <CardDescription>Connect and import from multiple sources</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>CSV and Excel file import</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Direct database connections</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>API integration with CRM and core banking systems</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Secure data pipelines with encryption</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="h-12 w-12 bg-lightgray rounded-lg flex items-center justify-center text-navy mb-2">
                  <FileCheck size={24} />
                </div>
                <CardTitle>AI-Based Cleaning & Classification</CardTitle>
                <CardDescription>Automated data preparation and validation</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>LLM-based field identification and mapping</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Anomaly and inconsistency detection</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Data standardization and normalization</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Confidence scoring for data quality</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="h-12 w-12 bg-lightgray rounded-lg flex items-center justify-center text-navy mb-2">
                  <BarChart3 size={24} />
                </div>
                <CardTitle>Forecasting Engine</CardTitle>
                <CardDescription>Predictive modeling for financial metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Monthly principal and interest projections</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Default risk and prepayment modeling</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Fee income and revenue forecasting</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Cohort and account-level analysis</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="h-12 w-12 bg-lightgray rounded-lg flex items-center justify-center text-navy mb-2">
                  <Settings size={24} />
                </div>
                <CardTitle>Scenario Testing</CardTitle>
                <CardDescription>Evaluate different business scenarios</CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Interest rate change simulations</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Default rate sensitivity analysis</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Growth modeling and capacity planning</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="h-5 w-5 rounded-full bg-teal/20 flex items-center justify-center">
                      <ArrowRight size={12} className="text-teal" />
                    </div>
                    <span>Regulatory stress testing support</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
      
      {/* Technical Architecture Section */}
      <section className="section-padding bg-lightgray">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-navy">Technical Architecture</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our platform is designed for security, scalability, and ease of deployment in your environment.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl font-bold mb-6 text-navy">Flexible Deployment Options</h3>
              <ul className="space-y-6">
                <li className="flex items-start gap-4">
                  <div className="h-8 w-8 rounded-full bg-teal text-white flex items-center justify-center font-bold shrink-0">
                    1
                  </div>
                  <div>
                    <h4 className="font-semibold text-navy">On-Premises Deployment</h4>
                    <p className="text-gray-600">
                      Run the platform entirely within your secure environment, keeping sensitive financial data behind your firewall.
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-4">
                  <div className="h-8 w-8 rounded-full bg-teal text-white flex items-center justify-center font-bold shrink-0">
                    2
                  </div>
                  <div>
                    <h4 className="font-semibold text-navy">Containerized Implementation</h4>
                    <p className="text-gray-600">
                      Docker and Kubernetes support for efficient resource utilization and simplified scaling as your needs grow.
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-4">
                  <div className="h-8 w-8 rounded-full bg-teal text-white flex items-center justify-center font-bold shrink-0">
                    3
                  </div>
                  <div>
                    <h4 className="font-semibold text-navy">Secure Cloud Options</h4>
                    <p className="text-gray-600">
                      For institutions comfortable with cloud deployment, we offer secure, compliant cloud hosting with full encryption.
                    </p>
                  </div>
                </li>
                <li className="flex items-start gap-4">
                  <div className="h-8 w-8 rounded-full bg-teal text-white flex items-center justify-center font-bold shrink-0">
                    4
                  </div>
                  <div>
                    <h4 className="font-semibold text-navy">API-First Architecture</h4>
                    <p className="text-gray-600">
                      RESTful API interface for seamless integration with existing systems and workflows.
                    </p>
                  </div>
                </li>
              </ul>
            </div>
            
            <div className="bg-white p-6 rounded-xl shadow-md">
              <img 
                src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?w=800&h=600&fit=crop" 
                alt="Technical architecture diagram" 
                className="rounded-lg shadow-sm"
              />
            </div>
          </div>
        </div>
      </section>
      
      <CallToAction 
        title="Ready to see our solution in action?"
        description="Schedule a personalized demo to see how our AI-driven platform can transform your financial data operations."
      />
    </div>
  );
};

export default Solution;
