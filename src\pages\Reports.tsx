import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ArrowLeft, 
  ArrowRight, 
  Save, 
  Download, 
  BarChart2, 
  PieChart,
  LineChart,
  Table,
  Plus,
  Trash2,
  RefreshCw,
  MessageSquare
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

// Mock data for report fields
const availableFields = [
  { id: 'invoice_id', name: 'Invoice ID', category: 'identification' },
  { id: 'payment_id', name: 'Payment ID', category: 'identification' },
  { id: 'date', name: 'Date', category: 'date' },
  { id: 'due_date', name: 'Due Date', category: 'date' },
  { id: 'amount', name: 'Amount', category: 'financial' },
  { id: 'customer', name: 'Customer', category: 'entity' },
  { id: 'status', name: 'Status', category: 'status' },
  { id: 'payment_method', name: 'Payment Method', category: 'payment' },
  { id: 'currency', name: 'Currency', category: 'financial' },
  { id: 'department', name: 'Department', category: 'organization' },
  { id: 'region', name: 'Region', category: 'geography' },
  { id: 'product', name: 'Product', category: 'product' },
];

// Mock data for report templates
const reportTemplates = [
  { id: 'profit_loss', name: 'Profit & Loss', description: 'Monthly P&L statement with revenue and expenses' },
  { id: 'sales_by_month', name: 'Sales by Month', description: 'Monthly sales trends with year-over-year comparison' },
  { id: 'customer_aging', name: 'Customer Aging', description: 'Aging analysis of customer receivables' },
  { id: 'regional_performance', name: 'Regional Performance', description: 'Sales and profitability by geographic region' },
];

// Mock data for saved reports
const savedReports = [
  { id: 1, name: 'Monthly Reconciliation Q2', created: '2023-09-15', type: 'Pivot Table' },
  { id: 2, name: 'Sales by Region', created: '2023-09-10', type: 'Bar Chart' },
  { id: 3, name: 'Customer Payment Analysis', created: '2023-09-05', type: 'Pivot Table' },
];

const Reports = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('builder');
  const [reportName, setReportName] = useState('');
  const [rowFields, setRowFields] = useState<string[]>(['customer']);
  const [columnFields, setColumnFields] = useState<string[]>(['date']);
  const [valueFields, setValueFields] = useState<string[]>(['amount']);
  const [aggregation, setAggregation] = useState('sum');
  const [isGenerating, setIsGenerating] = useState(false);
  const [reportGenerated, setReportGenerated] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [nlQuery, setNlQuery] = useState('');
  const [queryResult, setQueryResult] = useState<string | null>(null);

  const handleAddRowField = (field: string) => {
    if (!rowFields.includes(field)) {
      setRowFields([...rowFields, field]);
    }
  };

  const handleRemoveRowField = (field: string) => {
    setRowFields(rowFields.filter(f => f !== field));
  };

  const handleAddColumnField = (field: string) => {
    if (!columnFields.includes(field)) {
      setColumnFields([...columnFields, field]);
    }
  };

  const handleRemoveColumnField = (field: string) => {
    setColumnFields(columnFields.filter(f => f !== field));
  };

  const handleAddValueField = (field: string) => {
    if (!valueFields.includes(field)) {
      setValueFields([...valueFields, field]);
    }
  };

  const handleRemoveValueField = (field: string) => {
    setValueFields(valueFields.filter(f => f !== field));
  };

  const handleGenerateReport = () => {
    setIsGenerating(true);
    
    // Simulate report generation
    setTimeout(() => {
      setIsGenerating(false);
      setReportGenerated(true);
    }, 2000);
  };

  const handleSaveReport = () => {
    // In a real app, this would save the report configuration
    alert('Report saved successfully!');
  };

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    
    // Set predefined configurations based on template
    if (templateId === 'profit_loss') {
      setRowFields(['department']);
      setColumnFields(['date']);
      setValueFields(['amount']);
      setAggregation('sum');
      setReportName('Profit & Loss Statement');
    } else if (templateId === 'sales_by_month') {
      setRowFields(['product']);
      setColumnFields(['date']);
      setValueFields(['amount']);
      setAggregation('sum');
      setReportName('Sales by Month');
    } else if (templateId === 'customer_aging') {
      setRowFields(['customer']);
      setColumnFields(['status']);
      setValueFields(['amount']);
      setAggregation('sum');
      setReportName('Customer Aging Report');
    } else if (templateId === 'regional_performance') {
      setRowFields(['region']);
      setColumnFields(['date']);
      setValueFields(['amount']);
      setAggregation('sum');
      setReportName('Regional Performance');
    }
  };

  const handleNaturalLanguageQuery = () => {
    if (!nlQuery) return;
    
    setIsGenerating(true);
    
    // Simulate query processing
    setTimeout(() => {
      setIsGenerating(false);
      
      // Mock response based on query
      if (nlQuery.toLowerCase().includes('total fees')) {
        setQueryResult('The total fees for the last quarter across all matched transactions is $42,850.75');
      } else if (nlQuery.toLowerCase().includes('customer')) {
        setQueryResult('Top 3 customers by transaction volume: 1. Acme Corp ($25,420), 2. Globex Inc ($18,750), 3. Stark Industries ($15,300)');
      } else if (nlQuery.toLowerCase().includes('region')) {
        setQueryResult('Sales by region: North America: $125,400, Europe: $98,750, Asia: $76,300, Other: $42,550');
      } else {
        setQueryResult('I found the following insights: Total transactions: 1,240, Average transaction value: $1,850, Highest value transaction: $12,500 (Acme Corp)');
      }
    }, 1500);
  };

  const getFieldBadge = (field: string) => {
    const fieldObj = availableFields.find(f => f.id === field);
    if (!fieldObj) return null;
    
    return (
      <Badge className="flex items-center gap-1 bg-blue-100 text-blue-800 hover:bg-blue-100">
        {fieldObj.name}
        <button 
          className="ml-1 text-blue-600 hover:text-blue-800"
          onClick={(e) => {
            e.stopPropagation();
            if (rowFields.includes(field)) {
              handleRemoveRowField(field);
            } else if (columnFields.includes(field)) {
              handleRemoveColumnField(field);
            } else if (valueFields.includes(field)) {
              handleRemoveValueField(field);
            }
          }}
        >
          <Trash2 size={12} />
        </button>
      </Badge>
    );
  };

  return (
    <div className="pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center mb-8">
          <Button 
            variant="ghost" 
            size="sm" 
            className="mr-4"
            onClick={() => navigate('/data-pipeline')}
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Pipeline
          </Button>
          <h1 className="text-3xl font-bold text-navy">Report Configuration</h1>
        </div>

        <Tabs defaultValue="builder" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="builder" className="flex items-center gap-2">
              <Table size={16} />
              <span>Report Builder</span>
            </TabsTrigger>
            <TabsTrigger value="templates" className="flex items-center gap-2">
              <BarChart2 size={16} />
              <span>Templates</span>
            </TabsTrigger>
            <TabsTrigger value="nlq" className="flex items-center gap-2">
              <MessageSquare size={16} />
              <span>Natural Language Query</span>
            </TabsTrigger>
          </TabsList>

          {/* Report Builder Tab */}
          <TabsContent value="builder" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Left Panel: Available Fields */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Available Fields</CardTitle>
                  <CardDescription>
                    Drag fields to rows, columns, or values
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Input 
                      placeholder="Search fields..." 
                      className="mb-2"
                    />
                    
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Identification</h3>
                      <div className="flex flex-wrap gap-2">
                        {availableFields
                          .filter(field => field.category === 'identification')
                          .map(field => (
                            <Badge 
                              key={field.id}
                              className="cursor-pointer bg-gray-100 text-gray-800 hover:bg-gray-200"
                              onClick={() => handleAddRowField(field.id)}
                            >
                              {field.name}
                            </Badge>
                          ))
                        }
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Date</h3>
                      <div className="flex flex-wrap gap-2">
                        {availableFields
                          .filter(field => field.category === 'date')
                          .map(field => (
                            <Badge 
                              key={field.id}
                              className="cursor-pointer bg-gray-100 text-gray-800 hover:bg-gray-200"
                              onClick={() => handleAddColumnField(field.id)}
                            >
                              {field.name}
                            </Badge>
                          ))
                        }
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Financial</h3>
                      <div className="flex flex-wrap gap-2">
                        {availableFields
                          .filter(field => field.category === 'financial')
                          .map(field => (
                            <Badge 
                              key={field.id}
                              className="cursor-pointer bg-gray-100 text-gray-800 hover:bg-gray-200"
                              onClick={() => handleAddValueField(field.id)}
                            >
                              {field.name}
                            </Badge>
                          ))
                        }
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium text-gray-500">Other</h3>
                      <div className="flex flex-wrap gap-2">
                        {availableFields
                          .filter(field => !['identification', 'date', 'financial'].includes(field.category))
                          .map(field => (
                            <Badge 
                              key={field.id}
                              className="cursor-pointer bg-gray-100 text-gray-800 hover:bg-gray-200"
                              onClick={() => handleAddRowField(field.id)}
                            >
                              {field.name}
                            </Badge>
                          ))
                        }
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Middle Panel: Report Configuration */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Report Configuration</CardTitle>
                  <CardDescription>
                    Configure your report layout
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="report-name">Report Name</Label>
                      <Input 
                        id="report-name" 
                        placeholder="Enter report name" 
                        value={reportName}
                        onChange={(e) => setReportName(e.target.value)}
                      />
                    </div>
                    
                    <div>
                      <Label>Rows</Label>
                      <div className="flex flex-wrap gap-2 p-2 border rounded-md min-h-[40px]">
                        {rowFields.map(field => getFieldBadge(field))}
                      </div>
                    </div>
                    
                    <div>
                      <Label>Columns</Label>
                      <div className="flex flex-wrap gap-2 p-2 border rounded-md min-h-[40px]">
                        {columnFields.map(field => getFieldBadge(field))}
                      </div>
                    </div>
                    
                    <div>
                      <Label>Values</Label>
                      <div className="flex flex-wrap gap-2 p-2 border rounded-md min-h-[40px]">
                        {valueFields.map(field => getFieldBadge(field))}
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="aggregation">Aggregation</Label>
                      <Select value={aggregation} onValueChange={setAggregation}>
                        <SelectTrigger id="aggregation">
                          <SelectValue placeholder="Select aggregation" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="sum">Sum</SelectItem>
                          <SelectItem value="avg">Average</SelectItem>
                          <SelectItem value="min">Minimum</SelectItem>
                          <SelectItem value="max">Maximum</SelectItem>
                          <SelectItem value="count">Count</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Right Panel: Preview */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Preview</CardTitle>
                  <CardDescription>
                    {reportGenerated ? 'Generated report preview' : 'Generate to see preview'}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isGenerating ? (
                    <div className="flex flex-col items-center justify-center h-64">
                      <RefreshCw size={32} className="animate-spin text-teal mb-4" />
                      <p className="text-gray-500">Generating report...</p>
                    </div>
                  ) : reportGenerated ? (
                    <div className="space-y-4">
                      <div className="border rounded-md overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {availableFields.find(f => f.id === rowFields[0])?.name || 'Row'}
                              </th>
                              {['Jan', 'Feb', 'Mar'].map(month => (
                                <th key={month} className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  {month}
                                </th>
                              ))}
                              <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Total
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            <tr>
                              <td className="px-4 py-2 text-sm font-medium">Acme Corp</td>
                              <td className="px-4 py-2 text-sm">$8,500</td>
                              <td className="px-4 py-2 text-sm">$7,200</td>
                              <td className="px-4 py-2 text-sm">$9,720</td>
                              <td className="px-4 py-2 text-sm font-medium">$25,420</td>
                            </tr>
                            <tr>
                              <td className="px-4 py-2 text-sm font-medium">Globex Inc</td>
                              <td className="px-4 py-2 text-sm">$6,300</td>
                              <td className="px-4 py-2 text-sm">$5,800</td>
                              <td className="px-4 py-2 text-sm">$6,650</td>
                              <td className="px-4 py-2 text-sm font-medium">$18,750</td>
                            </tr>
                            <tr>
                              <td className="px-4 py-2 text-sm font-medium">Stark Industries</td>
                              <td className="px-4 py-2 text-sm">$4,200</td>
                              <td className="px-4 py-2 text-sm">$5,500</td>
                              <td className="px-4 py-2 text-sm">$5,600</td>
                              <td className="px-4 py-2 text-sm font-medium">$15,300</td>
                            </tr>
                            <tr className="bg-gray-50">
                              <td className="px-4 py-2 text-sm font-medium">Total</td>
                              <td className="px-4 py-2 text-sm font-medium">$19,000</td>
                              <td className="px-4 py-2 text-sm font-medium">$18,500</td>
                              <td className="px-4 py-2 text-sm font-medium">$21,970</td>
                              <td className="px-4 py-2 text-sm font-medium">$59,470</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                      
                      <div className="flex justify-between">
                        <Button variant="outline" size="sm" className="flex items-center gap-1">
                          <Download size={14} />
                          Excel
                        </Button>
                        <Button variant="outline" size="sm" className="flex items-center gap-1">
                          <Save size={14} />
                          Save Report
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-64 text-center">
                      <BarChart2 size={48} className="text-gray-300 mb-4" />
                      <p className="text-gray-500 mb-2">No preview available</p>
                      <p className="text-sm text-gray-400">Configure your report and click Generate to see a preview</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-between">
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setActiveTab('templates')}>
                  Load Template
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleSaveReport}
                  disabled={!reportName || rowFields.length === 0 || valueFields.length === 0}
                >
                  <Save size={16} className="mr-2" />
                  Save Configuration
                </Button>
              </div>
              <Button 
                className="bg-teal hover:bg-teal/90"
                onClick={handleGenerateReport}
                disabled={isGenerating || rowFields.length === 0 || valueFields.length === 0}
              >
                {isGenerating ? (
                  <>
                    <RefreshCw size={16} className="mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    Generate Report
                    <ArrowRight size={16} className="ml-2" />
                  </>
                )}
              </Button>
            </div>
          </TabsContent>

          {/* Templates Tab */}
          <TabsContent value="templates" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {reportTemplates.map(template => (
                <Card 
                  key={template.id}
                  className={`cursor-pointer hover:border-teal transition-colors ${selectedTemplate === template.id ? 'border-teal bg-teal/5' : ''}`}
                  onClick={() => handleTemplateSelect(template.id)}
                >
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg">{template.name}</CardTitle>
                    <CardDescription>{template.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-end">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleTemplateSelect(template.id);
                          setActiveTab('builder');
                        }}
                      >
                        Use Template
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Separator />

            <div className="space-y-4">
              <h3 className="text-lg font-medium">Saved Reports</h3>
              <div className="border rounded-md overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {savedReports.map(report => (
                      <tr key={report.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">{report.name}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{report.created}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{report.type}</td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div className="flex gap-2">
                            <Button variant="ghost" size="sm">Edit</Button>
                            <Button variant="ghost" size="sm">Run</Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </TabsContent>

          {/* Natural Language Query Tab */}
          <TabsContent value="nlq" className="space-y-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Natural Language Query</CardTitle>
                <CardDescription>
                  Ask questions about your data in plain English
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex gap-2">
                    <Input 
                      placeholder="e.g., Show me total fees for the last quarter across all matched transactions" 
                      value={nlQuery}
                      onChange={(e) => setNlQuery(e.target.value)}
                      className="flex-grow"
                    />
                    <Button 
                      onClick={handleNaturalLanguageQuery}
                      disabled={isGenerating || !nlQuery}
                    >
                      {isGenerating ? (
                        <RefreshCw size={16} className="animate-spin" />
                      ) : (
                        'Ask'
                      )}
                    </Button>
                  </div>
                  
                  <div className="text-sm text-gray-500">
                    <p>Example questions:</p>
                    <ul className="list-disc pl-5 space-y-1 mt-1">
                      <li>Show me total fees for the last quarter across all matched transactions</li>
                      <li>Who are my top 3 customers by transaction volume?</li>
                      <li>What's the breakdown of sales by region?</li>
                    </ul>
                  </div>
                  
                  {queryResult && (
                    <div className="mt-6">
                      <h3 className="text-base font-medium mb-2">Result</h3>
                      <Card>
                        <CardContent className="p-4">
                          <div className="flex items-start gap-3">
                            <MessageSquare className="text-teal mt-0.5 shrink-0" size={20} />
                            <div>
                              <p className="text-gray-800">{queryResult}</p>
                              <div className="flex gap-2 mt-4">
                                <Button variant="outline" size="sm">
                                  View in Dashboard
                                </Button>
                                <Button variant="outline" size="sm">
                                  Save as Report
                                </Button>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Reports;
