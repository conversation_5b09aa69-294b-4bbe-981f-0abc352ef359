import logging
from typing import Any, Dict, List, Optional, Union

import pandas as pd
from google.cloud import bigquery
from google.cloud.exceptions import NotFound

from app.core.config import settings

logger = logging.getLogger(__name__)


class BigQueryClient:
    """Client for interacting with Google BigQuery."""
    
    def __init__(self):
        self.client = bigquery.Client(project=settings.GCP_PROJECT_ID)
        self.dataset_id = settings.GCP_BIGQUERY_DATASET
    
    def create_table_from_dataframe(
        self, 
        df: pd.DataFrame, 
        table_name: str, 
        schema: Optional[List[bigquery.SchemaField]] = None
    ) -> None:
        """
        Create a BigQuery table from a pandas DataFrame.
        
        Args:
            df: DataFrame to upload
            table_name: Name of the table to create
            schema: Optional schema for the table
        """
        table_id = f"{settings.GCP_PROJECT_ID}.{self.dataset_id}.{table_name}"
        job_config = bigquery.LoadJobConfig(schema=schema)
        
        job = self.client.load_table_from_dataframe(df, table_id, job_config=job_config)
        job.result()  # Wait for the job to complete
        
        logger.info(f"Loaded {len(df)} rows into {table_id}")
    
    def execute_query(self, query: str) -> pd.DataFrame:
        """
        Execute a BigQuery SQL query and return results as a DataFrame.
        
        Args:
            query: SQL query to execute
            
        Returns:
            DataFrame with query results
        """
        query_job = self.client.query(query)
        results = query_job.result()
        return results.to_dataframe()
    
    def table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists in the dataset.
        
        Args:
            table_name: Name of the table to check
            
        Returns:
            True if the table exists, False otherwise
        """
        table_id = f"{settings.GCP_PROJECT_ID}.{self.dataset_id}.{table_name}"
        try:
            self.client.get_table(table_id)
            return True
        except NotFound:
            return False
    
    def delete_table(self, table_name: str) -> None:
        """
        Delete a table from the dataset.
        
        Args:
            table_name: Name of the table to delete
        """
        table_id = f"{settings.GCP_PROJECT_ID}.{self.dataset_id}.{table_name}"
        self.client.delete_table(table_id)
        logger.info(f"Table {table_name} deleted")
    
    def create_view(self, view_name: str, query: str) -> None:
        """
        Create a view in the dataset.
        
        Args:
            view_name: Name of the view to create
            query: SQL query for the view
        """
        view_id = f"{settings.GCP_PROJECT_ID}.{self.dataset_id}.{view_name}"
        view = bigquery.Table(view_id)
        view.view_query = query
        
        self.client.create_table(view)
        logger.info(f"View {view_name} created")


# Create a singleton instance
bigquery_client = BigQueryClient()
