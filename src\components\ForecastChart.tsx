import React, { useState, useEffect } from 'react';

// Demo data for the forecasting chart
const data = [
  { month: 'Jan', principal: 10000000, interest: 400000, risk: 2 },
  { month: 'Feb', principal: 9900000, interest: 396000, risk: 2.1 },
  { month: 'Mar', principal: 9800000, interest: 392000, risk: 2.2 },
  { month: 'Apr', principal: 9750000, interest: 390000, risk: 2.3 },
  { month: 'May', principal: 9800000, interest: 392000, risk: 2.1 },
  { month: 'Jun', principal: 9850000, interest: 394000, risk: 2.0 },
  { month: 'Jul', principal: 9900000, interest: 396000, risk: 2.2 },
  { month: 'Aug', principal: 9950000, interest: 398000, risk: 2.3 },
  { month: 'Sep', principal: 10000000, interest: 400000, risk: 2.4 },
  { month: 'Oct', principal: 10050000, interest: 402000, risk: 2.3 },
  { month: 'Nov', principal: 10100000, interest: 404000, risk: 2.2 },
  { month: 'Dec', principal: 10150000, interest: 406000, risk: 2.1 },
];

// Format large numbers with commas
const formatNumber = (value: number) => {
  return new Intl.NumberFormat('en-US').format(value);
};

const ForecastChart = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [Chart, setChart] = useState<any>(null);

  useEffect(() => {
    const loadRecharts = async () => {
      try {
        const recharts = await import('recharts');
        setChart({
          LineChart: recharts.LineChart,
          Line: recharts.Line,
          XAxis: recharts.XAxis,
          YAxis: recharts.YAxis,
          CartesianGrid: recharts.CartesianGrid,
          Tooltip: recharts.Tooltip,
          Legend: recharts.Legend,
          ResponsiveContainer: recharts.ResponsiveContainer
        });
        setIsLoaded(true);
      } catch (error) {
        console.error('Failed to load recharts:', error);
        setHasError(true);
      }
    };

    loadRecharts();
  }, []);

  if (hasError) {
    return (
      <div className="w-full h-[400px] bg-white rounded-xl shadow-md p-4 border border-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-navy mb-2">Chart Error</h3>
          <p className="text-gray-600">Failed to load chart library. Please try refreshing the page.</p>
        </div>
      </div>
    );
  }

  if (!isLoaded) {
    return (
      <div className="w-full h-[400px] bg-white rounded-xl shadow-md p-4 border border-gray-100 flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-navy mb-2">Loading Chart...</h3>
          <div className="w-8 h-8 border-4 border-teal border-t-transparent rounded-full animate-spin mx-auto"></div>
        </div>
      </div>
    );
  }

  const { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } = Chart;

  return (
    <div className="w-full h-[400px] bg-white rounded-xl shadow-md p-4 border border-gray-100">
      <h3 className="text-lg font-semibold text-navy mb-4">12-Month Loan Book Forecast</h3>
      <ResponsiveContainer width="100%" height="85%">
        <LineChart
          data={data}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis dataKey="month" />
          <YAxis
            yAxisId="left"
            tickFormatter={(value: number) => `$${(value / 1000000).toFixed(1)}M`}
          />
          <YAxis
            yAxisId="right"
            orientation="right"
            domain={[0, 5]}
            tickFormatter={(value: number) => `${value}%`}
          />
          <Tooltip
            formatter={(value: number, name: string) => {
              if (name === 'risk') {
                return [`${value}%`, 'Default Risk'];
              } else if (name === 'principal') {
                return [`$${formatNumber(value)}`, 'Outstanding Principal'];
              } else {
                return [`$${formatNumber(value)}`, 'Interest Income'];
              }
            }}
          />
          <Legend />
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="principal"
            name="Outstanding Principal"
            stroke="#0A2540"
            strokeWidth={2}
            dot={{ r: 2 }}
            activeDot={{ r: 6 }}
          />
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="interest"
            name="Interest Income"
            stroke="#00D4FF"
            strokeWidth={2}
            dot={{ r: 2 }}
          />
          <Line
            yAxisId="right"
            type="monotone"
            dataKey="risk"
            name="Default Risk"
            stroke="#7B61FF"
            strokeWidth={2}
            dot={{ r: 2 }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ForecastChart;
