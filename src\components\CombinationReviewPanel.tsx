import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardT<PERSON>le,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Check, 
  AlertTriangle, 
  Info, 
  Edit, 
  Save, 
  FileSpreadsheet, 
  Calendar, 
  DollarSign, 
  Tag, 
  FileText,
  Brain
} from 'lucide-react';

interface SourceSchema {
  source_id: string;
  source_name: string;
  field_mappings: any[];
}

interface CombinationStrategy {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  aiPowered: boolean;
}

interface CombinationReviewPanelProps {
  sources: {
    id: string;
    name: string;
  }[];
  onCombine: (strategy: string, name: string) => void;
  isLoading?: boolean;
}

const CombinationReviewPanel: React.FC<CombinationReviewPanelProps> = ({
  sources,
  onCombine,
  isLoading = false
}) => {
  const [selectedStrategy, setSelectedStrategy] = useState<string>("smart");
  const [combinedName, setCombinedName] = useState<string>(`Combined Dataset (${sources.length} sources)`);

  const combinationStrategies: CombinationStrategy[] = [
    {
      id: "smart",
      name: "AI-Powered Smart Combination",
      description: "Intelligently combines data using AI to detect and match transactions across sources, even with different schemas.",
      icon: <Brain className="h-8 w-8 text-purple-500" />,
      aiPowered: true
    },
    {
      id: "concat",
      name: "Simple Concatenation",
      description: "Stacks all data sources one after another. Best when sources contain different time periods or distinct transactions.",
      icon: <FileSpreadsheet className="h-8 w-8 text-blue-500" />,
      aiPowered: false
    },
    {
      id: "inner_join",
      name: "Inner Join",
      description: "Combines only rows that match across all sources. Best when you want only data that appears in all sources.",
      icon: <FileText className="h-8 w-8 text-green-500" />,
      aiPowered: false
    },
    {
      id: "outer_join",
      name: "Outer Join",
      description: "Combines all rows from all sources, matching where possible. Best when you want to preserve all data.",
      icon: <FileText className="h-8 w-8 text-orange-500" />,
      aiPowered: false
    }
  ];

  const handleCombine = () => {
    onCombine(selectedStrategy, combinedName);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Combine Data Sources</CardTitle>
        <CardDescription>
          Select how you want to combine {sources.length} data sources
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <label className="block text-sm font-medium mb-2">Combined Dataset Name</label>
          <input
            type="text"
            className="w-full p-2 border rounded-md"
            value={combinedName}
            onChange={(e) => setCombinedName(e.target.value)}
          />
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">Selected Sources</label>
          <div className="flex flex-wrap gap-2">
            {sources.map(source => (
              <Badge key={source.id} variant="secondary">
                {source.name}
              </Badge>
            ))}
          </div>
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">Combination Strategy</label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {combinationStrategies.map(strategy => (
              <div
                key={strategy.id}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedStrategy === strategy.id
                    ? "border-primary bg-primary/5"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setSelectedStrategy(strategy.id)}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-1">{strategy.icon}</div>
                  <div>
                    <div className="font-medium flex items-center gap-2">
                      {strategy.name}
                      {strategy.aiPowered && (
                        <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
                          AI-Powered
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">{strategy.description}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {selectedStrategy === "smart" && (
          <Card className="bg-purple-50 border-purple-200 mb-6">
            <CardHeader className="pb-2">
              <CardTitle className="text-base flex items-center gap-2">
                <Brain size={18} />
                How AI-Powered Combination Works
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ol className="list-decimal pl-5 space-y-2 text-sm">
                <li>AI analyzes the schema of each data source to understand field meanings</li>
                <li>Transactions are normalized to a standard format with confidence scoring</li>
                <li>AI detects if sources cover different time periods or contain overlapping data</li>
                <li>For overlapping periods, transactions are matched based on date, amount, and description</li>
                <li>When the same transaction appears in multiple sources, fields are merged with priority given to higher confidence values</li>
                <li>The result is a unified dataset with the most complete and accurate information</li>
              </ol>
            </CardContent>
          </Card>
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button onClick={handleCombine} disabled={isLoading}>
          {isLoading ? (
            <>Loading...</>
          ) : (
            <>
              <Check size={16} className="mr-2" />
              Combine Data Sources
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default CombinationReviewPanel;
