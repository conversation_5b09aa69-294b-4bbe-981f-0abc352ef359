import React from 'react'
import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import TestApp from './TestApp'
import './index.css'

// Add global error handler
console.log('Initializing application...');
window.addEventListener('error', (event) => {
  console.error('Global error caught:', event.error);
});

// Check if React is properly loaded
console.log('React version:', React.version);

const rootElement = document.getElementById("root");

if (!rootElement) {
  console.error("Failed to find the root element");
  document.body.innerHTML = '<div style="padding: 20px; text-align: center;"><h1>Error</h1><p>Failed to find the root element. Please check your HTML file.</p></div>';
} else {
  try {
    const root = createRoot(rootElement);
    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    );
  } catch (error) {
    console.error("Error rendering the app:", error);
    rootElement.innerHTML = `<div style="padding: 20px; text-align: center;"><h1>Error</h1><p>Failed to render the application. Please check the console for more details.</p><pre>${error instanceof Error ? error.message : String(error)}</pre></div>`;
  }
}
