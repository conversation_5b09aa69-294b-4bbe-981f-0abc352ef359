import logging
from datetime import datetime, timezone
from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api.dependencies import get_current_user, get_db
from app.models.data_source import DataSource
from app.models.pipeline import Pipeline, PipelineStep
from app.models.user import User
from app.schemas.data_cleaning import CleaningRequest, CleaningResponse
from app.services.data_cleaning.anomaly_detection import detect_anomalies
from app.services.data_cleaning.data_standardization import standardize_data
from app.services.data_cleaning.llm_classification import classify_data

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/clean", response_model=CleaningResponse)
async def clean_data(
    request: CleaningRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Clean and standardize data from a data source.
    """
    try:
        # Get the data source
        data_source = db.query(DataSource).filter(
            DataSource.id == request.data_source_id,
            DataSource.owner_id == current_user.id
        ).first()

        if not data_source:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Data source not found",
            )

        # Get the pipeline
        pipeline = db.query(Pipeline).filter(
            Pipeline.id == request.pipeline_id,
            Pipeline.owner_id == current_user.id
        ).first()

        if not pipeline:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Pipeline not found",
            )

        # Create pipeline step
        step = PipelineStep(
            name="Data Cleaning",
            step_type="data_cleaning",
            status="in_progress",
            order=request.step_order,
            config=request.config,
            pipeline_id=pipeline.id,
            data_source_id=data_source.id,
            started_at=datetime.now(timezone.utc)
        )

        db.add(step)
        db.commit()
        db.refresh(step)

        # Process data based on cleaning operations
        result = {}

        if "standardize" in request.operations:
            # Standardize data
            standardized_data = standardize_data(
                data_source=data_source,
                config=request.config.get("standardize", {})
            )
            result["standardize"] = standardized_data

        if "anomaly_detection" in request.operations:
            # Detect anomalies
            anomalies = detect_anomalies(
                data_source=data_source,
                config=request.config.get("anomaly_detection", {})
            )
            result["anomaly_detection"] = anomalies

        if "classification" in request.operations:
            # Classify data using LLM
            classifications = classify_data(
                data_source=data_source,
                config=request.config.get("classification", {})
            )
            result["classification"] = classifications

        # Update pipeline step
        step.status = "completed"
        step.result = result
        step.completed_at = datetime.now(timezone.utc)

        db.add(step)
        db.commit()
        db.refresh(step)

        return {
            "pipeline_id": pipeline.id,
            "step_id": step.id,
            "data_source_id": data_source.id,
            "operations": request.operations,
            "result": result
        }
    except Exception as e:
        logger.error(f"Error cleaning data: {e}")

        # Update pipeline step with error
        if 'step' in locals():
            step.status = "failed"
            step.error_message = str(e)
            db.add(step)
            db.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error cleaning data: {str(e)}",
        )


@router.get("/steps/{pipeline_id}", response_model=List[Dict[str, Any]])
async def get_cleaning_steps(
    pipeline_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),  # Used in pipeline ownership check
) -> Any:
    """
    Get all data cleaning steps for a pipeline.
    """
    # Check if pipeline exists and belongs to user
    pipeline = db.query(Pipeline).filter(
        Pipeline.id == pipeline_id,
        Pipeline.owner_id == current_user.id
    ).first()

    if not pipeline:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Pipeline not found",
        )

    # Get cleaning steps
    steps = db.query(PipelineStep).filter(
        PipelineStep.pipeline_id == pipeline_id,
        PipelineStep.step_type == "data_cleaning"
    ).order_by(PipelineStep.order).all()

    return [
        {
            "id": step.id,
            "name": step.name,
            "status": step.status,
            "order": step.order,
            "config": step.config,
            "result": step.result,
            "error_message": step.error_message,
            "started_at": step.started_at,
            "completed_at": step.completed_at,
            "data_source_id": step.data_source_id
        }
        for step in steps
    ]
