import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell } from 'recharts';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';

interface StandardizationChange {
  type: string;
  count: number;
  description: string;
}

interface StandardizationResultsProps {
  originalRows: number;
  standardizedRows: number;
  changes: Record<string, number>;
  sampleData?: any[];
}

const StandardizationResultsChart: React.FC<StandardizationResultsProps> = ({
  originalRows,
  standardizedRows,
  changes,
  sampleData = []
}) => {
  // Transform changes object into array for chart
  const changesData = Object.entries(changes).map(([type, count]) => ({
    type: formatChangeType(type),
    count,
    description: getChangeDescription(type)
  }));

  // Sort changes by count in descending order
  changesData.sort((a, b) => b.count - a.count);

  // Calculate total changes
  const totalChanges = changesData.reduce((sum, item) => sum + item.count, 0);
  
  // Calculate percentage of rows changed
  const percentChanged = originalRows > 0 ? (totalChanges / originalRows) * 100 : 0;

  // Colors for the chart
  const colors = [
    '#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A28BFF', 
    '#FF6B6B', '#4ECDC4', '#FFA69E', '#95D5B2', '#C1A7FF'
  ];

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Data Standardization Results</CardTitle>
        <CardDescription>
          Summary of changes made during data standardization
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="summary">
          <TabsList className="mb-4">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="changes">Changes</TabsTrigger>
            {sampleData.length > 0 && <TabsTrigger value="sample">Sample Data</TabsTrigger>}
          </TabsList>
          
          <TabsContent value="summary" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Original Rows</p>
                <p className="text-2xl font-bold">{originalRows.toLocaleString()}</p>
              </div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Total Changes</p>
                <p className="text-2xl font-bold">{totalChanges.toLocaleString()}</p>
              </div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Rows Changed (%)</p>
                <p className="text-2xl font-bold">{percentChanged.toFixed(1)}%</p>
              </div>
            </div>
            
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-2">Changes by Type</h3>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={changesData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 70 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="type" 
                      angle={-45} 
                      textAnchor="end" 
                      height={70} 
                      tick={{ fontSize: 12 }}
                    />
                    <YAxis />
                    <Tooltip 
                      formatter={(value: number, name: string, props: any) => {
                        return [`${value.toLocaleString()} changes`, props.payload.description];
                      }}
                    />
                    <Legend />
                    <Bar dataKey="count" name="Changes" radius={[4, 4, 0, 0]}>
                      {changesData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="changes">
            <div className="space-y-4">
              {changesData.map((change, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div>
                    <h4 className="font-medium">{change.type}</h4>
                    <p className="text-sm text-gray-500">{change.description}</p>
                  </div>
                  <Badge variant="outline" className="ml-2 text-sm">
                    {change.count.toLocaleString()} changes
                  </Badge>
                </div>
              ))}
            </div>
          </TabsContent>
          
          {sampleData.length > 0 && (
            <TabsContent value="sample">
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="bg-slate-100">
                      {Object.keys(sampleData[0]).map((key) => (
                        <th key={key} className="p-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {key}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {sampleData.map((row, rowIndex) => (
                      <tr key={rowIndex} className={rowIndex % 2 === 0 ? 'bg-white' : 'bg-slate-50'}>
                        {Object.values(row).map((value: any, colIndex) => (
                          <td key={colIndex} className="p-2 text-sm">
                            {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
};

// Helper function to format change type for display
function formatChangeType(type: string): string {
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Helper function to get description for each change type
function getChangeDescription(type: string): string {
  const descriptions: Record<string, string> = {
    date_format: 'Dates standardized to consistent format',
    case_normalization: 'Text case normalized (upper/lower/title)',
    whitespace_removal: 'Extra whitespace removed',
    currency_format: 'Currency values standardized',
    numeric_format: 'Numeric values formatted consistently',
    missing_values: 'Missing values handled',
    data_type_conversion: 'Data types converted',
    text_normalization: 'Text normalized (special chars removed, etc.)'
  };
  
  return descriptions[type] || 'Changes applied to data';
}

export default StandardizationResultsChart;
