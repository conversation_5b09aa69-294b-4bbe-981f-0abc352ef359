#!/usr/bin/env python3
"""
Test script to verify Excel export functionality.
"""

import requests
import json
from datetime import datetime

def test_excel_export():
    """Test the Excel export endpoint."""
    
    # Test parameters
    source_id = "3"  # Known source ID from our testing
    api_url = "http://localhost:8002"
    
    print(f"Testing Excel export for source ID: {source_id}")
    
    # Test data
    request_data = {
        "report_name": "Test Financial Report",
        "start_date": "2023-01-01",
        "end_date": "2023-12-31",
        "include_uncategorized": True
    }
    
    try:
        # Make the API request
        print("Making API request...")
        response = requests.post(
            f"{api_url}/api/v1/excel-reports/{source_id}/excel-report",
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            # Save the Excel file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"test_financial_report_{timestamp}.xlsx"
            
            with open(filename, "wb") as f:
                f.write(response.content)
            
            print(f"✅ Excel file saved successfully: {filename}")
            print(f"File size: {len(response.content)} bytes")
            
            # Try to open with openpyxl to verify it's a valid Excel file
            try:
                from openpyxl import load_workbook
                wb = load_workbook(filename)
                print(f"✅ Excel file is valid!")
                print(f"Worksheets: {wb.sheetnames}")
                
                # Check each worksheet
                for sheet_name in wb.sheetnames:
                    ws = wb[sheet_name]
                    print(f"  - {sheet_name}: {ws.max_row} rows, {ws.max_column} columns")
                
                wb.close()
                
            except Exception as e:
                print(f"❌ Error validating Excel file: {e}")
                
        else:
            print(f"❌ API request failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Error text: {response.text}")
    
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_data_availability():
    """Test if source data is available."""
    
    source_id = "3"
    api_url = "http://localhost:8002"
    
    print(f"\nTesting data availability for source ID: {source_id}")
    
    try:
        # Check if source exists
        response = requests.get(f"{api_url}/api/v1/data-sources/{source_id}")
        print(f"Data source status: {response.status_code}")
        
        if response.status_code == 200:
            source_data = response.json()
            print(f"✅ Data source found: {source_data.get('name', 'Unknown')}")
        
        # Check if simple data is available
        response = requests.get(f"{api_url}/api/v1/simple/simple-data/{source_id}?limit=10")
        print(f"Simple data status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Simple data available: {len(data.get('data', []))} sample rows")
        
        # Check if categorization clusters are available
        response = requests.get(f"{api_url}/api/v1/transaction-categorization/clusters/{source_id}")
        print(f"Categorization clusters status: {response.status_code}")
        
        if response.status_code == 200:
            clusters_data = response.json()
            clusters = clusters_data.get('clusters', [])
            print(f"✅ Categorization clusters: {len(clusters)} clusters found")
            for cluster in clusters[:3]:  # Show first 3 clusters
                print(f"  - {cluster.get('label', 'Unknown')}: {cluster.get('row_count', 0)} rows, ${cluster.get('amount_total', 0):.2f}")
        else:
            print("ℹ️  No categorization clusters found - will use raw data")
    
    except Exception as e:
        print(f"❌ Error checking data availability: {e}")

if __name__ == "__main__":
    print("🧪 Excel Export Test Suite")
    print("=" * 50)
    
    # Test data availability first
    test_data_availability()
    
    # Test Excel export
    print("\n" + "=" * 50)
    test_excel_export()
    
    print("\n✅ Test completed!")
