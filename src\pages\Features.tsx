
import React from 'react';
import {
  <PERSON>,
  FileCheck,
  <PERSON>Bar,
  Settings,
  Users,
  Layout,
  ArrowRight
} from 'lucide-react';
import FeatureCard from '@/components/FeatureCard';
import CallToAction from '@/components/CallToAction';

const Features = () => {
  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="section-padding bg-gradient-to-br from-navy to-[#051b38] text-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Platform Features
            </h1>
            <p className="text-xl opacity-90 mb-8 leading-relaxed max-w-3xl mx-auto">
              Our comprehensive AI-driven platform offers powerful features to help financial institutions transform their data 
              operations and forecasting capabilities.
            </p>
          </div>
        </div>
      </section>

      {/* Main Features Section */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-navy">Core Features</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Discover the powerful capabilities that make our platform unique.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard 
              icon={<Database size={24} />}
              title="Multi-Source Data Integration" 
              description="Connect with any data source including CSV files, SQL databases, APIs, and core banking systems to consolidate your financial data."
            />
            <FeatureCard 
              icon={<FileCheck size={24} />}
              title="AI-Powered Data Cleanup" 
              description="Automatically identify, classify, and standardize data fields using our advanced language models trained on financial data."
            />

            <FeatureCard 
              icon={<ChartBar size={24} />}
              title="Advanced Forecasting" 
              description="Generate accurate projections for principal balances, interest income, default rates, and other key financial metrics."
            />
            <FeatureCard 
              icon={<Settings size={24} />}
              title="Scenario Analysis Tools" 
              description="Test different business scenarios by adjusting interest rates, default assumptions, and growth parameters."
            />
            <FeatureCard 
              icon={<Layout size={24} />}
              title="Customizable Dashboards" 
              description="Create and share intuitive visualizations and reports tailored to different stakeholders in your organization."
            />
          </div>
        </div>
      </section>

      {/* Data Integration Section */}
      <section className="section-padding bg-lightgray">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6 text-navy">Data Integration</h2>
              <p className="text-lg text-gray-600 mb-6">
                Our platform seamlessly connects to your existing systems, making data consolidation effortless.
              </p>
              
              <ul className="space-y-4">
                <li className="flex items-center gap-3">
                  <ArrowRight size={18} className="text-teal" />
                  <span>Core banking system connectors</span>
                </li>
                <li className="flex items-center gap-3">
                  <ArrowRight size={18} className="text-teal" />
                  <span>CRM and loan origination system integration</span>
                </li>
                <li className="flex items-center gap-3">
                  <ArrowRight size={18} className="text-teal" />
                  <span>Payment gateway data import</span>
                </li>
                <li className="flex items-center gap-3">
                  <ArrowRight size={18} className="text-teal" />
                  <span>Batch processing for historical data</span>
                </li>
                <li className="flex items-center gap-3">
                  <ArrowRight size={18} className="text-teal" />
                  <span>Real-time data streaming capabilities</span>
                </li>
                <li className="flex items-center gap-3">
                  <ArrowRight size={18} className="text-teal" />
                  <span>Secure data transfer with encryption</span>
                </li>
              </ul>
            </div>
            
            <div className="bg-white p-6 rounded-xl shadow-md">
              <img 
                src="https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=600&fit=crop" 
                alt="Data integration illustration" 
                className="rounded-lg shadow-sm"
              />
            </div>
          </div>
        </div>
      </section>

      {/* AI Capabilities Section */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="order-2 md:order-1">
              <div className="bg-white p-6 rounded-xl shadow-md">
                <img 
                  src="https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?w=800&h=600&fit=crop" 
                  alt="AI capabilities illustration" 
                  className="rounded-lg shadow-sm"
                />
              </div>
            </div>
            
            <div className="order-1 md:order-2">
              <h2 className="text-3xl font-bold mb-6 text-navy">AI Capabilities</h2>
              <p className="text-lg text-gray-600 mb-6">
                Our advanced machine learning models are specifically trained for financial data analysis.
              </p>
              
              <ul className="space-y-4">
                <li className="flex items-center gap-3">
                  <ArrowRight size={18} className="text-teal" />
                  <span>Automatic field classification and mapping</span>
                </li>
                <li className="flex items-center gap-3">
                  <ArrowRight size={18} className="text-teal" />
                  <span>Missing data imputation</span>
                </li>
                <li className="flex items-center gap-3">
                  <ArrowRight size={18} className="text-teal" />
                  <span>Duplicate record detection</span>
                </li>
                <li className="flex items-center gap-3">
                  <ArrowRight size={18} className="text-teal" />
                  <span>Data quality scoring</span>
                </li>
                <li className="flex items-center gap-3">
                  <ArrowRight size={18} className="text-teal" />
                  <span>Pattern recognition for fraud detection</span>
                </li>
                <li className="flex items-center gap-3">
                  <ArrowRight size={18} className="text-teal" />
                  <span>Natural language processing for text data</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Security & Compliance Section */}
      <section className="section-padding bg-navy text-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Security & Compliance</h2>
            <p className="text-xl opacity-80 max-w-3xl mx-auto">
              Our platform is built with financial data security and regulatory requirements at its core.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/5">
              <h3 className="text-xl font-semibold mb-4">Data Security</h3>
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <div className="h-5 w-5 rounded-full bg-teal flex items-center justify-center">
                    <ArrowRight size={10} className="text-navy" />
                  </div>
                  <span className="text-white/90">End-to-end encryption</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="h-5 w-5 rounded-full bg-teal flex items-center justify-center">
                    <ArrowRight size={10} className="text-navy" />
                  </div>
                  <span className="text-white/90">On-premises deployment</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="h-5 w-5 rounded-full bg-teal flex items-center justify-center">
                    <ArrowRight size={10} className="text-navy" />
                  </div>
                  <span className="text-white/90">Secure data pipelines</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="h-5 w-5 rounded-full bg-teal flex items-center justify-center">
                    <ArrowRight size={10} className="text-navy" />
                  </div>
                  <span className="text-white/90">Multi-factor authentication</span>
                </li>
              </ul>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/5">
              <h3 className="text-xl font-semibold mb-4">Regulatory Compliance</h3>
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <div className="h-5 w-5 rounded-full bg-teal flex items-center justify-center">
                    <ArrowRight size={10} className="text-navy" />
                  </div>
                  <span className="text-white/90">GDPR compliance support</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="h-5 w-5 rounded-full bg-teal flex items-center justify-center">
                    <ArrowRight size={10} className="text-navy" />
                  </div>
                  <span className="text-white/90">SOC 2 Type II certified</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="h-5 w-5 rounded-full bg-teal flex items-center justify-center">
                    <ArrowRight size={10} className="text-navy" />
                  </div>
                  <span className="text-white/90">Audit trails and logging</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="h-5 w-5 rounded-full bg-teal flex items-center justify-center">
                    <ArrowRight size={10} className="text-navy" />
                  </div>
                  <span className="text-white/90">Regulatory reporting assistance</span>
                </li>
              </ul>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/5">
              <h3 className="text-xl font-semibold mb-4">Access Control</h3>
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <div className="h-5 w-5 rounded-full bg-teal flex items-center justify-center">
                    <ArrowRight size={10} className="text-navy" />
                  </div>
                  <span className="text-white/90">Role-based permissions</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="h-5 w-5 rounded-full bg-teal flex items-center justify-center">
                    <ArrowRight size={10} className="text-navy" />
                  </div>
                  <span className="text-white/90">Data access restrictions</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="h-5 w-5 rounded-full bg-teal flex items-center justify-center">
                    <ArrowRight size={10} className="text-navy" />
                  </div>
                  <span className="text-white/90">Activity monitoring</span>
                </li>
                <li className="flex items-center gap-3">
                  <div className="h-5 w-5 rounded-full bg-teal flex items-center justify-center">
                    <ArrowRight size={10} className="text-navy" />
                  </div>
                  <span className="text-white/90">IP restriction capabilities</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      <CallToAction />
    </div>
  );
};

export default Features;
