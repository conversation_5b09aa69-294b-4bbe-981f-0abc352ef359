import logging
from typing import Any, Dict, List, Optional

# Mock LLM client for development
from app.core.config import settings

logger = logging.getLogger(__name__)


class LLMClient:
    """Mock client for interacting with LLMs."""

    def __init__(self):
        # Mock LLM client
        self.llm = None
        # Don't log warning during init - only when actually used

    def classify_data(self, text: str, categories: List[str]) -> Dict[str, float]:
        """
        Classify text into predefined categories.

        Args:
            text: Text to classify
            categories: List of possible categories

        Returns:
            Dictionary mapping categories to confidence scores
        """
        # Mock implementation
        logger.info(f"Mock classify_data called with text: {text[:50]}... and categories: {categories}")
        result = '{"' + categories[0] + '": 0.95}'

        try:
            # Parse the result as JSON
            import json
            return json.loads(result)
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return {categories[0]: 1.0}  # Default to first category on error

    def extract_entities(self, text: str, entity_types: List[str]) -> Dict[str, List[str]]:
        """
        Extract entities from text.

        Args:
            text: Text to extract entities from
            entity_types: Types of entities to extract (e.g., "person", "organization")

        Returns:
            Dictionary mapping entity types to lists of extracted entities
        """
        # Mock implementation
        logger.info(f"Mock extract_entities called with text: {text[:50]}... and entity_types: {entity_types}")
        result = '{"' + entity_types[0] + '": ["mock entity"]}'

        try:
            # Parse the result as JSON
            import json
            return json.loads(result)
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return {entity_type: [] for entity_type in entity_types}

    def generate_sql(self, question: str, table_schema: Dict[str, List[str]]) -> str:
        """
        Generate SQL from a natural language question.

        Args:
            question: Natural language question
            table_schema: Dictionary mapping table names to lists of column names

        Returns:
            Generated SQL query
        """
        schema_str = ""
        for table, columns in table_schema.items():
            schema_str += f"Table: {table}\nColumns: {', '.join(columns)}\n"

        # Mock implementation
        logger.info(f"Mock generate_sql called with question: {question}")
        result = "SELECT * FROM " + list(table_schema.keys())[0] + " LIMIT 10;"

        return result.strip()

    def generate(self, prompt: str) -> str:
        """
        Generate text from a prompt.

        Args:
            prompt: Text prompt

        Returns:
            Generated text
        """
        # Mock implementation
        logger.info(f"Mock generate called with prompt: {prompt[:100]}...")

        # For transaction categorization, return a mock JSON response
        if "categorizing bank transactions" in prompt:
            try:
                # Extract tokens from the prompt
                import re
                tokens_match = re.search(r"common tokens:\s*([^\\n]+)", prompt)
                tokens = tokens_match.group(1).strip() if tokens_match else "unknown"

                # Log the extracted tokens
                logger.info(f"Extracted tokens: {tokens}")

                # Generate a category based on tokens
                category = "Uncategorized"
                confidence = 0.7

                # Check for common financial transaction categories
                if "salary" in tokens.lower() or "payment" in tokens.lower() or "deposit" in tokens.lower():
                    category = "Salary Income"
                    confidence = 0.9
                elif "amazon" in tokens.lower() or "shop" in tokens.lower() or "purchase" in tokens.lower():
                    category = "Online Shopping"
                    confidence = 0.85
                elif "netflix" in tokens.lower() or "subscription" in tokens.lower() or "monthly" in tokens.lower():
                    category = "Subscription Services"
                    confidence = 0.9
                elif "grocery" in tokens.lower() or "food" in tokens.lower() or "market" in tokens.lower():
                    category = "Grocery Shopping"
                    confidence = 0.95
                elif "restaurant" in tokens.lower() or "dining" in tokens.lower() or "cafe" in tokens.lower():
                    category = "Dining Out"
                    confidence = 0.8
                elif "gas" in tokens.lower() or "fuel" in tokens.lower() or "transport" in tokens.lower():
                    category = "Transportation"
                    confidence = 0.85
                elif "transfer" in tokens.lower() or "wire" in tokens.lower() or "ach" in tokens.lower():
                    category = "Bank Transfer"
                    confidence = 0.9
                elif "withdraw" in tokens.lower() or "atm" in tokens.lower() or "cash" in tokens.lower():
                    category = "Cash Withdrawal"
                    confidence = 0.95
                elif "bill" in tokens.lower() or "utility" in tokens.lower() or "payment" in tokens.lower():
                    category = "Bill Payment"
                    confidence = 0.85
                elif "insurance" in tokens.lower() or "premium" in tokens.lower() or "policy" in tokens.lower():
                    category = "Insurance"
                    confidence = 0.9
                elif "interest" in tokens.lower() or "dividend" in tokens.lower() or "investment" in tokens.lower():
                    category = "Investment Income"
                    confidence = 0.85
                elif "fee" in tokens.lower() or "charge" in tokens.lower() or "service" in tokens.lower():
                    category = "Bank Fees"
                    confidence = 0.9
                elif "tax" in tokens.lower() or "irs" in tokens.lower() or "refund" in tokens.lower():
                    category = "Taxes"
                    confidence = 0.95
                elif "health" in tokens.lower() or "medical" in tokens.lower() or "doctor" in tokens.lower():
                    category = "Healthcare"
                    confidence = 0.85
                elif "rent" in tokens.lower() or "mortgage" in tokens.lower() or "housing" in tokens.lower():
                    category = "Housing"
                    confidence = 0.9
                elif "education" in tokens.lower() or "tuition" in tokens.lower() or "school" in tokens.lower():
                    category = "Education"
                    confidence = 0.85
                elif "travel" in tokens.lower() or "hotel" in tokens.lower() or "flight" in tokens.lower():
                    category = "Travel"
                    confidence = 0.9
                elif "entertainment" in tokens.lower() or "movie" in tokens.lower() or "game" in tokens.lower():
                    category = "Entertainment"
                    confidence = 0.85
                else:
                    # Use the first token as part of the category name
                    first_token = tokens.split(',')[0].strip()
                    if first_token and len(first_token) > 2:
                        category = f"{first_token.capitalize()} Transactions"
                        confidence = 0.7

                # Log the generated category
                logger.info(f"Generated category: {category} with confidence {confidence}")

                return f'{{"label": "{category}", "confidence": {confidence}}}'
            except Exception as e:
                logger.error(f"Error generating category: {e}")
                # Return a default category on error
                return '{"label": "Miscellaneous", "confidence": 0.5}'

        # Default response for other prompts
        return "This is a mock response from the LLM client."


# Use lazy loading for the mock client
_mock_client = None

def get_mock_client():
    """
    Get or create the mock LLM client instance.

    Returns:
        LLMClient: The mock LLM client instance
    """
    global _mock_client
    if _mock_client is None:
        _mock_client = LLMClient()
        logger.warning("Using mock LLM client - no actual LLM calls will be made")
    return _mock_client

# For backward compatibility - create a lazy property
class _LLMClientProxy:
    def __getattr__(self, name):
        return getattr(get_mock_client(), name)

llm_client = _LLMClientProxy()
