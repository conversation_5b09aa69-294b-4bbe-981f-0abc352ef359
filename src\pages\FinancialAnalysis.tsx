import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { useToast } from '../components/ui/use-toast';
import { 
  ArrowLeft, 
  RefreshCw, 
  FileSpreadsheet, 
  BarChart, 
  Download,
  FileText,
  Database,
  LineChart,
  Calendar
} from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Badge } from '../components/ui/badge';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import fileService from '../services/excelService';
import PnLReportView from '../components/PnLReportView';

const FinancialAnalysis = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [dataSource, setDataSource] = useState<any | null>(null);
  const [sourceData, setSourceData] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('data');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [fieldMappings, setFieldMappings] = useState<any[]>([]);
  const [isMappingLoading, setIsMappingLoading] = useState<boolean>(false);
  const [pnlReport, setPnlReport] = useState<any | null>(null);
  const [isPnlLoading, setIsPnlLoading] = useState<boolean>(false);
  const [reportName, setReportName] = useState<string>('');
  
  useEffect(() => {
    if (!id) return;
    
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch data source info
        const source = await fileService.getDataSource(id);
        setDataSource(source);
        
        // Set default report name
        setReportName(`${source.name} P&L Report`);
        
        // Fetch source data
        const { data } = await fileService.getDataSourceData(id, 100);
        setSourceData(data);
        
        // Infer schema
        await inferSchema();
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          variant: "destructive",
          title: "Error fetching data",
          description: "There was a problem loading the data. Please try again.",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [id, toast]);
  
  const inferSchema = async () => {
    if (!id) return;
    
    setIsMappingLoading(true);
    try {
      // Call the API to infer schema
      const response = await fetch(`http://localhost:8002/api/infer-schema`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          source_id: id,
          override_existing: false
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Error inferring schema: ${response.statusText}`);
      }
      
      const result = await response.json();
      setFieldMappings(result.field_mappings);
      
      toast({
        title: "Schema inferred successfully",
        description: `Identified ${result.field_mappings.length} fields in the data.`,
      });
    } catch (error) {
      console.error('Error inferring schema:', error);
      toast({
        variant: "destructive",
        title: "Error inferring schema",
        description: "There was a problem analyzing the data structure. Please try again.",
      });
    } finally {
      setIsMappingLoading(false);
    }
  };
  
  const updateFieldMapping = (index: number, field: string, value: any) => {
    const updatedMappings = [...fieldMappings];
    updatedMappings[index] = {
      ...updatedMappings[index],
      [field]: value
    };
    setFieldMappings(updatedMappings);
  };
  
  const saveFieldMappings = async () => {
    if (!id) return;
    
    setIsMappingLoading(true);
    try {
      // Call the API to save field mappings
      const response = await fetch(`http://localhost:8002/api/field-mapping-override`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          source_id: id,
          field_mappings: fieldMappings
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Error saving field mappings: ${response.statusText}`);
      }
      
      const result = await response.json();
      
      toast({
        title: "Field mappings saved",
        description: "Your changes to the field mappings have been saved.",
      });
    } catch (error) {
      console.error('Error saving field mappings:', error);
      toast({
        variant: "destructive",
        title: "Error saving field mappings",
        description: "There was a problem saving your changes. Please try again.",
      });
    } finally {
      setIsMappingLoading(false);
    }
  };
  
  const generatePnL = async () => {
    if (!id || !reportName) return;
    
    setIsPnlLoading(true);
    try {
      // Call the API to generate P&L
      const response = await fetch(`http://localhost:8002/api/generate-pnl`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: reportName,
          source_ids: [id]
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Error generating P&L: ${response.statusText}`);
      }
      
      const result = await response.json();
      setPnlReport(result);
      
      toast({
        title: "P&L report generated",
        description: "Your P&L report has been generated successfully.",
      });
      
      // Switch to the P&L tab
      setActiveTab('pnl');
    } catch (error) {
      console.error('Error generating P&L:', error);
      toast({
        variant: "destructive",
        title: "Error generating P&L",
        description: "There was a problem generating the P&L report. Please try again.",
      });
    } finally {
      setIsPnlLoading(false);
    }
  };
  
  const exportPnL = () => {
    // In a real implementation, this would generate an Excel file
    toast({
      title: "Export initiated",
      description: "Your P&L report is being exported to Excel.",
    });
  };
  
  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/multi-file-ingestion')}
            className="mr-2"
          >
            <ArrowLeft size={16} className="mr-1" />
            Back
          </Button>
          <h1 className="text-2xl font-bold">
            {isLoading ? 'Loading...' : dataSource?.name || 'Financial Analysis'}
          </h1>
        </div>
        
        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => navigate(`/excel-analysis/${id}`)}
          >
            <BarChart size={16} className="mr-2" />
            Standard Analysis
          </Button>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="data">Data</TabsTrigger>
          <TabsTrigger value="mapping">Field Mapping</TabsTrigger>
          <TabsTrigger value="pnl">P&L Report</TabsTrigger>
        </TabsList>
        
        <TabsContent value="data" className="mt-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Source Data</h2>
                <div className="flex space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={inferSchema}
                    disabled={isMappingLoading}
                  >
                    {isMappingLoading ? (
                      <RefreshCw size={16} className="mr-2 animate-spin" />
                    ) : (
                      <FileText size={16} className="mr-2" />
                    )}
                    Infer Schema
                  </Button>
                </div>
              </div>
              
              {isLoading ? (
                <div className="flex justify-center items-center py-12">
                  <RefreshCw size={24} className="animate-spin text-gray-400" />
                </div>
              ) : sourceData.length > 0 ? (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {Object.keys(sourceData[0]).map((key) => (
                          <TableHead key={key}>{key}</TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {sourceData.map((row, rowIndex) => (
                        <TableRow key={rowIndex}>
                          {Object.values(row).map((value: any, cellIndex) => (
                            <TableCell key={cellIndex}>
                              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <p className="text-gray-500">No data available.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="mapping" className="mt-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Field Mapping</h2>
                <div className="flex space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={inferSchema}
                    disabled={isMappingLoading}
                  >
                    {isMappingLoading ? (
                      <RefreshCw size={16} className="mr-2 animate-spin" />
                    ) : (
                      <RefreshCw size={16} className="mr-2" />
                    )}
                    Re-Infer Schema
                  </Button>
                  <Button 
                    onClick={saveFieldMappings}
                    disabled={isMappingLoading || fieldMappings.length === 0}
                  >
                    Save Mappings
                  </Button>
                </div>
              </div>
              
              {isMappingLoading ? (
                <div className="flex justify-center items-center py-12">
                  <RefreshCw size={24} className="animate-spin text-gray-400" />
                </div>
              ) : fieldMappings.length > 0 ? (
                <div className="space-y-6">
                  <p className="text-sm text-gray-500">
                    Review and adjust the field mappings below. These mappings determine how your data will be interpreted for financial analysis.
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {fieldMappings.map((mapping, index) => (
                      <div key={index} className="bg-gray-50 p-4 rounded-md border border-gray-200">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <p className="font-medium">{mapping.source_field}</p>
                            <p className="text-sm text-gray-500">
                              Confidence: {(mapping.confidence * 100).toFixed(0)}%
                            </p>
                          </div>
                          <Badge 
                            variant={mapping.confidence > 0.7 ? "default" : "outline"}
                            className={mapping.confidence > 0.7 ? "bg-green-500" : ""}
                          >
                            {mapping.confidence > 0.7 ? "High Confidence" : "Low Confidence"}
                          </Badge>
                        </div>
                        
                        <div className="space-y-3">
                          <div>
                            <Label htmlFor={`semantic-type-${index}`}>Semantic Type</Label>
                            <Select 
                              value={mapping.semantic_type} 
                              onValueChange={(value) => updateFieldMapping(index, 'semantic_type', value)}
                            >
                              <SelectTrigger id={`semantic-type-${index}`}>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="amount">Amount</SelectItem>
                                <SelectItem value="date">Date</SelectItem>
                                <SelectItem value="category">Category</SelectItem>
                                <SelectItem value="description">Description</SelectItem>
                                <SelectItem value="unknown">Unknown</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          
                          <div>
                            <Label htmlFor={`target-field-${index}`}>Target Field</Label>
                            <Select 
                              value={mapping.target_field} 
                              onValueChange={(value) => updateFieldMapping(index, 'target_field', value)}
                            >
                              <SelectTrigger id={`target-field-${index}`}>
                                <SelectValue placeholder="Select field" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="amount">Amount</SelectItem>
                                <SelectItem value="transaction_date">Transaction Date</SelectItem>
                                <SelectItem value="category">Category</SelectItem>
                                <SelectItem value="description">Description</SelectItem>
                                <SelectItem value={mapping.source_field}>{mapping.source_field} (Keep as is)</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          
                          {mapping.semantic_type === 'amount' && (
                            <div>
                              <Label htmlFor={`is-income-${index}`}>Transaction Type</Label>
                              <Select 
                                value={mapping.is_income === null ? 'auto' : mapping.is_income ? 'income' : 'expense'} 
                                onValueChange={(value) => {
                                  let isIncome = null;
                                  if (value === 'income') isIncome = true;
                                  if (value === 'expense') isIncome = false;
                                  updateFieldMapping(index, 'is_income', isIncome);
                                }}
                              >
                                <SelectTrigger id={`is-income-${index}`}>
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="auto">Auto-detect (positive = income, negative = expense)</SelectItem>
                                  <SelectItem value="income">Income</SelectItem>
                                  <SelectItem value="expense">Expense</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex justify-end">
                    <Button 
                      onClick={saveFieldMappings}
                      disabled={isMappingLoading}
                    >
                      {isMappingLoading ? (
                        <>
                          <RefreshCw size={16} className="mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>Save Mappings</>
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  <FileText size={48} className="text-gray-300 mb-4" />
                  <p className="text-gray-500 mb-4">No field mappings available</p>
                  <Button onClick={inferSchema}>Infer Schema</Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="pnl" className="mt-6">
          {!pnlReport ? (
            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">P&L Report</h2>
                </div>
                
                <div className="space-y-6">
                  <p className="text-sm text-gray-500">
                    Generate a Profit & Loss report from your financial data. This will analyze your income and expenses over time.
                  </p>
                  
                  <div className="bg-gray-50 p-6 rounded-md border border-gray-200">
                    <h3 className="text-lg font-medium mb-4">Report Settings</h3>
                    
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="report-name">Report Name</Label>
                        <Input 
                          id="report-name" 
                          value={reportName} 
                          onChange={(e) => setReportName(e.target.value)}
                          placeholder="Enter a name for your report"
                        />
                      </div>
                      
                      <div className="flex justify-end">
                        <Button 
                          onClick={generatePnL}
                          disabled={isPnlLoading || !reportName || fieldMappings.length === 0}
                        >
                          {isPnlLoading ? (
                            <>
                              <RefreshCw size={16} className="mr-2 animate-spin" />
                              Generating...
                            </>
                          ) : (
                            <>Generate P&L Report</>
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <PnLReportView 
              report={pnlReport}
              isLoading={isPnlLoading}
              onExport={exportPnL}
            />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FinancialAnalysis;
