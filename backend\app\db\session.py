import os
import logging
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base

from app.core.config import settings

logger = logging.getLogger(__name__)

# Check environment to determine database connection
ENVIRONMENT = os.environ.get("ENVIRONMENT", "development")

if ENVIRONMENT == "development":
    # Use SQLite for development
    logger.info("Using SQLite for development")
    SQLALCHEMY_DATABASE_URL = "sqlite:///./app.db"
    engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})

    # Create the SQLite database directory if it doesn't exist
    os.makedirs(os.path.dirname(os.path.abspath("./app.db")), exist_ok=True)
else:
    # Use PostgreSQL for production
    try:
        logger.info(f"Connecting to PostgreSQL at {settings.POSTGRES_SERVER}")
        SQLALCHEMY_DATABASE_URL = str(settings.SQLALCHEMY_DATABASE_URI)
        engine = create_engine(SQLALCHEMY_DATABASE_URL)
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL: {e}")
        logger.warning("Falling back to SQLite")
        SQLALCHEMY_DATABASE_URL = "sqlite:///./dev.db"
        engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()
