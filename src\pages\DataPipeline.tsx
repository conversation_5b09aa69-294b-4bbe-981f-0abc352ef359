import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Upload,
  Database,
  FileCheck,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  FileText,
  Bell,
  HelpCircle,
  User,
  ArrowRight,
  Loader2
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/components/ui/use-toast';

import anomalyDetectionService, { JobStatus } from '@/services/anomalyDetectionService';

// Initial pipeline steps (will be updated based on job status)
const initialPipelineSteps = [
  { id: 'upload', name: 'Uploaded', icon: <Upload size={24} />, status: 'complete', link: '/data-ingestion' },
  { id: 'parsing', name: 'Parsing', icon: <FileText size={24} />, status: 'complete', link: '/data-ingestion' },
  { id: 'cleaning', name: 'Data Cleaning', icon: <FileCheck size={24} />, status: 'complete', link: '/data-cleaning' },
  { id: 'matching', name: 'Matching', icon: <Database size={24} />, status: 'in-progress', link: '/reconciliation' },
  { id: 'anomaly', name: 'Anomaly Review', icon: <AlertTriangle size={24} />, status: 'pending', link: '/data-cleaning' },
  { id: 'finalized', name: 'Finalized', icon: <CheckCircle size={24} />, status: 'pending', link: '/reconciliation-summary' }
];

// Initial recent activities
const initialRecentActivities = [
  { id: 1, action: 'Invoice data uploaded', timestamp: '10 minutes ago', user: 'John Doe', details: '1,240 records processed' },
  { id: 2, action: 'Payment data uploaded', timestamp: '15 minutes ago', user: 'John Doe', details: '1,180 records processed' },
  { id: 3, action: 'Matching process started', timestamp: '8 minutes ago', user: 'System', details: 'Automatic matching in progress' },
  { id: 4, action: '14 anomalies flagged', timestamp: '5 minutes ago', user: 'System', details: 'Requires review' },
  { id: 5, action: 'Monthly report generated', timestamp: '1 hour ago', user: 'Jane Smith', details: 'Q2 Reconciliation' }
];

const DataPipeline = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('pipelines');
  const [pipelineSteps, setPipelineSteps] = useState(initialPipelineSteps);
  const [activeJobs, setActiveJobs] = useState<JobStatus[]>([]);
  const [overallProgress, setOverallProgress] = useState(45);
  const [recentActivities, setRecentActivities] = useState(initialRecentActivities);

  // Check for active anomaly detection jobs
  useEffect(() => {
    const checkActiveJobs = async () => {
      try {
        // For demo purposes, we're using pipeline ID 1
        const jobs = await anomalyDetectionService.getActiveJobs(1);
        setActiveJobs(jobs);

        // Update pipeline steps based on job status
        const updatedSteps = [...pipelineSteps];
        const anomalyStepIndex = updatedSteps.findIndex(step => step.id === 'anomaly');

        if (jobs && jobs.length > 0) {
          // If there are active anomaly detection jobs, update the anomaly step
          if (anomalyStepIndex !== -1) {
            updatedSteps[anomalyStepIndex] = {
              ...updatedSteps[anomalyStepIndex],
              status: 'in-progress',
              link: `/data-cleaning?jobId=${jobs[0].job_id}`
            };
            setPipelineSteps(updatedSteps);
          }

          // Add to recent activities
          const newActivity = {
            id: Date.now(),
            action: 'Anomaly detection in progress',
            timestamp: 'Just now',
            user: 'System',
            details: `Job ID: ${jobs[0].job_id}, Progress: ${jobs[0].progress}%`
          };
          setRecentActivities([newActivity, ...recentActivities.slice(0, 4)]);
        } else {
          // No active jobs, make sure the anomaly step is in the correct state
          if (anomalyStepIndex !== -1) {
            // Check if the step is already marked as complete, if not set it to pending
            if (updatedSteps[anomalyStepIndex].status !== 'complete') {
              updatedSteps[anomalyStepIndex] = {
                ...updatedSteps[anomalyStepIndex],
                status: 'pending',
                link: `/data-cleaning`
              };
              setPipelineSteps(updatedSteps);
            }
          }
        }
      } catch (error) {
        console.error('Error checking active jobs:', error);
      }
    };

    checkActiveJobs();

    // Poll for active jobs every 30 seconds
    const interval = setInterval(checkActiveJobs, 30000);

    return () => clearInterval(interval);
  }, [pipelineSteps, recentActivities]);

  return (
    <div className="pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Top Navigation */}
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-navy">Data Pipeline</h1>
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="icon">
              <Bell size={20} />
            </Button>
            <Button variant="ghost" size="icon">
              <HelpCircle size={20} />
            </Button>
            <Button variant="ghost" size="icon">
              <User size={20} />
            </Button>
          </div>
        </div>

        {/* Main Tabs */}
        <Tabs defaultValue="pipelines" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full">
            <TabsTrigger value="pipelines" className="flex items-center gap-2">
              <Database size={16} />
              <span>Data Pipelines</span>
            </TabsTrigger>
            <TabsTrigger value="reconciliation" className="flex items-center gap-2">
              <FileCheck size={16} />
              <span>Reconciliation</span>
            </TabsTrigger>
            <TabsTrigger value="reports" className="flex items-center gap-2">
              <BarChart3 size={16} />
              <span>Reports</span>
            </TabsTrigger>
            <TabsTrigger value="audit" className="flex items-center gap-2">
              <Clock size={16} />
              <span>Audit Trail</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pipelines" className="space-y-6">
            {/* Pipeline Status Widget */}
            <Card>
              <CardHeader>
                <CardTitle>Pipeline Status</CardTitle>
                <CardDescription>
                  Current status of your data processing pipeline
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between mb-6">
                  <div className="flex-1 grid grid-cols-5 gap-2">
                    {pipelineSteps.map((step, index) => (
                      <Link
                        key={step.id}
                        to={step.link}
                        className={`relative flex flex-col items-center ${index < pipelineSteps.length - 1 ? 'after:content-[""] after:absolute after:top-6 after:right-0 after:w-full after:h-[2px] after:bg-gray-200 after:z-0' : ''} ${step.status === 'pending' ? 'pointer-events-none opacity-70' : 'hover:opacity-80'}`}
                      >
                        <div
                          className={`z-10 flex items-center justify-center w-12 h-12 rounded-full ${
                            step.status === 'complete' ? 'bg-green-100 text-green-600' :
                            step.status === 'in-progress' ? 'bg-blue-100 text-blue-600' :
                            'bg-gray-100 text-gray-400'
                          }`}
                        >
                          {step.icon}
                        </div>
                        <p className="mt-2 text-sm font-medium">{step.name}</p>
                        <Badge
                          className={`mt-1 ${
                            step.status === 'complete' ? 'bg-green-100 text-green-600 hover:bg-green-100' :
                            step.status === 'in-progress' ? 'bg-blue-100 text-blue-600 hover:bg-blue-100' :
                            'bg-gray-100 text-gray-500 hover:bg-gray-100'
                          }`}
                        >
                          {step.status === 'complete' ? 'Complete' :
                           step.status === 'in-progress' ? 'In Progress' :
                           'Pending'}
                        </Badge>
                      </Link>
                    ))}
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm text-gray-500">Overall Progress</p>
                    <div className="flex items-center gap-2 mt-1">
                      <Progress value={overallProgress} className="w-64 h-2" />
                      <span className="text-sm font-medium">{overallProgress}%</span>
                    </div>
                  </div>

                  {/* Show active anomaly detection jobs */}
                  {activeJobs.length > 0 && (
                    <div className="mt-4 p-3 bg-blue-50 rounded-md">
                      <div className="flex items-center gap-2">
                        <Loader2 size={16} className="text-blue-600 animate-spin" />
                        <p className="text-sm font-medium text-blue-700">Anomaly Detection in Progress</p>
                      </div>
                      <div className="mt-2">
                        <p className="text-xs text-blue-600">Job ID: {activeJobs[0].job_id}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Progress value={activeJobs[0].progress} className="w-full h-1.5" />
                          <span className="text-xs font-medium text-blue-700">{activeJobs[0].progress}%</span>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        className="mt-2 text-xs h-7 px-2 py-0"
                        onClick={() => navigate(`/data-cleaning?jobId=${activeJobs[0].job_id}`)}
                      >
                        View Details
                      </Button>
                    </div>
                  )}
                  <Link to="/data-ingestion">
                    <Button className="bg-teal hover:bg-teal/90">
                      Ingest New Data
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activities */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activities</CardTitle>
                <CardDescription>
                  Last 5 activities in your data pipeline
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-start gap-4 pb-4 border-b border-gray-100 last:border-0 last:pb-0">
                      <div className="bg-gray-100 p-2 rounded-full">
                        <Clock size={16} className="text-gray-500" />
                      </div>
                      <div>
                        <p className="font-medium">{activity.action}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <p className="text-sm text-gray-500">{activity.timestamp}</p>
                          <span className="text-gray-300">•</span>
                          <p className="text-sm text-gray-500">By {activity.user}</p>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{activity.details}</p>
                      </div>
                      <Button variant="ghost" size="sm" className="ml-auto">
                        View
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reconciliation">
            <Card className="p-8 text-center">
              <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                <FileCheck size={32} className="text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Reconciliation Dashboard</h3>
              <p className="text-gray-500 mb-6 max-w-md mx-auto">
                Match and reconcile data from multiple sources, review anomalies, and ensure data accuracy.
              </p>
              <Link to="/reconciliation">
                <Button className="mx-auto">
                  Go to Reconciliation
                  <ArrowRight size={16} className="ml-2" />
                </Button>
              </Link>
            </Card>
          </TabsContent>

          <TabsContent value="reports">
            <Card className="p-8 text-center">
              <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                <BarChart3 size={32} className="text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Report Configuration</h3>
              <p className="text-gray-500 mb-6 max-w-md mx-auto">
                Create custom reports, visualize your data, and export in various formats.
              </p>
              <Link to="/reports">
                <Button className="mx-auto">
                  Go to Reports
                  <ArrowRight size={16} className="ml-2" />
                </Button>
              </Link>
            </Card>
          </TabsContent>

          <TabsContent value="audit">
            <Card className="p-8 text-center">
              <div className="mx-auto w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mb-4">
                <Clock size={32} className="text-amber-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Audit Trail</h3>
              <p className="text-gray-500 mb-6 max-w-md mx-auto">
                View a complete history of all changes and actions for compliance and auditing purposes.
              </p>
              <Link to="/audit-trail">
                <Button className="mx-auto">
                  View Audit Trail
                  <ArrowRight size={16} className="ml-2" />
                </Button>
              </Link>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default DataPipeline;
