import os
import logging
import uvicorn
from dotenv import load_dotenv
from app.db.init_db import init_db

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Set environment variable for development
os.environ["ENVIRONMENT"] = "development"

# Initialize the database
logger.info("Initializing database...")
init_db()

if __name__ == "__main__":
    logger.info("Starting API server...")
    uvicorn.run("app.main:app", host="0.0.0.0", port=8002, reload=True)
