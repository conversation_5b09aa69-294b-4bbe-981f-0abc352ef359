# How to Run the Financial Data Analysis System

This guide provides step-by-step instructions for running the application and troubleshooting common issues.

## Prerequisites

- Python 3.9+ with pip
- Node.js 16+ with npm

## Step 1: Install Python Dependencies

First, install all required Python packages:

```bash
cd backend
pip install -r requirements.txt
pip install pandas numpy openpyxl
cd ..
```

## Step 2: Start the Backend Server

From the project root directory, run the consolidated API script:

```bash
python backend/run_consolidated_api.py
```

You should see output similar to:
```
INFO:__main__:Initializing database...
INFO:app.db.init_db:Database tables created
INFO:app.db.init_db:Sample data created
INFO:__main__:Starting consolidated API server...
INFO:__main__:This server replaces excel_api.py, simple_api.py, and mock_api.py
INFO:__main__:All endpoints are now available through the structured app implementation
INFO:__main__:API will be available at http://localhost:8002
INFO:__main__:API documentation will be available at http://localhost:8002/docs
INFO:     Will watch for changes in these directories: ['C:\\Users\\<USER>