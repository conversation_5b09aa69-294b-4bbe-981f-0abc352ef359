import { useState, useEffect } from 'react';
import { Card, CardContent } from '../components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../components/ui/tabs';
import { Button } from '../components/ui/button';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'lucide-react';
import { DataSummary, DataAnalysis } from '../services/excelService';
import { Progress } from '../components/ui/progress';
import { Badge } from '../components/ui/badge';

interface DataSummaryViewProps {
  datasetId: string;
  isLoading: boolean;
  summary: DataSummary | null;
  analysis: DataAnalysis | null;
  onRefresh: () => void;
}

const DataSummaryView: React.FC<DataSummaryViewProps> = ({
  datasetId,
  isLoading,
  summary,
  analysis,
  onRefresh
}) => {
  const [activeTab, setActiveTab] = useState('summary');

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-center items-center py-12">
            <RefreshCw size={24} className="animate-spin text-gray-400" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!summary) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Data Summary</h2>
            <Button onClick={onRefresh} variant="outline" size="sm">
              <RefreshCw size={16} className="mr-2" />
              Generate Summary
            </Button>
          </div>
          <div className="flex flex-col items-center justify-center py-12">
            <BarChart size={48} className="text-gray-300 mb-4" />
            <p className="text-gray-500 mb-4">No summary data available</p>
            <Button onClick={onRefresh}>Generate Summary</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Data Insights</h2>
          <Button onClick={onRefresh} variant="outline" size="sm">
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="patterns">Patterns & Insights</TabsTrigger>
            <TabsTrigger value="anomalies">Anomalies</TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Dataset Overview</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                    <p className="text-sm text-gray-500">Rows</p>
                    <p className="text-xl font-bold">{summary.row_count.toLocaleString()}</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                    <p className="text-sm text-gray-500">Columns</p>
                    <p className="text-xl font-bold">{summary.column_count.toLocaleString()}</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                    <p className="text-sm text-gray-500">Numerical Columns</p>
                    <p className="text-xl font-bold">{Object.keys(summary.numerical_columns).length}</p>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
                    <p className="text-sm text-gray-500">Categorical Columns</p>
                    <p className="text-xl font-bold">{Object.keys(summary.categorical_columns).length}</p>
                  </div>
                </div>

                {summary.is_combined && summary.source_distribution && (
                  <div>
                    <h3 className="text-lg font-medium mt-6 mb-2">Source Distribution</h3>
                    <div className="space-y-2">
                      {Object.entries(summary.source_distribution).map(([source, count]) => (
                        <div key={source} className="flex items-center">
                          <div className="w-32 truncate">{source}</div>
                          <div className="flex-1 mx-2">
                            <Progress 
                              value={(count / summary.row_count) * 100} 
                              className="h-2" 
                            />
                          </div>
                          <div className="text-sm text-gray-500 w-20 text-right">
                            {count.toLocaleString()} rows
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">Top Correlations</h3>
                {summary.top_correlations.length > 0 ? (
                  <div className="space-y-3">
                    {summary.top_correlations.map((corr, index) => (
                      <div key={index} className="bg-gray-50 p-3 rounded-md border border-gray-200">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="font-medium">{corr.column1} ↔ {corr.column2}</p>
                            <p className="text-sm text-gray-500">
                              Correlation: {(corr.correlation * 100).toFixed(1)}%
                            </p>
                          </div>
                          <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                            corr.correlation > 0.8 ? 'bg-green-100' : 
                            corr.correlation > 0.5 ? 'bg-blue-100' : 'bg-gray-100'
                          }`}>
                            <TrendingUp className={`h-5 w-5 ${
                              corr.correlation > 0.8 ? 'text-green-600' : 
                              corr.correlation > 0.5 ? 'text-blue-600' : 'text-gray-600'
                            }`} />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No significant correlations found</p>
                )}

                {Object.keys(summary.missing_values).length > 0 && (
                  <div className="mt-6">
                    <h3 className="text-lg font-medium mb-2">Missing Values</h3>
                    <div className="space-y-2">
                      {Object.entries(summary.missing_values).map(([column, info]) => (
                        <div key={column} className="flex items-center">
                          <div className="w-32 truncate">{column}</div>
                          <div className="flex-1 mx-2">
                            <Progress 
                              value={info.percentage} 
                              className="h-2 bg-red-100" 
                            />
                          </div>
                          <div className="text-sm text-gray-500 w-20 text-right">
                            {info.percentage.toFixed(1)}% missing
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">Column Statistics</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(summary.numerical_columns).map(([column, stats]) => (
                  <div key={column} className="bg-gray-50 p-3 rounded-md border border-gray-200">
                    <p className="font-medium truncate">{column}</p>
                    <div className="grid grid-cols-2 gap-x-4 gap-y-1 mt-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500">Min:</span>
                        <span>{stats.min !== null ? stats.min.toLocaleString() : 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Max:</span>
                        <span>{stats.max !== null ? stats.max.toLocaleString() : 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Mean:</span>
                        <span>{stats.mean !== null ? stats.mean.toLocaleString(undefined, { maximumFractionDigits: 2 }) : 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500">Median:</span>
                        <span>{stats.median !== null ? stats.median.toLocaleString(undefined, { maximumFractionDigits: 2 }) : 'N/A'}</span>
                      </div>
                    </div>
                  </div>
                ))}

                {Object.entries(summary.categorical_columns).map(([column, stats]) => (
                  <div key={column} className="bg-gray-50 p-3 rounded-md border border-gray-200">
                    <p className="font-medium truncate">{column}</p>
                    <p className="text-sm text-gray-500 mt-1">
                      {stats.unique_values} unique values
                    </p>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500 mb-1">Top values:</p>
                      <div className="space-y-1">
                        {Object.entries(stats.top_values).slice(0, 3).map(([value, count]) => (
                          <div key={value} className="flex justify-between text-sm">
                            <span className="truncate max-w-[150px]">{value}</span>
                            <span>{count} rows</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="patterns" className="mt-4">
            {analysis && analysis.patterns.length > 0 ? (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Discovered Patterns</h3>
                
                {analysis.patterns.filter(p => p.type === 'correlation').length > 0 && (
                  <div>
                    <h4 className="text-md font-medium mb-2">Correlations</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {analysis.patterns
                        .filter(p => p.type === 'correlation')
                        .map((pattern, index) => (
                          <div key={index} className="bg-blue-50 p-4 rounded-md border border-blue-200">
                            <div className="flex items-start">
                              <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                <TrendingUp className="h-4 w-4 text-blue-600" />
                              </div>
                              <div>
                                <p className="font-medium">{pattern.description}</p>
                                <p className="text-sm text-gray-600 mt-1">
                                  Correlation: {(pattern.correlation || 0).toFixed(2)}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
                
                {analysis.patterns.filter(p => p.type === 'source_difference').length > 0 && (
                  <div className="mt-6">
                    <h4 className="text-md font-medium mb-2">Source Differences</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {analysis.patterns
                        .filter(p => p.type === 'source_difference')
                        .map((pattern, index) => (
                          <div key={index} className="bg-amber-50 p-4 rounded-md border border-amber-200">
                            <div className="flex items-start">
                              <div className="h-8 w-8 bg-amber-100 rounded-full flex items-center justify-center mr-3">
                                <AlertTriangle className="h-4 w-4 text-amber-600" />
                              </div>
                              <div>
                                <p className="font-medium">{pattern.description}</p>
                                <div className="grid grid-cols-2 gap-2 mt-2 text-sm">
                                  <div>
                                    <span className="text-gray-600">Source mean: </span>
                                    <span className="font-medium">{pattern.source_mean?.toFixed(2)}</span>
                                  </div>
                                  <div>
                                    <span className="text-gray-600">Overall mean: </span>
                                    <span className="font-medium">{pattern.overall_mean?.toFixed(2)}</span>
                                  </div>
                                  <div className="col-span-2">
                                    <span className="text-gray-600">Difference: </span>
                                    <span className="font-medium">{pattern.difference_percentage?.toFixed(1)}%</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12">
                <PieChart size={48} className="text-gray-300 mb-4" />
                <p className="text-gray-500 mb-4">No patterns or insights available</p>
                <Button onClick={onRefresh}>Analyze Data</Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="anomalies" className="mt-4">
            {analysis && Object.keys(analysis.outliers).length > 0 ? (
              <div className="space-y-6">
                <h3 className="text-lg font-medium">Detected Anomalies</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {Object.entries(analysis.outliers).map(([column, info]: [string, any]) => (
                    <div key={column} className="bg-red-50 p-4 rounded-md border border-red-200">
                      <p className="font-medium">{column}</p>
                      <div className="mt-2 space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Outliers:</span>
                          <span className="font-medium">{info.count}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Percentage:</span>
                          <span className="font-medium">{info.percentage.toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Range:</span>
                          <span className="font-medium">
                            {info.min_value.toLocaleString()} - {info.max_value.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div>
                  <h4 className="text-md font-medium mb-3">Column Statistics</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {Object.entries(analysis.column_stats)
                      .filter(([_, stats]: [string, any]) => stats.type === 'numerical')
                      .map(([column, stats]: [string, any]) => (
                        <div key={column} className="bg-gray-50 p-3 rounded-md border border-gray-200">
                          <div className="flex justify-between items-center">
                            <p className="font-medium truncate">{column}</p>
                            {stats.outlier_count > 0 && (
                              <Badge variant="destructive" className="ml-2">
                                {stats.outlier_count} outliers
                              </Badge>
                            )}
                          </div>
                          <div className="mt-2 text-sm">
                            <div className="flex justify-between mb-1">
                              <span className="text-gray-500">Quartiles:</span>
                            </div>
                            <div className="flex items-center h-6 bg-gray-200 rounded-full overflow-hidden">
                              <div className="h-full bg-blue-200" style={{ width: '25%' }}></div>
                              <div className="h-full bg-blue-300" style={{ width: '25%' }}></div>
                              <div className="h-full bg-blue-400" style={{ width: '25%' }}></div>
                              <div className="h-full bg-blue-500" style={{ width: '25%' }}></div>
                            </div>
                            <div className="flex justify-between mt-1 text-xs">
                              <span>{stats.quartiles.q1.toLocaleString()}</span>
                              <span>{stats.quartiles.median.toLocaleString()}</span>
                              <span>{stats.quartiles.q3.toLocaleString()}</span>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12">
                <AlertTriangle size={48} className="text-gray-300 mb-4" />
                <p className="text-gray-500 mb-4">No anomalies detected</p>
                <Button onClick={onRefresh}>Analyze Data</Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default DataSummaryView;
