import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { MatchingConfigFormValues } from '../validationSchema';
import { 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage,
  FormDescription
} from '@/components/ui/form';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Trash2, 
  ArrowRight,
  InfoIcon
} from 'lucide-react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent } from '@/components/ui/card';

interface AdvancedSettingsProps {
  form: UseFormReturn<MatchingConfigFormValues>;
  methods: string[];
  sourceFields: string[];
  targetFields: string[];
  isHybridSelected: boolean;
  isWeightedFieldSelected: boolean;
  isPhoneticNameSelected: boolean;
}

const AdvancedSettings: React.FC<AdvancedSettingsProps> = ({ 
  form, 
  methods,
  sourceFields,
  targetFields,
  isHybridSelected,
  isWeightedFieldSelected,
  isPhoneticNameSelected
}) => {
  const [activeTab, setActiveTab] = useState('general');
  
  // For field weights
  const [sourceFieldWeight, setSourceFieldWeight] = useState<string>('');
  const [targetFieldWeight, setTargetFieldWeight] = useState<string>('');
  const [fieldWeight, setFieldWeight] = useState<number>(0.5);
  
  // For name fields
  const [sourceNameField, setSourceNameField] = useState<string>('');
  const [targetNameField, setTargetNameField] = useState<string>('');
  
  // Watch form values
  const fieldWeights = form.watch('fieldWeights') || {};
  const nameFields = form.watch('nameFields') || { source: [], target: [] };
  
  // Add a new field weight
  const addFieldWeight = () => {
    if (sourceFieldWeight && targetFieldWeight) {
      const updatedWeights = { ...fieldWeights };
      
      if (!updatedWeights[sourceFieldWeight]) {
        updatedWeights[sourceFieldWeight] = {};
      }
      
      updatedWeights[sourceFieldWeight][targetFieldWeight] = fieldWeight;
      
      form.setValue('fieldWeights', updatedWeights, { shouldValidate: true });
      setSourceFieldWeight('');
      setTargetFieldWeight('');
      setFieldWeight(0.5);
    }
  };
  
  // Remove a field weight
  const removeFieldWeight = (sourceField: string, targetField: string) => {
    const updatedWeights = { ...fieldWeights };
    
    if (updatedWeights[sourceField]) {
      delete updatedWeights[sourceField][targetField];
      
      // Remove the source field if it has no target fields
      if (Object.keys(updatedWeights[sourceField]).length === 0) {
        delete updatedWeights[sourceField];
      }
      
      form.setValue('fieldWeights', updatedWeights, { shouldValidate: true });
    }
  };
  
  // Add a name field
  const addNameField = () => {
    if (sourceNameField && targetNameField) {
      const updatedNameFields = { 
        source: [...(nameFields.source || []), sourceNameField],
        target: [...(nameFields.target || []), targetNameField]
      };
      
      form.setValue('nameFields', updatedNameFields, { shouldValidate: true });
      setSourceNameField('');
      setTargetNameField('');
    }
  };
  
  // Remove a name field pair
  const removeNameField = (index: number) => {
    const updatedNameFields = {
      source: [...(nameFields.source || [])],
      target: [...(nameFields.target || [])]
    };
    
    updatedNameFields.source.splice(index, 1);
    updatedNameFields.target.splice(index, 1);
    
    form.setValue('nameFields', updatedNameFields, { shouldValidate: true });
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          {isHybridSelected && <TabsTrigger value="hybrid">Hybrid Weights</TabsTrigger>}
          {isWeightedFieldSelected && <TabsTrigger value="weights">Field Weights</TabsTrigger>}
          {isPhoneticNameSelected && <TabsTrigger value="names">Name Fields</TabsTrigger>}
        </TabsList>
        
        <TabsContent value="general">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="maxMatches"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Maximum Matches</FormLabel>
                  <FormDescription>
                    Maximum number of matches to return per record
                  </FormDescription>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                      min={1}
                      max={10}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="batchSize"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Batch Size</FormLabel>
                  <FormDescription>
                    Number of records to process in each batch for embeddings
                  </FormDescription>
                  <FormControl>
                    <Input
                      type="number"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                      min={1}
                      max={100}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="useFuzzy"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Use Fuzzy Matching</FormLabel>
                    <FormDescription>
                      Enable fuzzy matching for string comparisons
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </TabsContent>
        
        {isHybridSelected && (
          <TabsContent value="hybrid">
            <Alert className="mb-4">
              <InfoIcon className="h-4 w-4" />
              <AlertDescription>
                Configure the weights for each component in hybrid matching. The weights should sum to 1.
              </AlertDescription>
            </Alert>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormField
                control={form.control}
                name="hybridWeights.tfidf"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>TF-IDF Weight</FormLabel>
                    <FormDescription>
                      Weight for TF-IDF similarity
                    </FormDescription>
                    <div className="flex items-center space-x-4">
                      <FormControl>
                        <Slider
                          value={[field.value || 0.4]}
                          min={0}
                          max={1}
                          step={0.05}
                          onValueChange={(value) => field.onChange(value[0])}
                          className="flex-1"
                        />
                      </FormControl>
                      <Input
                        type="number"
                        value={field.value}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        min={0}
                        max={1}
                        step={0.05}
                        className="w-20"
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="hybridWeights.embeddings"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Embeddings Weight</FormLabel>
                    <FormDescription>
                      Weight for embeddings similarity
                    </FormDescription>
                    <div className="flex items-center space-x-4">
                      <FormControl>
                        <Slider
                          value={[field.value || 0.4]}
                          min={0}
                          max={1}
                          step={0.05}
                          onValueChange={(value) => field.onChange(value[0])}
                          className="flex-1"
                        />
                      </FormControl>
                      <Input
                        type="number"
                        value={field.value}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        min={0}
                        max={1}
                        step={0.05}
                        className="w-20"
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="hybridWeights.fuzzy"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Fuzzy Weight</FormLabel>
                    <FormDescription>
                      Weight for fuzzy matching similarity
                    </FormDescription>
                    <div className="flex items-center space-x-4">
                      <FormControl>
                        <Slider
                          value={[field.value || 0.2]}
                          min={0}
                          max={1}
                          step={0.05}
                          onValueChange={(value) => field.onChange(value[0])}
                          className="flex-1"
                        />
                      </FormControl>
                      <Input
                        type="number"
                        value={field.value}
                        onChange={(e) => field.onChange(parseFloat(e.target.value))}
                        min={0}
                        max={1}
                        step={0.05}
                        className="w-20"
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <Card className="mt-6">
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Total Weight</p>
                    <p className="text-sm text-gray-500">Sum of all weights</p>
                  </div>
                  <Badge className={
                    Math.abs((form.watch('hybridWeights.tfidf') || 0) + 
                            (form.watch('hybridWeights.embeddings') || 0) + 
                            (form.watch('hybridWeights.fuzzy') || 0) - 1) < 0.01 
                      ? 'bg-green-500' 
                      : 'bg-red-500'
                  }>
                    {((form.watch('hybridWeights.tfidf') || 0) + 
                      (form.watch('hybridWeights.embeddings') || 0) + 
                      (form.watch('hybridWeights.fuzzy') || 0)).toFixed(2)}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
        
        {isWeightedFieldSelected && (
          <TabsContent value="weights">
            <Alert className="mb-4">
              <InfoIcon className="h-4 w-4" />
              <AlertDescription>
                Configure weights for specific field pairs. Higher weights give more importance to those fields during matching.
              </AlertDescription>
            </Alert>
            
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end mb-4">
              <div className="md:col-span-2">
                <FormLabel>Source Field</FormLabel>
                <Select value={sourceFieldWeight} onValueChange={setSourceFieldWeight}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select source field" />
                  </SelectTrigger>
                  <SelectContent>
                    {sourceFields.map((field) => (
                      <SelectItem key={field} value={field}>
                        {field}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="md:col-span-2">
                <FormLabel>Target Field</FormLabel>
                <Select value={targetFieldWeight} onValueChange={setTargetFieldWeight}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select target field" />
                  </SelectTrigger>
                  <SelectContent>
                    {targetFields.map((field) => (
                      <SelectItem key={field} value={field}>
                        {field}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <FormLabel>Weight</FormLabel>
                <div className="flex items-center space-x-2">
                  <Slider
                    value={[fieldWeight]}
                    min={0.1}
                    max={1}
                    step={0.1}
                    onValueChange={(value) => setFieldWeight(value[0])}
                    className="w-24"
                  />
                  <Badge>{fieldWeight.toFixed(1)}</Badge>
                </div>
              </div>
              
              <div className="md:col-span-5">
                <Button 
                  type="button" 
                  onClick={addFieldWeight}
                  disabled={!sourceFieldWeight || !targetFieldWeight}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Field Weight
                </Button>
              </div>
            </div>
            
            {Object.keys(fieldWeights).length > 0 ? (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Source Field</TableHead>
                      <TableHead>Target Field</TableHead>
                      <TableHead>Weight</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(fieldWeights).flatMap(([sourceField, targetWeights]) =>
                      Object.entries(targetWeights).map(([targetField, weight]) => (
                        <TableRow key={`${sourceField}-${targetField}`}>
                          <TableCell>
                            <Badge variant="outline" className="font-mono">
                              {sourceField}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className="font-mono">
                              {targetField}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge>{weight.toFixed(1)}</Badge>
                          </TableCell>
                          <TableCell>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFieldWeight(sourceField, targetField)}
                            >
                              <Trash2 className="h-4 w-4 text-red-500" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <Alert>
                <AlertDescription>
                  No field weights defined yet. Add weights to give more importance to specific field pairs.
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>
        )}
        
        {isPhoneticNameSelected && (
          <TabsContent value="names">
            <Alert className="mb-4">
              <InfoIcon className="h-4 w-4" />
              <AlertDescription>
                Specify which fields contain names for phonetic matching. You can map multiple name fields (e.g., first name, last name).
              </AlertDescription>
            </Alert>
            
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end mb-4">
              <div className="md:col-span-2">
                <FormLabel>Source Name Field</FormLabel>
                <Select value={sourceNameField} onValueChange={setSourceNameField}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select source name field" />
                  </SelectTrigger>
                  <SelectContent>
                    {sourceFields.map((field) => (
                      <SelectItem 
                        key={field} 
                        value={field}
                        disabled={nameFields.source?.includes(field)}
                      >
                        {field}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex justify-center">
                <ArrowRight className="h-6 w-6 text-gray-400" />
              </div>
              
              <div className="md:col-span-2">
                <FormLabel>Target Name Field</FormLabel>
                <Select value={targetNameField} onValueChange={setTargetNameField}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select target name field" />
                  </SelectTrigger>
                  <SelectContent>
                    {targetFields.map((field) => (
                      <SelectItem 
                        key={field} 
                        value={field}
                        disabled={nameFields.target?.includes(field)}
                      >
                        {field}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="md:col-span-5">
                <Button 
                  type="button" 
                  onClick={addNameField}
                  disabled={!sourceNameField || !targetNameField}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Name Field Mapping
                </Button>
              </div>
            </div>
            
            {nameFields.source && nameFields.source.length > 0 ? (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Source Name Field</TableHead>
                      <TableHead>Target Name Field</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {nameFields.source.map((sourceField, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Badge variant="outline" className="font-mono">
                            {sourceField}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-mono">
                            {nameFields.target?.[index] || ''}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeNameField(index)}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <Alert>
                <AlertDescription>
                  No name fields defined yet. Add name field mappings for phonetic matching.
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};

export default AdvancedSettings;
