import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  ArrowLeft, 
  ArrowRight, 
  CheckCircle, 
  AlertTriangle, 
  Download, 
  BarChart3,
  PieChart,
  FileText,
  ExternalLink
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

const ReconciliationSummary = () => {
  const navigate = useNavigate();
  const [isProcessing, setIsProcessing] = useState(false);
  const [isComplete, setIsComplete] = useState(false);

  const handleFinalize = () => {
    setIsProcessing(true);
    
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      setIsComplete(true);
    }, 2000);
  };

  return (
    <div className="pt-20 pb-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center mb-8">
          <Button 
            variant="ghost" 
            size="sm" 
            className="mr-4"
            onClick={() => navigate('/reconciliation')}
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Reconciliation
          </Button>
          <h1 className="text-3xl font-bold text-navy">Reconciliation Summary</h1>
        </div>

        {/* Headline Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Matched vs. Unmatched</CardTitle>
              <CardDescription>
                Overview of reconciliation results
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Matched</span>
                  <span className="text-sm font-medium text-green-600">1,170 (94%)</span>
                </div>
                <Progress value={94} className="h-2 bg-gray-100" />
                
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Anomalies</span>
                  <span className="text-sm font-medium text-amber-600">28 (2.3%)</span>
                </div>
                <Progress value={2.3} className="h-2 bg-gray-100" />
                
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Unmatched</span>
                  <span className="text-sm font-medium text-red-600">42 (3.4%)</span>
                </div>
                <Progress value={3.4} className="h-2 bg-gray-100" />
              </div>
              
              <div className="mt-6 flex justify-center">
                <div className="w-48 h-48 relative">
                  {/* This is a simplified chart representation */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-full h-full rounded-full border-8 border-green-500 opacity-20"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-[94%] h-[94%] rounded-full border-8 border-green-500"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-[89%] h-[89%] rounded-full border-8 border-amber-500"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-[86%] h-[86%] rounded-full border-8 border-red-500"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-3xl font-bold">94%</div>
                      <div className="text-sm text-gray-500">Match Rate</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Anomalies By Type</CardTitle>
              <CardDescription>
                Breakdown of detected anomalies
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                    <span className="text-sm">Amount Mismatch</span>
                  </div>
                  <span className="text-sm font-medium">12 (43%)</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                    <span className="text-sm">Date Mismatch</span>
                  </div>
                  <span className="text-sm font-medium">8 (29%)</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-amber-500 mr-2"></div>
                    <span className="text-sm">Customer Mismatch</span>
                  </div>
                  <span className="text-sm font-medium">5 (18%)</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                    <span className="text-sm">Currency Mismatch</span>
                  </div>
                  <span className="text-sm font-medium">3 (10%)</span>
                </div>
              </div>
              
              <div className="mt-6 flex justify-center">
                <div className="w-48 h-48 relative">
                  {/* This is a simplified chart representation */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-full h-full rounded-full border-[16px] border-blue-500 opacity-20"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-[85%] h-[85%] rounded-full border-[16px] border-purple-500 opacity-20"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-[70%] h-[70%] rounded-full border-[16px] border-amber-500 opacity-20"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-[55%] h-[55%] rounded-full border-[16px] border-green-500 opacity-20"></div>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <div className="text-3xl font-bold">28</div>
                      <div className="text-sm text-gray-500">Anomalies</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Panel */}
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Action Required</CardTitle>
            <CardDescription>
              Review and finalize the reconciliation process
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isComplete ? (
              <Alert className="bg-green-50 border-green-200">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertTitle className="text-green-800">Reconciliation Complete</AlertTitle>
                <AlertDescription className="text-green-700">
                  The reconciliation process has been successfully completed. You can now generate reports or export the data.
                </AlertDescription>
              </Alert>
            ) : (
              <>
                <Alert className="bg-amber-50 border-amber-200 mb-4">
                  <AlertTriangle className="h-4 w-4 text-amber-600" />
                  <AlertTitle className="text-amber-800">Unresolved Anomalies</AlertTitle>
                  <AlertDescription className="text-amber-700">
                    There are 28 anomalies that require review. 
                    <Button 
                      variant="link" 
                      className="text-amber-800 p-0 h-auto font-medium"
                      onClick={() => navigate('/reconciliation')}
                    >
                      Go fix them
                    </Button>
                  </AlertDescription>
                </Alert>
                
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-base font-medium">Ready to Finalize?</h3>
                    <p className="text-sm text-gray-500 mt-1">
                      You can proceed with unresolved anomalies, but they will be flagged in reports.
                    </p>
                  </div>
                  
                  <Button 
                    className="bg-teal hover:bg-teal/90"
                    onClick={handleFinalize}
                    disabled={isProcessing}
                  >
                    {isProcessing ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      <>
                        Finalize Reconciliation
                        <CheckCircle size={16} className="ml-2" />
                      </>
                    )}
                  </Button>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Download & Export */}
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Download & Export</CardTitle>
            <CardDescription>
              Export reconciled data in various formats
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" className="flex items-center justify-center gap-2" disabled={!isComplete}>
                <FileText size={16} />
                Download CSV
              </Button>
              
              <Button variant="outline" className="flex items-center justify-center gap-2" disabled={!isComplete}>
                <FileText size={16} />
                Download Excel
              </Button>
              
              <Button variant="outline" className="flex items-center justify-center gap-2" disabled={!isComplete}>
                <ExternalLink size={16} />
                Publish to BI Tools
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button variant="outline" onClick={() => navigate('/reconciliation')}>
            <ArrowLeft size={16} className="mr-2" />
            Back to Reconciliation
          </Button>
          
          <Button 
            onClick={() => navigate('/reports')}
            disabled={!isComplete}
            className={isComplete ? "bg-teal hover:bg-teal/90" : ""}
          >
            Configure Reports
            <ArrowRight size={16} className="ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ReconciliationSummary;
