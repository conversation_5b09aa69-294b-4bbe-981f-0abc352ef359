import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  ArrowRight,
  FileText,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Download,
  Filter,
  BarChart,
  Loader2,
  Settings
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  StandardizationResultsChart,
  AnomalyDetectionChart,
  ClassificationResultsChart,
  DataQualityScorecard
} from '@/components/DataCleaningVisualizations';

import dataCleaningService, {
  AnomalyDetectionResult,
  StandardizationResult,
  ClassificationR<PERSON>ult,
  QualityResult
} from '@/services/dataCleaningService';

import anomalyDetectionService, { JobStatus } from '@/services/anomalyDetectionService';
import JobStatusCard from '@/components/AnomalyDetection/JobStatusCard';

// Mock data for demonstration
const mockStandardizationData = {
  originalRows: 1240,
  standardizedRows: 1240,
  changes: {
    date_format: 156,
    case_normalization: 423,
    whitespace_removal: 892,
    currency_format: 245,
    numeric_format: 312,
    missing_values: 78,
    data_type_conversion: 45,
    text_normalization: 567
  },
  sampleData: [
    { id: 1, customer: "Acme Corp", amount: "$1,250.00", date: "2023-09-01" },
    { id: 2, customer: "Globex Inc", amount: "$750.50", date: "2023-09-05" },
    { id: 3, customer: "Stark Industries", amount: "$2,100.75", date: "2023-09-10" }
  ]
};

const mockAnomalyData = {
  totalRows: 1240,
  anomalyIndices: Array.from({ length: 42 }, (_, i) => i),
  anomaliesByMethod: {
    statistical: 28,
    isolation_forest: 35,
    local_outlier_factor: 22,
    domain_rules: 15
  },
  anomaliesByColumn: {
    amount: 18,
    date: 7,
    customer: 5,
    invoice_number: 3,
    payment_method: 9
  },
  anomalyRecords: [
    { id: 103, customer: "Wayne Enterprises", amount: "$15,750.00", date: "2023-09-15", anomaly_type: "statistical" },
    { id: 247, customer: "Umbrella Corp", amount: "$0.50", date: "2023-09-20", anomaly_type: "statistical" },
    { id: 382, customer: "Acme Corp", amount: "$1,500.00", date: "2023-12-25", anomaly_type: "domain_rules" }
  ],
  anomalyPercentage: 3.4
};

const mockClassificationData = {
  totalRows: 1240,
  processedRows: 500,
  classificationType: 'category',
  classifications: [
    { index: 1, text: "Invoice payment for Q3 services", category: "Invoice", confidence: 0.95, reasoning: "Contains 'invoice' keyword and refers to payment" },
    { index: 2, text: "Refund for returned product XYZ", category: "Refund", confidence: 0.92, reasoning: "Contains 'refund' keyword" },
    { index: 3, text: "Monthly subscription renewal", category: "Subscription", confidence: 0.88, reasoning: "Refers to recurring payment" }
  ],
  distribution: {
    "Invoice": 45.2,
    "Payment": 32.8,
    "Refund": 8.5,
    "Subscription": 10.3,
    "Other": 3.2
  },
  confidence: {
    "Invoice": 0.92,
    "Payment": 0.89,
    "Refund": 0.94,
    "Subscription": 0.87,
    "Other": 0.75
  }
};

const mockQualityData = {
  overallScore: 85,
  metrics: [
    { name: "Missing Values", score: 92, category: "Completeness", description: "Percentage of fields with non-null values" },
    { name: "Required Fields", score: 98, category: "Completeness", description: "Percentage of records with all required fields" },
    { name: "Value Accuracy", score: 78, category: "Accuracy", description: "Percentage of values matching expected patterns" },
    { name: "Range Validation", score: 85, category: "Accuracy", description: "Percentage of numeric values within expected ranges" },
    { name: "Format Consistency", score: 94, category: "Consistency", description: "Consistency of date and number formats" },
    { name: "Value Consistency", score: 88, category: "Consistency", description: "Consistency of categorical values" },
    { name: "Recency", score: 96, category: "Timeliness", description: "Percentage of records with recent timestamps" },
    { name: "Duplicate Records", score: 99, category: "Uniqueness", description: "Percentage of unique records" },
    { name: "Format Validity", score: 91, category: "Validity", description: "Percentage of values in valid formats" },
    { name: "Referential Integrity", score: 65, category: "Validity", description: "Percentage of references that match existing records" }
  ],
  recommendations: [
    "Improve referential integrity by ensuring all referenced IDs exist in the related tables",
    "Enhance value accuracy by implementing stricter validation rules for customer information",
    "Consider adding data type validation to prevent mixed formats in numeric fields"
  ]
};

const DataCleaningDashboard: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('standardization');

  // State for real data
  const [dataSourceId, setDataSourceId] = useState<number | null>(null);
  const [pipelineId, setPipelineId] = useState<number | null>(null);

  // State for data cleaning results
  const [anomalyData, setAnomalyData] = useState<AnomalyDetectionResult | null>(null);
  const [standardizationData, setStandardizationData] = useState<StandardizationResult | null>(null);
  const [classificationData, setClassificationData] = useState<ClassificationResult | null>(null);
  const [qualityData, setQualityData] = useState<QualityResult | null>(null);

  // Job status for anomaly detection
  const [anomalyJob, setAnomalyJob] = useState<JobStatus | null>(null);
  const [jobPollingInterval, setJobPollingInterval] = useState<NodeJS.Timeout | null>(null);

  // Loading and error states for each data type
  const [loadingStates, setLoadingStates] = useState({
    anomaly: false,
    standardization: false,
    classification: false,
    quality: false
  });

  const [errorStates, setErrorStates] = useState({
    anomaly: null as string | null,
    standardization: null as string | null,
    classification: null as string | null,
    quality: null as string | null
  });

  // Extract data source and pipeline IDs from URL query params
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const dsId = params.get('dataSourceId');
    const plId = params.get('pipelineId');
    const jobId = params.get('jobId');

    if (dsId) setDataSourceId(parseInt(dsId));
    if (plId) setPipelineId(parseInt(plId));

    // If we have a job ID, check its status
    if (jobId) {
      checkAnomalyJobStatus(jobId);
    }
    // If we have both IDs, fetch all data automatically
    else if (dsId && plId) {
      const dsIdNum = parseInt(dsId);
      const plIdNum = parseInt(plId);

      // Check for active anomaly detection jobs first
      checkActiveAnomalyJobs(plIdNum);

      // Fetch other data
      fetchStandardizationData(dsIdNum, plIdNum);
      fetchClassificationData(dsIdNum, plIdNum);
      fetchQualityData(dsIdNum, plIdNum);
    }

    // Cleanup polling interval when component unmounts
    return () => {
      if (jobPollingInterval) {
        clearInterval(jobPollingInterval);
      }
    };
  }, [location.search]);

  // Check for active anomaly detection jobs
  const checkActiveAnomalyJobs = async (plId: number) => {
    try {
      const activeJobs = await anomalyDetectionService.getActiveJobs(plId);
      if (activeJobs && activeJobs.length > 0) {
        // Get the most recent job
        const latestJob = activeJobs[0];
        setAnomalyJob(latestJob);

        // Start polling for job status updates
        startJobStatusPolling(latestJob.job_id);
      } else {
        // No active jobs, try to fetch completed results
        fetchCompletedAnomalyResults(plId);
      }
    } catch (err) {
      console.error('Error checking active jobs:', err);
      setErrorStates(prev => ({ ...prev, anomaly: 'Failed to check active anomaly detection jobs.' }));
    }
  };

  // Start a new anomaly detection job
  const startAnomalyDetection = async (dsId: number, plId: number) => {
    setLoadingStates(prev => ({ ...prev, anomaly: true }));
    setErrorStates(prev => ({ ...prev, anomaly: null }));

    try {
      // Start an asynchronous anomaly detection job
      const jobStatus = await anomalyDetectionService.detectAnomalies(dsId, plId, {
        methods: ['statistical', 'isolation_forest', 'local_outlier_factor', 'domain_rules'],
        statistical_method: 'z_score',
        threshold: 3.0,
        contamination: 0.05,
        save_output: true
      });

      setAnomalyJob(jobStatus);
      toast({
        title: "Anomaly detection started",
        description: `Job ID: ${jobStatus.job_id}. You can track the progress here.`,
      });

      // Update URL to include job ID
      const params = new URLSearchParams(location.search);
      params.set('jobId', jobStatus.job_id);
      navigate(`${location.pathname}?${params.toString()}`, { replace: true });

      // Start polling for job status updates
      startJobStatusPolling(jobStatus.job_id);
    } catch (err) {
      console.error('Error starting anomaly detection:', err);
      setErrorStates(prev => ({ ...prev, anomaly: 'Failed to start anomaly detection. Please try again.' }));
      toast({
        variant: "destructive",
        title: "Error starting anomaly detection",
        description: "There was a problem starting the anomaly detection job.",
      });
    } finally {
      setLoadingStates(prev => ({ ...prev, anomaly: false }));
    }
  };

  // Check the status of an anomaly detection job
  const checkAnomalyJobStatus = async (jobId: string) => {
    try {
      const jobStatus = await anomalyDetectionService.checkJobStatus(jobId);
      setAnomalyJob(jobStatus);

      // If the job is completed, fetch the results
      if (jobStatus.status === 'completed' && jobStatus.result_id) {
        fetchAnomalyResults(jobStatus.result_id);
      }
      // If the job failed, show an error
      else if (jobStatus.status === 'failed') {
        setErrorStates(prev => ({ ...prev, anomaly: `Anomaly detection job failed: ${jobStatus.message || 'Unknown error'}` }));
        toast({
          variant: "destructive",
          title: "Anomaly detection failed",
          description: jobStatus.message || "The anomaly detection job failed. Please try again.",
        });
      }
    } catch (err) {
      console.error('Error checking job status:', err);
      setErrorStates(prev => ({ ...prev, anomaly: 'Failed to check job status. Please try again.' }));
    }
  };

  // Start polling for job status updates
  const startJobStatusPolling = (jobId: string) => {
    // Clear any existing polling interval
    if (jobPollingInterval) {
      clearInterval(jobPollingInterval);
    }

    // Set up a new polling interval
    const interval = setInterval(async () => {
      try {
        const jobStatus = await anomalyDetectionService.checkJobStatus(jobId);
        setAnomalyJob(jobStatus);

        // If the job is completed or failed, stop polling
        if (jobStatus.status === 'completed' || jobStatus.status === 'failed') {
          clearInterval(interval);
          setJobPollingInterval(null);

          // If the job is completed, fetch the results
          if (jobStatus.status === 'completed' && jobStatus.result_id) {
            fetchAnomalyResults(jobStatus.result_id);

            // Remove job ID from URL
            const params = new URLSearchParams(location.search);
            params.delete('jobId');
            navigate(`${location.pathname}?${params.toString()}`, { replace: true });

            toast({
              title: "Anomaly detection complete",
              description: "The anomaly detection job has completed successfully.",
            });
          }
          // If the job failed, show an error
          else if (jobStatus.status === 'failed') {
            setErrorStates(prev => ({ ...prev, anomaly: `Anomaly detection job failed: ${jobStatus.message || 'Unknown error'}` }));
            toast({
              variant: "destructive",
              title: "Anomaly detection failed",
              description: jobStatus.message || "The anomaly detection job failed. Please try again.",
            });
          }
        }
      } catch (err) {
        console.error('Error polling job status:', err);
        clearInterval(interval);
        setJobPollingInterval(null);
      }
    }, 5000); // Poll every 5 seconds

    setJobPollingInterval(interval);
  };

  // Fetch anomaly detection results by result ID
  const fetchAnomalyResults = async (resultId: string) => {
    setLoadingStates(prev => ({ ...prev, anomaly: true }));

    try {
      const result = await anomalyDetectionService.getAnomalyDetectionResults(resultId);
      setAnomalyData(result);
      toast({
        title: "Anomaly detection results loaded",
        description: `Found ${result.anomaly_indices.length} anomalies in ${result.total_rows} records.`,
      });
    } catch (err) {
      console.error('Error fetching anomaly results:', err);
      setErrorStates(prev => ({ ...prev, anomaly: 'Failed to fetch anomaly detection results. Please try again.' }));
    } finally {
      setLoadingStates(prev => ({ ...prev, anomaly: false }));
    }
  };

  // Fetch completed anomaly detection results for a pipeline
  const fetchCompletedAnomalyResults = async (plId: number) => {
    if (!dataSourceId) return;

    setLoadingStates(prev => ({ ...prev, anomaly: true }));

    try {
      // Get history of anomaly detection jobs for this data source
      const history = await anomalyDetectionService.getAnomalyDetectionHistory(dataSourceId);

      // Find the most recent completed job for this pipeline
      const completedJob = history.find(job =>
        job.pipeline_id === plId &&
        job.status === 'completed' &&
        job.result
      );

      if (completedJob && completedJob.result) {
        setAnomalyData(completedJob.result);
        toast({
          title: "Loaded previous anomaly detection results",
          description: `Found ${completedJob.result.anomaly_indices.length} anomalies in ${completedJob.result.total_rows} records.`,
        });
      } else {
        // No completed jobs found, show a message
        setErrorStates(prev => ({ ...prev, anomaly: 'No anomaly detection results found. Run a new detection to see results.' }));
      }
    } catch (err) {
      console.error('Error fetching completed anomaly results:', err);
      setErrorStates(prev => ({ ...prev, anomaly: 'Failed to fetch previous anomaly detection results.' }));

      // Fallback to mock data in development
      if (import.meta.env.DEV) {
        setAnomalyData({
          total_rows: mockAnomalyData.totalRows,
          anomaly_indices: mockAnomalyData.anomalyIndices,
          anomalies_by_method: mockAnomalyData.anomaliesByMethod,
          anomalies_by_column: mockAnomalyData.anomaliesByColumn,
          anomaly_records: mockAnomalyData.anomalyRecords,
          anomaly_percentage: mockAnomalyData.anomalyPercentage
        });
      }
    } finally {
      setLoadingStates(prev => ({ ...prev, anomaly: false }));
    }
  };

  // Fetch standardization data from the API
  const fetchStandardizationData = async (dsId: number, plId: number) => {
    setLoadingStates(prev => ({ ...prev, standardization: true }));
    setErrorStates(prev => ({ ...prev, standardization: null }));

    try {
      // Call the API
      const result = await dataCleaningService.standardizeData(dsId, plId);
      setStandardizationData(result);
      toast({
        title: "Standardization complete",
        description: `Applied ${Object.values(result.changes).reduce((a, b) => a + b, 0)} changes to ${result.standardizedRows} records.`,
      });
    } catch (err) {
      console.error('Error fetching standardization data:', err);
      setErrorStates(prev => ({ ...prev, standardization: 'Failed to fetch standardization data. Please try again.' }));
      toast({
        variant: "destructive",
        title: "Error fetching standardization data",
        description: "There was a problem retrieving standardization results.",
      });

      // Fallback to mock data in development
      if (import.meta.env.DEV) {
        setStandardizationData(mockStandardizationData);
      }
    } finally {
      setLoadingStates(prev => ({ ...prev, standardization: false }));
    }
  };

  // Fetch classification data from the API
  const fetchClassificationData = async (dsId: number, plId: number) => {
    setLoadingStates(prev => ({ ...prev, classification: true }));
    setErrorStates(prev => ({ ...prev, classification: null }));

    try {
      // Call the API
      const result = await dataCleaningService.classifyData(dsId, plId);
      setClassificationData(result);
      toast({
        title: "Classification complete",
        description: `Classified ${result.processedRows} out of ${result.totalRows} records.`,
      });
    } catch (err) {
      console.error('Error fetching classification data:', err);
      setErrorStates(prev => ({ ...prev, classification: 'Failed to fetch classification data. Please try again.' }));
      toast({
        variant: "destructive",
        title: "Error fetching classification data",
        description: "There was a problem retrieving classification results.",
      });

      // Fallback to mock data in development
      if (import.meta.env.DEV) {
        setClassificationData(mockClassificationData);
      }
    } finally {
      setLoadingStates(prev => ({ ...prev, classification: false }));
    }
  };

  // Fetch quality data from the API
  const fetchQualityData = async (dsId: number, plId: number) => {
    setLoadingStates(prev => ({ ...prev, quality: true }));
    setErrorStates(prev => ({ ...prev, quality: null }));

    try {
      // Call the API
      const result = await dataCleaningService.assessQuality(dsId, plId);
      setQualityData(result);
      toast({
        title: "Quality assessment complete",
        description: `Overall quality score: ${result.overallScore}/100`,
      });
    } catch (err) {
      console.error('Error fetching quality data:', err);
      setErrorStates(prev => ({ ...prev, quality: 'Failed to fetch quality data. Please try again.' }));
      toast({
        variant: "destructive",
        title: "Error fetching quality data",
        description: "There was a problem retrieving quality results.",
      });

      // Fallback to mock data in development
      if (import.meta.env.DEV) {
        setQualityData(mockQualityData);
      }
    } finally {
      setLoadingStates(prev => ({ ...prev, quality: false }));
    }
  };

  // Handle refresh button click
  const handleRefresh = () => {
    if (dataSourceId && pipelineId) {
      // Refresh data based on active tab
      switch (activeTab) {
        case 'anomalies':
          // If there's an active job, check its status
          if (anomalyJob && (anomalyJob.status === 'pending' || anomalyJob.status === 'processing')) {
            checkAnomalyJobStatus(anomalyJob.job_id);
          } else {
            // Otherwise, check for active jobs or fetch completed results
            checkActiveAnomalyJobs(pipelineId);
          }
          break;
        case 'standardization':
          fetchStandardizationData(dataSourceId, pipelineId);
          break;
        case 'classification':
          fetchClassificationData(dataSourceId, pipelineId);
          break;
        case 'quality':
          fetchQualityData(dataSourceId, pipelineId);
          break;
        default:
          // Refresh all data
          if (anomalyJob && (anomalyJob.status === 'pending' || anomalyJob.status === 'processing')) {
            checkAnomalyJobStatus(anomalyJob.job_id);
          } else {
            checkActiveAnomalyJobs(pipelineId);
          }
          fetchStandardizationData(dataSourceId, pipelineId);
          fetchClassificationData(dataSourceId, pipelineId);
          fetchQualityData(dataSourceId, pipelineId);
      }
    } else {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Data source or pipeline ID is missing.",
      });
    }
  };

  // Handle starting a new anomaly detection job
  const handleStartAnomalyDetection = () => {
    if (dataSourceId && pipelineId) {
      startAnomalyDetection(dataSourceId, pipelineId);
    } else {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Data source or pipeline ID is missing.",
      });
    }
  };

  // Handle canceling an anomaly detection job
  const handleCancelJob = async () => {
    if (anomalyJob) {
      try {
        await anomalyDetectionService.cancelJob(anomalyJob.job_id);
        toast({
          title: "Job canceled",
          description: "The anomaly detection job has been canceled.",
        });

        // Clear the job status and stop polling
        setAnomalyJob(null);
        if (jobPollingInterval) {
          clearInterval(jobPollingInterval);
          setJobPollingInterval(null);
        }

        // Remove job ID from URL
        const params = new URLSearchParams(location.search);
        params.delete('jobId');
        navigate(`${location.pathname}?${params.toString()}`, { replace: true });
      } catch (err) {
        console.error('Error canceling job:', err);
        toast({
          variant: "destructive",
          title: "Error canceling job",
          description: "There was a problem canceling the anomaly detection job.",
        });
      }
    }
  };

  const handleExport = () => {
    // Determine which data to export based on active tab
    let exportData: any;
    let filename: string;

    switch (activeTab) {
      case 'standardization':
        exportData = {
          originalRows: mockStandardizationData.originalRows,
          standardizedRows: mockStandardizationData.standardizedRows,
          changes: mockStandardizationData.changes,
          sampleData: mockStandardizationData.sampleData
        };
        filename = 'data-cleaning-standardization.json';
        break;
      case 'anomalies':
        exportData = {
          totalRows: mockAnomalyData.totalRows,
          anomalyCount: mockAnomalyData.anomalyIndices.length,
          anomaliesByMethod: mockAnomalyData.anomaliesByMethod,
          anomaliesByColumn: mockAnomalyData.anomaliesByColumn,
          anomalyRecords: mockAnomalyData.anomalyRecords,
          anomalyPercentage: mockAnomalyData.anomalyPercentage
        };
        filename = 'data-cleaning-anomalies.json';
        break;
      case 'classification':
        exportData = mockClassificationData;
        filename = 'data-cleaning-classification.json';
        break;
      case 'quality':
        exportData = mockQualityData;
        filename = 'data-cleaning-quality.json';
        break;
      default:
        exportData = {
          standardization: mockStandardizationData,
          anomalies: mockAnomalyData,
          classification: mockClassificationData,
          quality: mockQualityData
        };
        filename = 'data-cleaning-results.json';
    }

    // Create a JSON blob and download it
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              className="mr-4"
              onClick={() => navigate('/data-pipeline')}
            >
              <ArrowLeft size={16} className="mr-2" />
              Back to Pipeline
            </Button>
            <h1 className="text-3xl font-bold text-navy">Data Cleaning Results</h1>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={Object.values(loadingStates).some(Boolean)}
            >
              {Object.values(loadingStates).some(Boolean) ? (
                <RefreshCw size={16} className="mr-2 animate-spin" />
              ) : (
                <RefreshCw size={16} className="mr-2" />
              )}
              Refresh
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleExport}
            >
              <Download size={16} className="mr-2" />
              Export
            </Button>

            {activeTab === 'anomalies' && (
              <Button
                size="sm"
                className="bg-teal hover:bg-teal/90"
                onClick={() => {
                  const params = new URLSearchParams();
                  if (dataSourceId) params.append('dataSourceId', dataSourceId.toString());
                  if (pipelineId) params.append('pipelineId', pipelineId.toString());
                  navigate(`/anomaly-detection?${params.toString()}`);
                }}
              >
                <Settings size={16} className="mr-2" />
                Configure Detection
              </Button>
            )}
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card className={`cursor-pointer ${activeTab === 'standardization' ? 'border-blue-500 bg-blue-50' : ''}`}
                onClick={() => setActiveTab('standardization')}>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Standardization</p>
                  <p className="text-2xl font-bold">
                    {loadingStates.standardization ? (
                      <RefreshCw size={20} className="animate-spin" />
                    ) : standardizationData ? (
                      Object.values(standardizationData.changes).reduce((a: number, b: number) => a + b, 0).toLocaleString()
                    ) : (
                      Object.values(mockStandardizationData.changes).reduce((a, b) => a + b, 0).toLocaleString()
                    )}
                  </p>
                  <p className="text-sm text-gray-500">changes applied</p>
                </div>
                <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <FileText className="h-5 w-5 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className={`cursor-pointer ${activeTab === 'anomalies' ? 'border-amber-500 bg-amber-50' : ''}`}
                onClick={() => setActiveTab('anomalies')}>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Anomalies</p>
                  <p className="text-2xl font-bold">
                    {loadingStates.anomaly ? (
                      <RefreshCw size={20} className="animate-spin" />
                    ) : anomalyData ? (
                      anomalyData.anomaly_indices.length
                    ) : (
                      mockAnomalyData.anomalyIndices.length
                    )}
                  </p>
                  <p className="text-sm text-gray-500">
                    detected ({loadingStates.anomaly ? '...' : anomalyData ?
                      anomalyData.anomaly_percentage.toFixed(1) :
                      mockAnomalyData.anomalyPercentage.toFixed(1)}%)
                  </p>
                </div>
                <div className="h-10 w-10 bg-amber-100 rounded-full flex items-center justify-center">
                  <AlertTriangle className="h-5 w-5 text-amber-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className={`cursor-pointer ${activeTab === 'classification' ? 'border-green-500 bg-green-50' : ''}`}
                onClick={() => setActiveTab('classification')}>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Classification</p>
                  <p className="text-2xl font-bold">
                    {loadingStates.classification ? (
                      <RefreshCw size={20} className="animate-spin" />
                    ) : classificationData ? (
                      classificationData.processedRows
                    ) : (
                      mockClassificationData.processedRows
                    )}
                  </p>
                  <p className="text-sm text-gray-500">records classified</p>
                </div>
                <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className={`cursor-pointer ${activeTab === 'quality' ? 'border-purple-500 bg-purple-50' : ''}`}
                onClick={() => setActiveTab('quality')}>
            <CardContent className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-gray-500">Quality Score</p>
                  <p className="text-2xl font-bold">
                    {loadingStates.quality ? (
                      <RefreshCw size={20} className="animate-spin" />
                    ) : qualityData ? (
                      `${qualityData.overallScore}/100`
                    ) : (
                      `${mockQualityData.overallScore}/100`
                    )}
                  </p>
                  <p className="text-sm text-gray-500">overall quality</p>
                </div>
                <div className="h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center">
                  <BarChart className="h-5 w-5 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="mb-6">
          {activeTab === 'standardization' && (
            loadingStates.standardization ? (
              <div className="flex flex-col items-center justify-center py-16">
                <Loader2 size={48} className="animate-spin text-teal mb-4" />
                <p className="text-lg text-gray-600">Loading standardization data...</p>
                <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
              </div>
            ) : errorStates.standardization ? (
              <Alert variant="destructive" className="my-8">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{errorStates.standardization}</AlertDescription>
              </Alert>
            ) : standardizationData ? (
              <StandardizationResultsChart
                originalRows={standardizationData.originalRows}
                standardizedRows={standardizationData.standardizedRows}
                changes={standardizationData.changes}
                sampleData={standardizationData.sampleData}
              />
            ) : (
              <StandardizationResultsChart
                originalRows={mockStandardizationData.originalRows}
                standardizedRows={mockStandardizationData.standardizedRows}
                changes={mockStandardizationData.changes}
                sampleData={mockStandardizationData.sampleData}
              />
            )
          )}

          {activeTab === 'anomalies' && (
            <div>
              {/* Show job status card if there's an active job */}
              {anomalyJob && (anomalyJob.status === 'pending' || anomalyJob.status === 'processing') && (
                <JobStatusCard
                  job={anomalyJob}
                  onRefresh={() => checkAnomalyJobStatus(anomalyJob.job_id)}
                  onCancel={handleCancelJob}
                  onViewResults={() => {
                    if (anomalyJob.result_id) {
                      fetchAnomalyResults(anomalyJob.result_id);
                    }
                  }}
                />
              )}

              {/* Show loading state */}
              {loadingStates.anomaly ? (
                <div className="flex flex-col items-center justify-center py-16">
                  <Loader2 size={48} className="animate-spin text-teal mb-4" />
                  <p className="text-lg text-gray-600">Running anomaly detection algorithms...</p>
                  <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
                </div>
              ) : errorStates.anomaly ? (
                <div className="space-y-4">
                  <Alert variant="destructive" className="my-4">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertTitle>Error</AlertTitle>
                    <AlertDescription>{errorStates.anomaly}</AlertDescription>
                  </Alert>

                  {/* Show button to start a new detection */}
                  <div className="flex justify-center">
                    <Button
                      onClick={handleStartAnomalyDetection}
                      className="bg-teal hover:bg-teal/90"
                    >
                      <RefreshCw size={16} className="mr-2" />
                      Run Anomaly Detection
                    </Button>
                  </div>
                </div>
              ) : anomalyData ? (
                <AnomalyDetectionChart
                  totalRows={anomalyData.total_rows}
                  anomalyIndices={anomalyData.anomaly_indices}
                  anomaliesByMethod={anomalyData.anomalies_by_method}
                  anomaliesByColumn={anomalyData.anomalies_by_column}
                  anomalyRecords={anomalyData.anomaly_records}
                  anomalyPercentage={anomalyData.anomaly_percentage}
                />
              ) : (
                <div className="flex flex-col items-center justify-center py-16 space-y-6">
                  <div className="text-center">
                    <AlertTriangle size={48} className="text-amber-500 mx-auto mb-4" />
                    <p className="text-lg text-gray-600">No anomaly detection results available</p>
                    <p className="text-sm text-gray-500 mt-2">Run a new detection to see results</p>
                  </div>

                  <Button
                    onClick={handleStartAnomalyDetection}
                    className="bg-teal hover:bg-teal/90"
                  >
                    <RefreshCw size={16} className="mr-2" />
                    Run Anomaly Detection
                  </Button>
                </div>
              )}
            </div>
          )}

          {activeTab === 'classification' && (
            loadingStates.classification ? (
              <div className="flex flex-col items-center justify-center py-16">
                <Loader2 size={48} className="animate-spin text-teal mb-4" />
                <p className="text-lg text-gray-600">Loading classification data...</p>
                <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
              </div>
            ) : errorStates.classification ? (
              <Alert variant="destructive" className="my-8">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{errorStates.classification}</AlertDescription>
              </Alert>
            ) : classificationData ? (
              <ClassificationResultsChart
                totalRows={classificationData.totalRows}
                processedRows={classificationData.processedRows}
                classificationType={classificationData.classificationType}
                classifications={classificationData.classifications}
                distribution={classificationData.distribution}
                confidence={classificationData.confidence}
              />
            ) : (
              <ClassificationResultsChart
                totalRows={mockClassificationData.totalRows}
                processedRows={mockClassificationData.processedRows}
                classificationType={mockClassificationData.classificationType}
                classifications={mockClassificationData.classifications}
                distribution={mockClassificationData.distribution}
                confidence={mockClassificationData.confidence}
              />
            )
          )}

          {activeTab === 'quality' && (
            loadingStates.quality ? (
              <div className="flex flex-col items-center justify-center py-16">
                <Loader2 size={48} className="animate-spin text-teal mb-4" />
                <p className="text-lg text-gray-600">Loading quality data...</p>
                <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
              </div>
            ) : errorStates.quality ? (
              <Alert variant="destructive" className="my-8">
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{errorStates.quality}</AlertDescription>
              </Alert>
            ) : qualityData ? (
              <DataQualityScorecard
                overallScore={qualityData.overallScore}
                metrics={qualityData.metrics}
                recommendations={qualityData.recommendations}
              />
            ) : (
              <DataQualityScorecard
                overallScore={mockQualityData.overallScore}
                metrics={mockQualityData.metrics}
                recommendations={mockQualityData.recommendations}
              />
            )
          )}
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <Button variant="outline" onClick={() => navigate('/data-pipeline')}>
            <ArrowLeft size={16} className="mr-2" />
            Back to Pipeline
          </Button>

          <Button
            className="bg-teal hover:bg-teal/90"
            onClick={() => navigate('/reconciliation')}
          >
            Proceed to Reconciliation
            <ArrowRight size={16} className="ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DataCleaningDashboard;
