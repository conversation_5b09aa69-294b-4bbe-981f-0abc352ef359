"""
Example usage of the advanced matching engine.
"""

import json
from typing import Dict, List, Any

from app.services.reconciliation.advanced_matching import match_records


def run_example():
    """
    Run an example of the matching engine.
    """
    # Sample data
    data_a = [
        {"id": 1, "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "amount": 1000.0},
        {"id": 2, "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>", "amount": 2500.0},
        {"id": 3, "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "amount": 750.0},
        {"id": 4, "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "amount": 1200.0},
        {"id": 5, "first_name": "<PERSON>", "last_name": "<PERSON>", "email": "<EMAIL>", "amount": 3000.0}
    ]
    
    data_b = [
        {"id": 101, "name": "<PERSON>", "email": "<EMAIL>", "transaction": 1000.0},
        {"id": 102, "name": "Jane <PERSON>e", "email": "<EMAIL>", "transaction": 2500.0},
        {"id": 103, "name": "Bob <PERSON>", "email": "<EMAIL>", "transaction": 750.0},
        {"id": 104, "name": "Emily W.", "email": "<EMAIL>", "transaction": 1200.0},
        {"id": 105, "name": "Mike Brown", "email": "<EMAIL>", "transaction": 3000.0},
        {"id": 106, "name": "Sarah Wilson", "email": "<EMAIL>", "transaction": 1500.0}
    ]
    
    # Configuration for matching
    config = {
        "methods": ["tfidf", "phonetic_name"],
        "field_mappings": {
            "first_name": "name",
            "last_name": "name",
            "email": "email",
            "amount": "transaction"
        },
        "thresholds": {
            "tfidf": 0.6,
            "phonetic_name": 0.7,
            "anomaly_factor": 0.7
        },
        "weights": {
            "email": {"email": 0.8},
            "amount": {"transaction": 0.5}
        },
        "name_fields": {
            "source": ["first_name", "last_name"],
            "target": ["name"]
        },
        "max_matches": 1
    }
    
    # Run matching
    results = match_records(data_a, data_b, config)
    
    # Print results
    print("Matching Results:")
    print(f"Total matches: {results['stats']['total_matches']}")
    print(f"Total anomalies: {results['stats']['total_anomalies']}")
    print("\nMatches by method:")
    for method, stats in results['stats']['methods'].items():
        print(f"  {method}: {stats['matches']} matches, {stats['anomalies']} anomalies")
    
    print("\nMatched Records:")
    for i, match in enumerate(results['matches']):
        print(f"\nMatch {i+1}:")
        print(f"  Source: {match['source_record']['first_name']} {match['source_record']['last_name']} ({match['source_record']['email']})")
        print(f"  Target: {match['target_record']['name']} ({match['target_record']['email']})")
        print(f"  Confidence: {match['confidence']:.2f}")
        print(f"  Method: {match['match_method']}")
    
    print("\nAnomalies:")
    for i, anomaly in enumerate(results['anomalies']):
        print(f"\nAnomaly {i+1}:")
        print(f"  Source: {anomaly['source_record']['first_name']} {anomaly['source_record']['last_name']}")
        print(f"  Target: {anomaly['target_record']['name']}")
        print(f"  Confidence: {anomaly['confidence']:.2f}")
        print(f"  Method: {anomaly['match_method']}")
        print(f"  Anomaly Type: {anomaly.get('anomaly_type', 'unknown')}")


if __name__ == "__main__":
    run_example()
