import React from 'react';
import { Card, CardContent } from '../ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { Loader2, AlertTriangle, FileSpreadsheet, RefreshCw } from 'lucide-react';
import { Button } from '../ui/button';
import { PreviewResult } from '../../services/semanticMappingService';

interface MappingPreviewProps {
  previewData: PreviewResult | null;
  isLoading: boolean;
  onRefresh?: () => void;
}

const MappingPreview: React.FC<MappingPreviewProps> = ({
  previewData,
  isLoading,
  onRefresh
}) => {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
        <span className="ml-2 text-gray-500">Generating preview...</span>
      </div>
    );
  }

  if (!previewData || !previewData.preview || previewData.preview.length === 0) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>No preview available</AlertTitle>
        <AlertDescription>
          Create field mappings to see a preview of the combined data.
        </AlertDescription>
      </Alert>
    );
  }

  const { preview, statistics } = previewData;
  const columns = Object.keys(preview[0] || {});

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <FileSpreadsheet className="h-5 w-5 mr-2 text-blue-500" />
            <span className="font-medium">Combined Data Preview</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex space-x-2">
              <Badge variant="outline">
                {statistics.row_count} rows
              </Badge>
              <Badge variant="outline">
                {statistics.column_count} columns
              </Badge>
            </div>
            {onRefresh && (
              <Button variant="ghost" size="sm" onClick={onRefresh} disabled={isLoading}>
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>
            )}
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map(column => (
                  <TableHead key={column} className="whitespace-nowrap">
                    <div className="flex items-center">
                      <span>{column}</span>
                      {statistics.null_counts[column] > 0 && (
                        <Badge variant="outline" className="ml-2 text-xs">
                          {Math.round((statistics.null_counts[column] / statistics.row_count) * 100)}% null
                        </Badge>
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {preview.map((row, rowIndex) => (
                <TableRow key={rowIndex}>
                  {columns.map(column => (
                    <TableCell key={`${rowIndex}-${column}`} className="whitespace-nowrap">
                      {row[column] !== null ? String(row[column]) : (
                        <span className="text-gray-400 italic">null</span>
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {statistics.row_count > preview.length && (
          <div className="mt-2 text-sm text-gray-500 text-center">
            Showing {preview.length} of {statistics.row_count} rows
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MappingPreview;
