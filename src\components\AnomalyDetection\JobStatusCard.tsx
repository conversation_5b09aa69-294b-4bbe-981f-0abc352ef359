import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Loader2, 
  CheckCircle2, 
  AlertCircle, 
  Clock, 
  XCircle,
  RefreshCw
} from 'lucide-react';
import { JobStatus } from '@/services/anomalyDetectionService';

interface JobStatusCardProps {
  job: JobStatus;
  onRefresh: () => void;
  onCancel: () => void;
  onViewResults: () => void;
}

const JobStatusCard: React.FC<JobStatusCardProps> = ({ 
  job, 
  onRefresh, 
  onCancel, 
  onViewResults 
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  const getStatusIcon = () => {
    switch (job.status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'processing':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle2 className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = () => {
    switch (job.status) {
      case 'pending':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Pending</Badge>;
      case 'processing':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Processing</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Completed</Badge>;
      case 'failed':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Failed</Badge>;
      default:
        return null;
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="flex items-center">
              {getStatusIcon()}
              <span className="ml-2">Anomaly Detection Job</span>
            </CardTitle>
            <CardDescription>
              Job ID: {job.job_id}
            </CardDescription>
          </div>
          {getStatusBadge()}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between items-center mb-1">
              <span className="text-sm font-medium">Progress</span>
              <span className="text-sm text-gray-500">{job.progress}%</span>
            </div>
            <Progress value={job.progress} className="h-2" />
          </div>
          
          {job.message && (
            <div className="text-sm text-gray-600">
              {job.message}
            </div>
          )}
          
          <div className="text-xs text-gray-500">
            Started: {formatDate(job.created_at)}
          </div>
          
          <div className="flex justify-end space-x-2 pt-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onRefresh}
              disabled={job.status === 'completed'}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Refresh
            </Button>
            
            {(job.status === 'pending' || job.status === 'processing') && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onCancel}
              >
                <XCircle className="h-4 w-4 mr-1" />
                Cancel
              </Button>
            )}
            
            {job.status === 'completed' && (
              <Button 
                size="sm"
                className="bg-teal hover:bg-teal/90"
                onClick={onViewResults}
              >
                View Results
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default JobStatusCard;
