import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { useToast } from '../components/ui/use-toast';
import ConfirmationModal from '../components/ConfirmationModal';
import SchemaReviewPanel from '../components/SchemaReviewPanel';
import { Dialog, DialogContent } from '../components/ui/dialog';
import {
  ArrowLeft,
  RefreshCw,
  AlertTriangle,
  FileSpreadsheet,
  BarChart,
  Download,
  FileText,
  Database,
  LineChart,
  RotateCcw
} from 'lucide-react';
import fileService, {
  CombinedDataset,
  AnomalyResult,
  DataSummary,
  DataAnalysis
} from '../services/excelService';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Badge } from '../components/ui/badge';
import DataSummaryView from '../components/DataSummaryView';

const CombinedAnalysis = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [combinedDataset, setCombinedDataset] = useState<CombinedDataset | null>(null);
  const [sourceData, setSourceData] = useState<any[]>([]);
  const [anomalyResult, setAnomalyResult] = useState<AnomalyResult | null>(null);
  const [activeTab, setActiveTab] = useState('data');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSummary, setDataSummary] = useState<DataSummary | null>(null);
  const [dataAnalysis, setDataAnalysis] = useState<DataAnalysis | null>(null);
  const [isSummaryLoading, setIsSummaryLoading] = useState<boolean>(false);
  const [isReprocessing, setIsReprocessing] = useState<boolean>(false);
  const [showReprocessModal, setShowReprocessModal] = useState<boolean>(false);
  const [fieldMappings, setFieldMappings] = useState<any[]>([]);
  const [showSchemaReviewModal, setShowSchemaReviewModal] = useState<boolean>(false);

  useEffect(() => {
    if (!id) return;

    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch combined dataset info
        const dataset = await fileService.getCombinedDataset(id);
        setCombinedDataset(dataset);

        // Fetch source data
        const { data } = await fileService.getCombinedDatasetData(id, 100);
        setSourceData(data);

        // Check if there are any anomaly results for this combined dataset
        const results = await fileService.getAnomalyResults();
        const combinedResults = Object.values(results).filter(
          result => result.combined_dataset_id === id
        );

        if (combinedResults.length > 0) {
          // Get the most recent result
          setAnomalyResult(combinedResults[0]);
        }

        // Fetch data summary and analysis
        fetchDataSummary();
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          variant: "destructive",
          title: "Error fetching data",
          description: "There was a problem loading the data. Please try again.",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [id, toast]);

  const fetchDataSummary = async () => {
    if (!id) return;

    setIsSummaryLoading(true);
    try {
      // Fetch data summary
      const summary = await fileService.getDatasetSummary(id);
      setDataSummary(summary);

      // Fetch data analysis
      const analysis = await fileService.analyzeDataset(id);
      setDataAnalysis(analysis);
    } catch (error) {
      console.error('Error fetching data summary and analysis:', error);
      toast({
        variant: "destructive",
        title: "Error analyzing data",
        description: "There was a problem analyzing the data. Please try again.",
      });
    } finally {
      setIsSummaryLoading(false);
    }
  };

  const handleDetectAnomalies = async () => {
    if (!id || !combinedDataset) return;

    setIsLoading(true);
    try {
      // Get numerical columns
      const numericalColumns = Object.keys(sourceData[0] || {}).filter(key => {
        const value = sourceData[0][key];
        return typeof value === 'number' && !key.startsWith('_');
      });

      // Detect anomalies
      const result = await fileService.detectAnomaliesCombined(id, numericalColumns);
      setAnomalyResult(result);

      toast({
        title: "Anomaly detection complete",
        description: `Found ${result.anomaly_count} anomalies in ${result.total_rows} records.`,
      });

      // Switch to anomalies tab
      setActiveTab('anomalies');
    } catch (error) {
      console.error('Error detecting anomalies:', error);
      toast({
        variant: "destructive",
        title: "Error detecting anomalies",
        description: "There was a problem detecting anomalies. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportData = () => {
    if (!combinedDataset) return;

    let dataToExport: any;
    let filename: string;

    switch (activeTab) {
      case 'data':
        dataToExport = sourceData;
        filename = `${combinedDataset.name}-data.json`;
        break;
      case 'anomalies':
        dataToExport = anomalyResult;
        filename = `${combinedDataset.name}-anomalies.json`;
        break;
      default:
        dataToExport = sourceData;
        filename = `${combinedDataset.name}-data.json`;
    }

    // Create a JSON blob and download it
    const blob = new Blob([JSON.stringify(dataToExport, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleReprocessDataset = async () => {
    if (!id || !combinedDataset) return;

    setIsReprocessing(true);
    setShowReprocessModal(false);

    try {
      // Get field mappings for each source
      const mappings: any[] = [];
      for (const sourceId of combinedDataset.source_ids) {
        try {
          // Get schema inference for this source
          const schemaResult = await fileService.inferSchema(sourceId);
          if (schemaResult && schemaResult.field_mappings) {
            // Get sample data for this source to enhance the field mappings with sample values
            try {
              const sourceData = await fileService.getDataSourceData(sourceId, 10);
              if (sourceData && sourceData.data && sourceData.data.length > 0) {
                // Add sample values to each field mapping if not already present
                schemaResult.field_mappings.forEach((mapping: any) => {
                  if (!mapping.sample_values || mapping.sample_values.length === 0) {
                    const sourceField = mapping.source_field;
                    const sampleValues = sourceData.data
                      .map((row: any) => row[sourceField])
                      .filter((val: any) => val !== undefined && val !== null)
                      .slice(0, 5);
                    mapping.sample_values = sampleValues;
                  }
                });
              }
            } catch (dataError) {
              console.error(`Error getting sample data for source ${sourceId}:`, dataError);
            }

            mappings.push(...schemaResult.field_mappings);
          }
        } catch (error) {
          console.error(`Error getting schema for source ${sourceId}:`, error);
        }
      }

      if (mappings.length > 0) {
        // Store mappings and show schema review modal
        setFieldMappings(mappings);
        setShowSchemaReviewModal(true);
      } else {
        // If no mappings could be retrieved, show an error
        toast({
          variant: "destructive",
          title: "Error retrieving field mappings",
          description: "Could not retrieve field mappings for the dataset sources. Please try again.",
        });
        setIsReprocessing(false);
      }
    } catch (error) {
      console.error('Error preparing for reprocessing:', error);
      toast({
        variant: "destructive",
        title: "Error preparing for reprocessing",
        description: "There was a problem preparing the dataset for reprocessing. Please try again.",
      });
      setIsReprocessing(false);
    }
  };

  const handleConfirmReprocessing = async (updatedMappings: any[]) => {
    if (!id || !combinedDataset) return;

    try {
      // Get the current join strategy or default to 'smart'
      const joinStrategy = combinedDataset.join_strategy || 'smart';

      // Call the reprocess endpoint with the updated field mappings
      const result = await fileService.reprocessCombinedDataset(id, joinStrategy as any, updatedMappings);
      setCombinedDataset(result);

      // Refresh the data
      const { data } = await fileService.getCombinedDatasetData(id, 100);
      setSourceData(data);

      // Reset anomaly result since the dataset has changed
      setAnomalyResult(null);

      // Refresh summary and analysis
      fetchDataSummary();

      toast({
        title: "Dataset reprocessed successfully",
        description: `The dataset "${result.name}" has been reprocessed with ${result.row_count} rows.`,
      });
    } catch (error) {
      console.error('Error reprocessing dataset:', error);
      toast({
        variant: "destructive",
        title: "Error reprocessing dataset",
        description: "There was a problem reprocessing the dataset. Please try again.",
      });
    } finally {
      setIsReprocessing(false);
      setShowSchemaReviewModal(false);
    }
  };

  const handleCancelReprocessing = () => {
    setIsReprocessing(false);
    setShowSchemaReviewModal(false);
  };

  // Helper function to render table headers from data
  const renderTableHeaders = (data: any[]) => {
    if (!data || data.length === 0) return null;

    const headers = Object.keys(data[0]);
    return (
      <TableHeader>
        <TableRow>
          {headers.map((header) => (
            <TableHead key={header}>{header}</TableHead>
          ))}
        </TableRow>
      </TableHeader>
    );
  };

  // Helper function to render table rows from data
  const renderTableRows = (data: any[], anomalyIndices: number[] = []) => {
    if (!data || data.length === 0) return null;

    return (
      <TableBody>
        {data.map((row, rowIndex) => (
          <TableRow
            key={rowIndex}
            className={anomalyIndices.includes(rowIndex) ? 'bg-red-50' : ''}
          >
            {Object.entries(row).map(([key, value]: [string, any], cellIndex) => (
              <TableCell key={cellIndex}>
                {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                {anomalyIndices.includes(rowIndex) && cellIndex === 0 && (
                  <Badge variant="destructive" className="ml-2">
                    <AlertTriangle size={12} className="mr-1" />
                    Anomaly
                  </Badge>
                )}
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    );
  };

  if (!combinedDataset) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex justify-center items-center h-64">
          {isLoading ? (
            <RefreshCw size={24} className="animate-spin text-gray-400" />
          ) : (
            <p className="text-gray-500">Combined dataset not found</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-4"
            onClick={() => navigate('/multi-file-ingestion')}
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Multi-File Ingestion
          </Button>
          <h1 className="text-3xl font-bold">{combinedDataset.name}</h1>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowReprocessModal(true)}
            disabled={isReprocessing}
            className="border-amber-200 bg-amber-50 text-amber-700 hover:bg-amber-100"
          >
            {isReprocessing ? (
              <RefreshCw size={16} className="mr-2 animate-spin" />
            ) : (
              <RotateCcw size={16} className="mr-2" />
            )}
            Reprocess Dataset
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleExportData}
          >
            <Download size={16} className="mr-2" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">Total Rows</p>
                <p className="text-2xl font-bold">
                  {combinedDataset.row_count.toLocaleString()}
                </p>
              </div>
              <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                <Database className="h-5 w-5 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">Source Files</p>
                <p className="text-2xl font-bold">
                  {combinedDataset.source_ids.length}
                </p>
              </div>
              <div className="h-10 w-10 bg-green-100 rounded-full flex items-center justify-center">
                <FileSpreadsheet className="h-5 w-5 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">Anomalies</p>
                <p className="text-2xl font-bold">
                  {anomalyResult ? anomalyResult.anomaly_count.toLocaleString() : '0'}
                </p>
                <p className="text-sm text-gray-500">
                  {anomalyResult ?
                    `${anomalyResult.anomaly_percentage.toFixed(1)}%` :
                    'Not detected yet'}
                </p>
              </div>
              <div className="h-10 w-10 bg-amber-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="h-5 w-5 text-amber-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">Columns</p>
                <p className="text-2xl font-bold">
                  {combinedDataset.column_count}
                </p>
              </div>
              <div className="h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center">
                <BarChart className="h-5 w-5 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="data">Combined Data</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="anomalies">Anomalies</TabsTrigger>
        </TabsList>

        <TabsContent value="data" className="mt-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Combined Data Preview</h2>
                <Button onClick={handleDetectAnomalies} disabled={isLoading}>
                  {isLoading ? (
                    <RefreshCw size={16} className="mr-2 animate-spin" />
                  ) : (
                    <AlertTriangle size={16} className="mr-2" />
                  )}
                  Detect Anomalies
                </Button>
              </div>

              <div className="border rounded-md overflow-auto max-h-[500px]">
                <Table>
                  {renderTableHeaders(sourceData)}
                  {renderTableRows(sourceData)}
                </Table>
              </div>

              <p className="text-sm text-gray-500 mt-2">
                Showing {sourceData.length} of {combinedDataset.row_count} rows
              </p>

              <div className="mt-6">
                <h3 className="text-lg font-semibold mb-2">Source Files</h3>
                <div className="space-y-2">
                  {combinedDataset.source_ids.map((sourceId, index) => (
                    <div
                      key={sourceId}
                      className="p-3 bg-gray-50 rounded-md border border-gray-200"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <FileSpreadsheet size={16} className="mr-2 text-blue-600" />
                          <span>Source {index + 1}: {sourceId}</span>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => navigate(`/excel-analysis/${sourceId}`)}
                        >
                          View Source
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="mt-6">
          <DataSummaryView
            datasetId={id || ''}
            isLoading={isSummaryLoading}
            summary={dataSummary}
            analysis={dataAnalysis}
            onRefresh={fetchDataSummary}
          />
        </TabsContent>

        <TabsContent value="anomalies" className="mt-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-xl font-semibold">Anomaly Detection Results</h2>
                <Button onClick={handleDetectAnomalies} disabled={isLoading}>
                  {isLoading ? (
                    <RefreshCw size={16} className="mr-2 animate-spin" />
                  ) : (
                    <RefreshCw size={16} className="mr-2" />
                  )}
                  Refresh
                </Button>
              </div>

              {!anomalyResult ? (
                <div className="flex flex-col items-center justify-center py-12">
                  <AlertTriangle size={48} className="text-gray-300 mb-4" />
                  <p className="text-gray-500 mb-4">No anomalies detected yet</p>
                  <Button onClick={handleDetectAnomalies} disabled={isLoading}>
                    {isLoading ? 'Detecting...' : 'Detect Anomalies'}
                  </Button>
                </div>
              ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <Card>
                      <CardContent className="p-4">
                        <p className="text-sm text-gray-500">Anomaly Count</p>
                        <p className="text-2xl font-bold">{anomalyResult.anomaly_count}</p>
                        <p className="text-sm text-gray-500">
                          {anomalyResult.anomaly_percentage.toFixed(1)}% of total
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <p className="text-sm text-gray-500">Affected Columns</p>
                        <p className="text-2xl font-bold">
                          {Object.keys(anomalyResult.anomalies_by_column).length}
                        </p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardContent className="p-4">
                        <p className="text-sm text-gray-500">Detection Time</p>
                        <p className="text-2xl font-bold">
                          {new Date(anomalyResult.created_at).toLocaleTimeString()}
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  <h3 className="text-lg font-semibold mb-2">Anomalies by Column</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                    {Object.entries(anomalyResult.anomalies_by_column).map(([column, data]: [string, any]) => (
                      <Card key={column}>
                        <CardContent className="p-4">
                          <p className="font-medium">{column}</p>
                          <p className="text-xl font-bold">{data.count} anomalies</p>
                          <p className="text-sm text-gray-500">
                            {((data.count / anomalyResult.total_rows) * 100).toFixed(1)}% of rows
                          </p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  <h3 className="text-lg font-semibold mb-2">Anomalous Records</h3>
                  <div className="border rounded-md overflow-auto max-h-[400px]">
                    <Table>
                      {renderTableHeaders(anomalyResult.anomaly_records)}
                      {renderTableRows(anomalyResult.anomaly_records)}
                    </Table>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Reprocess Confirmation Modal */}
      <ConfirmationModal
        isOpen={showReprocessModal}
        onClose={() => setShowReprocessModal(false)}
        onConfirm={handleReprocessDataset}
        title="Reprocess Dataset"
        description={
          `Are you sure you want to reprocess this dataset? This will allow you to review and adjust field mappings before recreating the dataset. The current dataset will be deleted and cannot be recovered.`
        }
        confirmText="Proceed to Field Mapping"
        cancelText="Cancel"
        destructive={true}
      />

      {/* Schema Review Modal */}
      <Dialog open={showSchemaReviewModal} onOpenChange={setShowSchemaReviewModal}>
        <DialogContent className="max-w-4xl">
          <SchemaReviewPanel
            fieldMappings={fieldMappings}
            onUpdateMapping={setFieldMappings}
            onConfirm={() => handleConfirmReprocessing(fieldMappings)}
            onCancel={handleCancelReprocessing}
            isLoading={isReprocessing}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CombinedAnalysis;
