import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { MatchingConfigFormValues } from '../validationSchema';
import { 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage,
  FormDescription
} from '@/components/ui/form';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { InfoIcon } from 'lucide-react';

interface ThresholdSettingsProps {
  form: UseFormReturn<MatchingConfigFormValues>;
  methods: string[];
}

const ThresholdSettings: React.FC<ThresholdSettingsProps> = ({ form, methods }) => {
  // Helper function to get threshold description based on value
  const getThresholdDescription = (value: number): string => {
    if (value >= 0.9) return 'Very strict - only nearly exact matches';
    if (value >= 0.8) return 'Strict - high confidence matches';
    if (value >= 0.7) return 'Balanced - good confidence matches';
    if (value >= 0.6) return 'Relaxed - more matches, some may be incorrect';
    if (value >= 0.5) return 'Very relaxed - many matches, higher error rate';
    return 'Extremely relaxed - not recommended';
  };

  // Helper function to get threshold color based on value
  const getThresholdColor = (value: number): string => {
    if (value >= 0.9) return 'bg-green-600';
    if (value >= 0.8) return 'bg-green-500';
    if (value >= 0.7) return 'bg-blue-500';
    if (value >= 0.6) return 'bg-yellow-500';
    if (value >= 0.5) return 'bg-orange-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2 mb-4">
        <InfoIcon className="h-5 w-5 text-blue-500" />
        <p className="text-sm text-gray-600">
          Set the confidence thresholds for each matching method. Higher thresholds mean stricter matching.
        </p>
      </div>

      {methods.includes('tfidf') && (
        <FormField
          control={form.control}
          name="thresholds.tfidf"
          render={({ field }) => (
            <FormItem>
              <FormLabel>TF-IDF Matching Threshold</FormLabel>
              <FormDescription>
                Threshold for text-based matching using TF-IDF vectorization
              </FormDescription>
              <div className="flex items-center space-x-4">
                <FormControl>
                  <Slider
                    value={[field.value || 0.7]}
                    min={0.1}
                    max={1}
                    step={0.05}
                    onValueChange={(value) => field.onChange(value[0])}
                    className="flex-1"
                  />
                </FormControl>
                <Input
                  type="number"
                  value={field.value}
                  onChange={(e) => field.onChange(parseFloat(e.target.value))}
                  min={0.1}
                  max={1}
                  step={0.05}
                  className="w-20"
                />
              </div>
              <div className="mt-2">
                <Badge className={getThresholdColor(field.value)}>
                  {getThresholdDescription(field.value)}
                </Badge>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      {methods.includes('embeddings') && (
        <FormField
          control={form.control}
          name="thresholds.embeddings"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Embeddings Matching Threshold</FormLabel>
              <FormDescription>
                Threshold for semantic matching using embeddings
              </FormDescription>
              <div className="flex items-center space-x-4">
                <FormControl>
                  <Slider
                    value={[field.value || 0.7]}
                    min={0.1}
                    max={1}
                    step={0.05}
                    onValueChange={(value) => field.onChange(value[0])}
                    className="flex-1"
                  />
                </FormControl>
                <Input
                  type="number"
                  value={field.value}
                  onChange={(e) => field.onChange(parseFloat(e.target.value))}
                  min={0.1}
                  max={1}
                  step={0.05}
                  className="w-20"
                />
              </div>
              <div className="mt-2">
                <Badge className={getThresholdColor(field.value)}>
                  {getThresholdDescription(field.value)}
                </Badge>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      {methods.includes('hybrid') && (
        <FormField
          control={form.control}
          name="thresholds.hybrid"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Hybrid Matching Threshold</FormLabel>
              <FormDescription>
                Threshold for combined matching using multiple techniques
              </FormDescription>
              <div className="flex items-center space-x-4">
                <FormControl>
                  <Slider
                    value={[field.value || 0.7]}
                    min={0.1}
                    max={1}
                    step={0.05}
                    onValueChange={(value) => field.onChange(value[0])}
                    className="flex-1"
                  />
                </FormControl>
                <Input
                  type="number"
                  value={field.value}
                  onChange={(e) => field.onChange(parseFloat(e.target.value))}
                  min={0.1}
                  max={1}
                  step={0.05}
                  className="w-20"
                />
              </div>
              <div className="mt-2">
                <Badge className={getThresholdColor(field.value)}>
                  {getThresholdDescription(field.value)}
                </Badge>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      {methods.includes('weighted_field') && (
        <FormField
          control={form.control}
          name="thresholds.weighted_field"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Weighted Field Matching Threshold</FormLabel>
              <FormDescription>
                Threshold for field-by-field matching with weights
              </FormDescription>
              <div className="flex items-center space-x-4">
                <FormControl>
                  <Slider
                    value={[field.value || 0.7]}
                    min={0.1}
                    max={1}
                    step={0.05}
                    onValueChange={(value) => field.onChange(value[0])}
                    className="flex-1"
                  />
                </FormControl>
                <Input
                  type="number"
                  value={field.value}
                  onChange={(e) => field.onChange(parseFloat(e.target.value))}
                  min={0.1}
                  max={1}
                  step={0.05}
                  className="w-20"
                />
              </div>
              <div className="mt-2">
                <Badge className={getThresholdColor(field.value)}>
                  {getThresholdDescription(field.value)}
                </Badge>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      {methods.includes('phonetic_name') && (
        <FormField
          control={form.control}
          name="thresholds.phonetic_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phonetic Name Matching Threshold</FormLabel>
              <FormDescription>
                Threshold for name matching using phonetic algorithms
              </FormDescription>
              <div className="flex items-center space-x-4">
                <FormControl>
                  <Slider
                    value={[field.value || 0.7]}
                    min={0.1}
                    max={1}
                    step={0.05}
                    onValueChange={(value) => field.onChange(value[0])}
                    className="flex-1"
                  />
                </FormControl>
                <Input
                  type="number"
                  value={field.value}
                  onChange={(e) => field.onChange(parseFloat(e.target.value))}
                  min={0.1}
                  max={1}
                  step={0.05}
                  className="w-20"
                />
              </div>
              <div className="mt-2">
                <Badge className={getThresholdColor(field.value)}>
                  {getThresholdDescription(field.value)}
                </Badge>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />
      )}

      <FormField
        control={form.control}
        name="thresholds.anomaly_factor"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Anomaly Detection Factor</FormLabel>
            <FormDescription>
              Factor to multiply thresholds by for anomaly detection (partial matches)
            </FormDescription>
            <div className="flex items-center space-x-4">
              <FormControl>
                <Slider
                  value={[field.value || 0.7]}
                  min={0.1}
                  max={0.9}
                  step={0.05}
                  onValueChange={(value) => field.onChange(value[0])}
                  className="flex-1"
                />
              </FormControl>
              <Input
                type="number"
                value={field.value}
                onChange={(e) => field.onChange(parseFloat(e.target.value))}
                min={0.1}
                max={0.9}
                step={0.05}
                className="w-20"
              />
            </div>
            <FormMessage />
          </FormItem>
        )}
      />

      <Card className="mt-6">
        <CardContent className="pt-6">
          <div className="text-sm">
            <p className="font-medium mb-2">Threshold Guide:</p>
            <ul className="space-y-2">
              <li className="flex items-center">
                <Badge className="bg-green-600 mr-2">0.9 - 1.0</Badge>
                <span>Very strict - only nearly exact matches</span>
              </li>
              <li className="flex items-center">
                <Badge className="bg-green-500 mr-2">0.8 - 0.9</Badge>
                <span>Strict - high confidence matches</span>
              </li>
              <li className="flex items-center">
                <Badge className="bg-blue-500 mr-2">0.7 - 0.8</Badge>
                <span>Balanced - good confidence matches</span>
              </li>
              <li className="flex items-center">
                <Badge className="bg-yellow-500 mr-2">0.6 - 0.7</Badge>
                <span>Relaxed - more matches, some may be incorrect</span>
              </li>
              <li className="flex items-center">
                <Badge className="bg-orange-500 mr-2">0.5 - 0.6</Badge>
                <span>Very relaxed - many matches, higher error rate</span>
              </li>
              <li className="flex items-center">
                <Badge className="bg-red-500 mr-2">Below 0.5</Badge>
                <span>Extremely relaxed - not recommended</span>
              </li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ThresholdSettings;
