import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, FileSpreadsheet, Tag } from 'lucide-react';

import ClusterReview from '@/components/TransactionCategorization/ClusterReview';
import dataSourcesService from '@/services/dataSourcesService';

const TransactionCategorizationPage: React.FC = () => {
  const { sourceId } = useParams<{ sourceId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [dataSource, setDataSource] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Load data source on mount
  useEffect(() => {
    if (sourceId) {
      loadDataSource(sourceId);
    }
  }, [sourceId]);

  // Load data source from API
  const loadDataSource = async (id: string) => {
    try {
      setLoading(true);
      const source = await dataSourcesService.getDataSource(id);
      setDataSource(source);
    } catch (error: any) {
      toast({
        title: 'Error loading data source',
        description: error.message || 'An error occurred while loading the data source.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle back button click
  const handleBack = () => {
    if (sourceId) {
      navigate(`/excel-analysis/${sourceId}`);
    } else {
      navigate('/');
    }
  };

  // Handle completion of categorization
  const handleCategorizationComplete = () => {
    // Refresh data source
    if (sourceId) {
      loadDataSource(sourceId);
    }
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="sm" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <h1 className="text-3xl font-bold">Transaction Categorization</h1>
        </div>
      </div>

      {/* Data source info */}
      {dataSource && (
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center space-x-2">
              <FileSpreadsheet className="h-5 w-5 text-primary" />
              <CardTitle>{dataSource.name}</CardTitle>
            </div>
            <CardDescription>
              {dataSource.description || 'No description provided'}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-2">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <div className="text-sm font-medium text-gray-500">Source Type</div>
                <div>{dataSource.source_type || 'Unknown'}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">File Name</div>
                <div>{dataSource.file_name || 'N/A'}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Created</div>
                <div>{new Date(dataSource.created_at).toLocaleDateString()}</div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-500">Row Count</div>
                <div>{dataSource.row_count || 'Unknown'}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main content */}
      <Tabs defaultValue="categories">
        <TabsList>
          <TabsTrigger value="categories">
            <Tag className="h-4 w-4 mr-2" />
            Categories
          </TabsTrigger>
        </TabsList>
        <TabsContent value="categories" className="mt-4">
          {sourceId && (
            <ClusterReview
              sourceId={sourceId}
              onComplete={handleCategorizationComplete}
            />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TransactionCategorizationPage;
