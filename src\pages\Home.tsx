
import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { 
  Database, 
  ChartBar, 
  Search, 
  Settings, 
  ArrowRight 
} from 'lucide-react';
import FeatureCard from '@/components/FeatureCard';
import ForecastChart from '@/components/ForecastChart';
import CallToAction from '@/components/CallToAction';

const Home = () => {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <div className="bg-bb-gradient text-bb-white min-h-[calc(100vh-5rem)] flex items-center relative overflow-hidden">
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: 'url("data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.15\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
            backgroundSize: '24px 24px'
          }}
        />
        <div className="max-w-7xl mx-auto px-6 lg:px-8 z-10 w-full">
          <div className="grid md:grid-cols-2 gap-16 items-center">
            <div className="opacity-0 animate-fade-in">
              <h1 className="font-heading text-5xl md:text-6xl lg:text-7xl font-extrabold tracking-tight mb-8 leading-tight">
                Simplify Financial Data Intelligence
              </h1>
              <p className="text-xl opacity-90 mb-10 leading-relaxed font-medium">
                Transform complex financial data into clear insights with AI-powered analysis, categorization, and reporting designed for modern banking professionals.
              </p>
              <div className="flex flex-wrap gap-6">
                <Link to="/analyzer">
                  <Button size="lg" className="bg-bb-red hover:bg-bb-red/90 text-bb-white font-semibold px-8 py-4 text-lg rounded-xl shadow-lg hover:shadow-xl transition-all duration-200">
                    Try Financial Analyzer
                  </Button>
                </Link>
                <Link to="/solution">
                  <Button size="lg" className="bg-bb-white/10 hover:bg-bb-white/20 text-bb-white font-semibold px-8 py-4 text-lg rounded-xl border border-bb-white/20 backdrop-blur-sm transition-all duration-200">
                    See How It Works
                  </Button>
                </Link>
              </div>
            </div>
            <div className="opacity-0 animate-fade-in animate-delay-300 hidden md:block">
              <div className="glass p-6">
                <ForecastChart />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Key Benefits Section */}
      <section className="section-padding bg-lightgray">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-navy">Key Benefits</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our platform transforms how financial institutions handle data, saving time and improving accuracy.
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard 
              icon={<Database size={24} />}
              title="Reduced Manual Effort" 
              description="Automate extraction, cleaning, and structuring of data from disparate systems, cutting down on spreadsheet drudgery."
            />
            <FeatureCard 
              icon={<ChartBar size={24} />}
              title="Faster Decision-Making" 
              description="Near real-time forecasting helps teams proactively manage sales, product lines, and capital requirements."
            />
            <FeatureCard 
              icon={<Settings size={24} />}
              title="Regulatory Compliance" 
              description="Keep data secure in your environment, with full control aligned with regulatory needs."
            />
          </div>
          <div className="mt-12 text-center">
            <Link to="/features">
              <Button className="flex items-center gap-2">
                Explore All Features
                <ArrowRight size={16} />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="section-padding">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-navy">How It Works</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our AI-driven platform seamlessly processes your financial data through a simple workflow.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="order-2 md:order-1">
              <div className="bg-white rounded-xl shadow-md p-6 border border-gray-100">
                <ul className="space-y-8">
                  <li className="flex items-start gap-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-teal text-navy font-bold shrink-0">
                      1
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-navy mb-1">Data Ingestion</h3>
                      <p className="text-gray-600">
                        Connect to multiple data sources including CSV files, databases, and APIs to import your financial data.
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start gap-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-teal text-navy font-bold shrink-0">
                      2
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-navy mb-1">AI-Based Cleaning</h3>
                      <p className="text-gray-600">
                        Our LLM automatically identifies fields, classifies data, and flags anomalies or inconsistencies.
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start gap-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-teal text-navy font-bold shrink-0">
                      3
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-navy mb-1">Forecast Generation</h3>
                      <p className="text-gray-600">
                        Generate accurate forecasts for principal, interest, default risk, and other key metrics.
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start gap-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-teal text-navy font-bold shrink-0">
                      4
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-navy mb-1">Scenario Analysis</h3>
                      <p className="text-gray-600">
                        Test different business scenarios with adjustable parameters to inform strategic decisions.
                      </p>
                    </div>
                  </li>
                </ul>
              </div>
            </div>

            <div className="order-1 md:order-2">
              <img 
                src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&h=600&fit=crop"
                alt="Data analytics dashboard"
                className="rounded-xl shadow-lg w-full"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Target Customers Section */}
      <section className="section-padding bg-navy text-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Who We Serve</h2>
            <p className="text-xl opacity-80 max-w-3xl mx-auto">
              Our platform is designed for financial institutions that need better data management and forecasting.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/5 hover:bg-white/15 transition-colors">
              <h3 className="text-2xl font-semibold mb-4 text-teal">Mid-Sized Financial Institutions</h3>
              <p className="text-white/80">
                Consumer lenders, Buy Now Pay Later providers, credit unions, and specialized loan companies seeking to reduce manual data work.
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/5 hover:bg-white/15 transition-colors">
              <h3 className="text-2xl font-semibold mb-4 text-teal">Regional Banks</h3>
              <p className="text-white/80">
                Institutions without extensive in-house data science teams that need efficient forecasting and reporting capabilities.
              </p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/5 hover:bg-white/15 transition-colors">
              <h3 className="text-2xl font-semibold mb-4 text-teal">Fintech Startups</h3>
              <p className="text-white/80">
                Rapidly growing companies merging data from various sources and platforms, looking for quick ROI on data integration.
              </p>
            </div>
          </div>
        </div>
      </section>

      <CallToAction />
    </div>
  );
};

export default Home;
