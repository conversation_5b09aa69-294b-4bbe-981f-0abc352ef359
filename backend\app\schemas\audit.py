from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class RecordHistoryBase(BaseModel):
    record_type: str
    record_id: str
    field: str
    old_value: Optional[str] = None
    new_value: Optional[str] = None
    reason: Optional[str] = None


class RecordHistoryCreate(RecordHistoryBase):
    pass


class RecordHistoryResponse(RecordHistoryBase):
    id: int
    created_at: datetime
    audit_log_id: Optional[int] = None
    
    class Config:
        from_attributes = True


class AuditLogBase(BaseModel):
    action: str
    description: Optional[str] = None
    category: str = Field(..., description="Category of action: 'data', 'system', 'edit', 'report', 'export'")
    details: Optional[Dict[str, Any]] = None


class AuditLogCreate(AuditLogBase):
    record_history: Optional[List[RecordHistoryCreate]] = None


class AuditLogResponse(AuditLogBase):
    id: int
    ip_address: Optional[str] = None
    created_at: datetime
    user_id: Optional[int] = None
    
    class Config:
        from_attributes = True
