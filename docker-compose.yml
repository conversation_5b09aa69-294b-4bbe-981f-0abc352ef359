version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - VITE_API_URL=http://backend:8002
    depends_on:
      - backend
    volumes:
      - ./src:/app/src
      - ./public:/app/public

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8002:8002"
    environment:
      - POSTGRES_SERVER=db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=data_oracle
      - SECRET_KEY=your_secret_key_here
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - GCP_STORAGE_BUCKET=${GCP_STORAGE_BUCKET}
      - GCP_BIGQUERY_DATASET=${GCP_BIGQUERY_DATASET}
      - VERTEX_AI_LOCATION=${VERTEX_AI_LOCATION}
      - VERTEX_AI_MODEL=${VERTEX_AI_MODEL}
      - GCP_CREDENTIALS_FILE=/app/gcp-credentials.json
      - GOOGLE_APPLICATION_CREDENTIALS=/app/gcp-credentials.json
    depends_on:
      - db
    volumes:
      - ./backend:/app
      - ${GOOGLE_APPLICATION_CREDENTIALS}:/app/gcp-credentials.json:ro

  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    environment:
      - POSTGRES_SERVER=db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=data_oracle
      - SECRET_KEY=your_secret_key_here
      - GCP_PROJECT_ID=${GCP_PROJECT_ID}
      - GCP_STORAGE_BUCKET=${GCP_STORAGE_BUCKET}
      - GCP_BIGQUERY_DATASET=${GCP_BIGQUERY_DATASET}
      - VERTEX_AI_LOCATION=${VERTEX_AI_LOCATION}
      - VERTEX_AI_MODEL=${VERTEX_AI_MODEL}
      - GCP_CREDENTIALS_FILE=/app/gcp-credentials.json
      - GOOGLE_APPLICATION_CREDENTIALS=/app/gcp-credentials.json
    depends_on:
      - db
      - backend
    volumes:
      - ./backend:/app
      - ${GOOGLE_APPLICATION_CREDENTIALS}:/app/gcp-credentials.json:ro
    command: python run_worker.py

  db:
    image: postgres:15
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=data_oracle
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
