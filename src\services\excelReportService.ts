import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8002';

export interface ExcelReportRequest {
  report_name?: string;
  start_date?: string;
  end_date?: string;
  include_uncategorized?: boolean;
}

export interface ExcelReportResponse {
  success: boolean;
  message?: string;
  filename?: string;
}

class ExcelReportService {
  /**
   * Generate and download an Excel report with P&L, reconciliation, and transaction data
   */
  async generateExcelReport(
    sourceId: string, 
    request: ExcelReportRequest = {}
  ): Promise<void> {
    try {
      console.log(`Generating Excel report for source ${sourceId}`, request);
      
      const response = await axios.post(
        `${API_URL}/api/v1/excel-reports/${sourceId}/excel-report`,
        {
          report_name: request.report_name || 'Financial Report',
          start_date: request.start_date,
          end_date: request.end_date,
          include_uncategorized: request.include_uncategorized ?? true
        },
        {
          responseType: 'blob', // Important for file downloads
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      // Create blob URL and trigger download
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      });
      
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Extract filename from response headers or create default
      const contentDisposition = response.headers['content-disposition'];
      let filename = 'Financial_Report.xlsx';
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          filename = filenameMatch[1].replace(/['"]/g, '');
        }
      }
      
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      console.log(`Excel report downloaded successfully: ${filename}`);
      
    } catch (error) {
      console.error('Error generating Excel report:', error);
      
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 404) {
          throw new Error('No transaction data found. Please ensure transactions are categorized first.');
        } else if (error.response?.status === 500) {
          throw new Error('Server error while generating report. Please try again.');
        } else {
          throw new Error(`Failed to generate Excel report: ${error.response?.statusText || error.message}`);
        }
      } else {
        throw new Error('An unexpected error occurred while generating the report.');
      }
    }
  }

  /**
   * Generate Excel report with date range filtering
   */
  async generateExcelReportWithDateRange(
    sourceId: string,
    startDate: Date,
    endDate: Date,
    reportName?: string
  ): Promise<void> {
    const request: ExcelReportRequest = {
      report_name: reportName || `Financial Report ${startDate.getFullYear()}`,
      start_date: startDate.toISOString().split('T')[0], // YYYY-MM-DD format
      end_date: endDate.toISOString().split('T')[0],
      include_uncategorized: true
    };

    return this.generateExcelReport(sourceId, request);
  }

  /**
   * Generate Excel report for current year
   */
  async generateCurrentYearReport(sourceId: string): Promise<void> {
    const currentYear = new Date().getFullYear();
    const startDate = new Date(currentYear, 0, 1); // January 1st
    const endDate = new Date(currentYear, 11, 31); // December 31st

    return this.generateExcelReportWithDateRange(
      sourceId,
      startDate,
      endDate,
      `Financial Report ${currentYear}`
    );
  }

  /**
   * Generate Excel report for a specific month
   */
  async generateMonthlyReport(
    sourceId: string, 
    year: number, 
    month: number
  ): Promise<void> {
    const startDate = new Date(year, month - 1, 1); // month is 0-indexed
    const endDate = new Date(year, month, 0); // Last day of the month

    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    return this.generateExcelReportWithDateRange(
      sourceId,
      startDate,
      endDate,
      `${monthNames[month - 1]} ${year} Financial Report`
    );
  }

  /**
   * Generate Excel report for a specific quarter
   */
  async generateQuarterlyReport(
    sourceId: string, 
    year: number, 
    quarter: number
  ): Promise<void> {
    const quarterStartMonths = [0, 3, 6, 9]; // Q1: Jan, Q2: Apr, Q3: Jul, Q4: Oct
    const quarterEndMonths = [2, 5, 8, 11]; // Q1: Mar, Q2: Jun, Q3: Sep, Q4: Dec

    const startDate = new Date(year, quarterStartMonths[quarter - 1], 1);
    const endDate = new Date(year, quarterEndMonths[quarter - 1] + 1, 0); // Last day of quarter

    return this.generateExcelReportWithDateRange(
      sourceId,
      startDate,
      endDate,
      `Q${quarter} ${year} Financial Report`
    );
  }

  /**
   * Check if Excel report generation is available for a source
   */
  async checkReportAvailability(sourceId: string): Promise<boolean> {
    try {
      // This could be enhanced to check if the source has categorized data
      // For now, we'll assume it's available if the source exists
      const response = await axios.get(`${API_URL}/api/v1/data-sources/${sourceId}`);
      return response.status === 200;
    } catch (error) {
      console.error('Error checking report availability:', error);
      return false;
    }
  }
}

// Export singleton instance
const excelReportService = new ExcelReportService();
export default excelReportService;
