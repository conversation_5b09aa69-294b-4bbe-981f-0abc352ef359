import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  <PERSON><PERSON>, 
  Ta<PERSON>Content, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  PieChart, 
  Pie, 
  Cell, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  AlertCircle, 
  CheckCircle, 
  ChevronDown, 
  ChevronRight, 
  Download, 
  Search, 
  X 
} from 'lucide-react';
import { MatchingResults, MatchResult, AnomalyResult } from '@/services/matchingService';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface MatchingResultsDisplayProps {
  results: MatchingResults;
  onSave?: () => void;
  onExport?: () => void;
  isLoading?: boolean;
}

const MatchingResultsDisplay: React.FC<MatchingResultsDisplayProps> = ({
  results,
  onSave,
  onExport,
  isLoading = false
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedMatch, setExpandedMatch] = useState<string | null>(null);
  
  // Prepare data for charts
  const pieData = [
    { name: 'Matches', value: results.stats.total_matches },
    { name: 'Anomalies', value: results.stats.total_anomalies }
  ];
  
  const methodData = Object.entries(results.stats.methods).map(([method, stats]) => ({
    name: formatMethodName(method),
    matches: stats.matches,
    anomalies: stats.anomalies
  }));
  
  // Filter matches based on search term
  const filteredMatches = results.matches.filter(match => 
    searchInRecord(match.source_record, searchTerm) || 
    searchInRecord(match.target_record, searchTerm)
  );
  
  const filteredAnomalies = results.anomalies.filter(anomaly => 
    searchInRecord(anomaly.source_record, searchTerm) || 
    searchInRecord(anomaly.target_record, searchTerm)
  );
  
  // Helper function to search in record
  function searchInRecord(record: Record<string, any>, term: string): boolean {
    if (!term) return true;
    
    const lowerTerm = term.toLowerCase();
    return Object.values(record).some(value => 
      String(value).toLowerCase().includes(lowerTerm)
    );
  }
  
  // Helper function to format method name
  function formatMethodName(method: string): string {
    switch (method) {
      case 'tfidf': return 'TF-IDF';
      case 'embeddings': return 'Embeddings';
      case 'hybrid': return 'Hybrid';
      case 'weighted_field': return 'Weighted Field';
      case 'phonetic_name': return 'Phonetic Name';
      default: return method.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
      ).join(' ');
    }
  }
  
  // Helper function to get confidence color
  function getConfidenceColor(confidence: number): string {
    if (confidence >= 0.9) return 'bg-green-500';
    if (confidence >= 0.8) return 'bg-green-400';
    if (confidence >= 0.7) return 'bg-blue-500';
    if (confidence >= 0.6) return 'bg-yellow-500';
    if (confidence >= 0.5) return 'bg-orange-500';
    return 'bg-red-500';
  }
  
  // Colors for charts
  const COLORS = ['#0088FE', '#FF8042', '#00C49F', '#FFBB28', '#A28BFF'];

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>Matching Results</CardTitle>
            <CardDescription>
              {results.stats.total_matches} matches and {results.stats.total_anomalies} anomalies found
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            {onSave && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={onSave}
                disabled={isLoading}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Save Results
              </Button>
            )}
            {onExport && (
              <Button 
                variant="outline" 
                size="sm"
                onClick={onExport}
                disabled={isLoading}
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="matches">Matches</TabsTrigger>
            <TabsTrigger value="anomalies">Anomalies</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Total Matches</p>
                <p className="text-2xl font-bold">{results.stats.total_matches}</p>
              </div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Total Anomalies</p>
                <p className="text-2xl font-bold">{results.stats.total_anomalies}</p>
              </div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Match Rate</p>
                <p className="text-2xl font-bold">
                  {results.stats.total_matches > 0 || results.stats.total_anomalies > 0 ? 
                    `${(results.stats.total_matches / (results.stats.total_matches + results.stats.total_anomalies) * 100).toFixed(1)}%` : 
                    '0%'}
                </p>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Match Distribution</h3>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {pieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [value, 'Count']} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Results by Method</h3>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={methodData}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="matches" name="Matches" fill="#0088FE" />
                      <Bar dataKey="anomalies" name="Anomalies" fill="#FF8042" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
            
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-2">Method Performance</h3>
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Method</TableHead>
                      <TableHead>Matches</TableHead>
                      <TableHead>Anomalies</TableHead>
                      <TableHead>Success Rate</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(results.stats.methods).map(([method, stats]) => {
                      const total = stats.matches + stats.anomalies;
                      const successRate = total > 0 ? (stats.matches / total) * 100 : 0;
                      
                      return (
                        <TableRow key={method}>
                          <TableCell className="font-medium">
                            {formatMethodName(method)}
                          </TableCell>
                          <TableCell>{stats.matches}</TableCell>
                          <TableCell>{stats.anomalies}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <div className="w-full bg-gray-200 rounded-full h-2.5 mr-2 max-w-[100px]">
                                <div 
                                  className={`h-2.5 rounded-full ${getConfidenceColor(successRate / 100)}`}
                                  style={{ width: `${successRate}%` }}
                                ></div>
                              </div>
                              <span>{successRate.toFixed(1)}%</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="matches">
            <div className="mb-4">
              <div className="flex items-center space-x-2">
                <Search className="h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search matches..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-sm"
                />
                {searchTerm && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setSearchTerm('')}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
            
            {filteredMatches.length > 0 ? (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead></TableHead>
                      <TableHead>Source Record</TableHead>
                      <TableHead>Target Record</TableHead>
                      <TableHead>Confidence</TableHead>
                      <TableHead>Method</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMatches.map((match, index) => (
                      <React.Fragment key={index}>
                        <TableRow className="cursor-pointer hover:bg-slate-50" onClick={() => setExpandedMatch(expandedMatch === `match-${index}` ? null : `match-${index}`)}>
                          <TableCell>
                            {expandedMatch === `match-${index}` ? 
                              <ChevronDown className="h-4 w-4" /> : 
                              <ChevronRight className="h-4 w-4" />}
                          </TableCell>
                          <TableCell>
                            {renderRecordSummary(match.source_record)}
                          </TableCell>
                          <TableCell>
                            {renderRecordSummary(match.target_record)}
                          </TableCell>
                          <TableCell>
                            <Badge className={getConfidenceColor(match.confidence)}>
                              {(match.confidence * 100).toFixed(1)}%
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {formatMethodName(match.match_method)}
                          </TableCell>
                        </TableRow>
                        
                        {expandedMatch === `match-${index}` && (
                          <TableRow className="bg-slate-50">
                            <TableCell colSpan={5} className="p-4">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-medium mb-2">Source Record</h4>
                                  <div className="bg-white p-3 rounded border">
                                    {renderRecordDetails(match.source_record)}
                                  </div>
                                </div>
                                <div>
                                  <h4 className="font-medium mb-2">Target Record</h4>
                                  <div className="bg-white p-3 rounded border">
                                    {renderRecordDetails(match.target_record)}
                                  </div>
                                </div>
                              </div>
                              
                              <div className="mt-4">
                                <h4 className="font-medium mb-2">Match Details</h4>
                                <div className="bg-white p-3 rounded border">
                                  <div className="grid grid-cols-2 gap-2">
                                    <div>
                                      <p className="text-sm text-gray-500">Confidence</p>
                                      <p>{(match.confidence * 100).toFixed(1)}%</p>
                                    </div>
                                    <div>
                                      <p className="text-sm text-gray-500">Method</p>
                                      <p>{formatMethodName(match.match_method)}</p>
                                    </div>
                                    
                                    {match.similarity_details && (
                                      <div className="col-span-2 mt-2">
                                        <p className="text-sm text-gray-500 mb-1">Similarity Details</p>
                                        <div className="grid grid-cols-3 gap-2">
                                          {Object.entries(match.similarity_details).map(([key, value]) => (
                                            <div key={key} className="bg-slate-50 p-2 rounded">
                                              <p className="text-xs text-gray-500">{formatMethodName(key)}</p>
                                              <p className="font-medium">{(value * 100).toFixed(1)}%</p>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    )}
                                    
                                    <div className="col-span-2 mt-2">
                                      <p className="text-sm text-gray-500 mb-1">Field Mappings</p>
                                      <div className="grid grid-cols-2 gap-2">
                                        {Object.entries(match.matched_fields).map(([source, target]) => (
                                          <div key={source} className="flex items-center space-x-2">
                                            <Badge variant="outline" className="font-mono">{source}</Badge>
                                            <ChevronRight className="h-4 w-4 text-gray-400" />
                                            <Badge variant="outline" className="font-mono">{target}</Badge>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </React.Fragment>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>No matches found</AlertTitle>
                <AlertDescription>
                  {searchTerm ? 
                    "No matches match your search criteria. Try a different search term." : 
                    "No matches were found between the datasets."}
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>
          
          <TabsContent value="anomalies">
            <div className="mb-4">
              <div className="flex items-center space-x-2">
                <Search className="h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search anomalies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="max-w-sm"
                />
                {searchTerm && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setSearchTerm('')}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
            
            {filteredAnomalies.length > 0 ? (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead></TableHead>
                      <TableHead>Source Record</TableHead>
                      <TableHead>Target Record</TableHead>
                      <TableHead>Confidence</TableHead>
                      <TableHead>Anomaly Type</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAnomalies.map((anomaly, index) => (
                      <React.Fragment key={index}>
                        <TableRow className="cursor-pointer hover:bg-slate-50" onClick={() => setExpandedMatch(expandedMatch === `anomaly-${index}` ? null : `anomaly-${index}`)}>
                          <TableCell>
                            {expandedMatch === `anomaly-${index}` ? 
                              <ChevronDown className="h-4 w-4" /> : 
                              <ChevronRight className="h-4 w-4" />}
                          </TableCell>
                          <TableCell>
                            {renderRecordSummary(anomaly.source_record)}
                          </TableCell>
                          <TableCell>
                            {renderRecordSummary(anomaly.target_record)}
                          </TableCell>
                          <TableCell>
                            <Badge className={getConfidenceColor(anomaly.confidence)}>
                              {(anomaly.confidence * 100).toFixed(1)}%
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {formatAnomalyType(anomaly.anomaly_type)}
                            </Badge>
                          </TableCell>
                        </TableRow>
                        
                        {expandedMatch === `anomaly-${index}` && (
                          <TableRow className="bg-slate-50">
                            <TableCell colSpan={5} className="p-4">
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-medium mb-2">Source Record</h4>
                                  <div className="bg-white p-3 rounded border">
                                    {renderRecordDetails(anomaly.source_record)}
                                  </div>
                                </div>
                                <div>
                                  <h4 className="font-medium mb-2">Target Record</h4>
                                  <div className="bg-white p-3 rounded border">
                                    {renderRecordDetails(anomaly.target_record)}
                                  </div>
                                </div>
                              </div>
                              
                              <div className="mt-4">
                                <h4 className="font-medium mb-2">Anomaly Details</h4>
                                <div className="bg-white p-3 rounded border">
                                  <div className="grid grid-cols-2 gap-2">
                                    <div>
                                      <p className="text-sm text-gray-500">Confidence</p>
                                      <p>{(anomaly.confidence * 100).toFixed(1)}%</p>
                                    </div>
                                    <div>
                                      <p className="text-sm text-gray-500">Method</p>
                                      <p>{formatMethodName(anomaly.match_method)}</p>
                                    </div>
                                    <div>
                                      <p className="text-sm text-gray-500">Anomaly Type</p>
                                      <p>{formatAnomalyType(anomaly.anomaly_type)}</p>
                                    </div>
                                    
                                    {anomaly.similarity_details && (
                                      <div className="col-span-2 mt-2">
                                        <p className="text-sm text-gray-500 mb-1">Similarity Details</p>
                                        <div className="grid grid-cols-3 gap-2">
                                          {Object.entries(anomaly.similarity_details).map(([key, value]) => (
                                            <div key={key} className="bg-slate-50 p-2 rounded">
                                              <p className="text-xs text-gray-500">{formatMethodName(key)}</p>
                                              <p className="font-medium">{(value * 100).toFixed(1)}%</p>
                                            </div>
                                          ))}
                                        </div>
                                      </div>
                                    )}
                                    
                                    <div className="col-span-2 mt-2">
                                      <p className="text-sm text-gray-500 mb-1">Field Mappings</p>
                                      <div className="grid grid-cols-2 gap-2">
                                        {Object.entries(anomaly.matched_fields).map(([source, target]) => (
                                          <div key={source} className="flex items-center space-x-2">
                                            <Badge variant="outline" className="font-mono">{source}</Badge>
                                            <ChevronRight className="h-4 w-4 text-gray-400" />
                                            <Badge variant="outline" className="font-mono">{target}</Badge>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </React.Fragment>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>No anomalies found</AlertTitle>
                <AlertDescription>
                  {searchTerm ? 
                    "No anomalies match your search criteria. Try a different search term." : 
                    "No anomalies were found between the datasets."}
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

// Helper function to render a summary of a record
function renderRecordSummary(record: Record<string, any>): React.ReactNode {
  // Get the first few fields to display
  const fields = Object.entries(record).slice(0, 2);
  
  return (
    <div>
      {fields.map(([key, value]) => (
        <div key={key} className="truncate max-w-[200px]">
          <span className="text-gray-500 text-xs">{key}: </span>
          <span className="text-sm">{String(value)}</span>
        </div>
      ))}
      {Object.keys(record).length > 2 && (
        <span className="text-xs text-gray-400">+ {Object.keys(record).length - 2} more fields</span>
      )}
    </div>
  );
}

// Helper function to render detailed record information
function renderRecordDetails(record: Record<string, any>): React.ReactNode {
  return (
    <div className="grid grid-cols-2 gap-2">
      {Object.entries(record).map(([key, value]) => (
        <div key={key}>
          <p className="text-xs text-gray-500">{key}</p>
          <p className="truncate">{String(value)}</p>
        </div>
      ))}
    </div>
  );
}

// Helper function to format anomaly type
function formatAnomalyType(type: string): string {
  if (type.includes('below_threshold')) {
    const method = type.split('_below_threshold')[0];
    return `Below ${formatMethodName(method)} Threshold`;
  }
  return type.split('_').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
}

export default MatchingResultsDisplay;
