from datetime import datetime
from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Integer, String
from sqlalchemy.orm import relationship

from app.db.base_class import Base


class User(Base):
    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String, index=True)
    is_active = Column(Boolean(), default=True)
    is_superuser = Column(Boolean(), default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    pipelines = relationship("Pipeline", back_populates="owner")
    data_sources = relationship("DataSource", back_populates="owner")
    audit_logs = relationship("AuditLog", back_populates="user")
