from datetime import datetime
from sqlalchemy import Column, DateTime, Enum, Float, ForeignKey, Integer, JSON, String, Text
from sqlalchemy.orm import relationship

from app.db.base_class import Base


class ReconciliationRule(Base):
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    rule_type = Column(Enum("exact_match", "fuzzy_match", "semantic_match", name="rule_type"), nullable=False)
    source_fields = Column(JSON, nullable=False)  # List of fields from source A
    target_fields = Column(JSON, nullable=False)  # List of fields from source B
    threshold = Column(Float, default=0.8)  # Confidence threshold for matches
    config = Column(JSON, nullable=True)  # Additional rule configuration
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class ReconciliationResult(Base):
    id = Column(Integer, primary_key=True, index=True)
    status = Column(Enum("pending", "in_progress", "completed", "failed", name="result_status"), default="pending")
    total_records = Column(Integer, default=0)
    matched_records = Column(Integer, default=0)
    anomaly_records = Column(Integer, default=0)
    unmatched_records = Column(Integer, default=0)
    match_rate = Column(Float, default=0.0)
    anomaly_details = Column(JSON, nullable=True)  # Details of anomalies by type
    result_data = Column(JSON, nullable=True)  # Summary of reconciliation results
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign keys
    pipeline_id = Column(Integer, ForeignKey("pipeline.id"))
    
    # Relationships
    pipeline = relationship("Pipeline", back_populates="reconciliation_results")
