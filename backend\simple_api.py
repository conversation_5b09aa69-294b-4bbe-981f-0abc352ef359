from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
import uuid
import random
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Data Oracle API")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mock data
ACTIVE_JOBS = [
    {
        "job_id": "job-1",
        "data_source_id": 1,
        "pipeline_id": 1,
        "status": "processing",
        "progress": 45.0,
        "created_at": datetime.now(timezone.utc).isoformat(),
        "updated_at": datetime.now(timezone.utc).isoformat(),
    }
]

COMPLETED_JOBS = [
    {
        "job_id": "job-2",
        "data_source_id": 1,
        "pipeline_id": 1,
        "status": "completed",
        "progress": 100.0,
        "result_id": "result-1",
        "created_at": datetime.now(timezone.utc).isoformat(),
        "updated_at": datetime.now(timezone.utc).isoformat(),
        "completed_at": datetime.now(timezone.utc).isoformat(),
    }
]

ANOMALY_RESULTS = {
    "result-1": {
        "id": "result-1",
        "total_rows": 1000,
        "anomaly_count": 15,
        "anomaly_indices": [12, 45, 67, 89, 120, 145, 190, 210, 250, 300, 350, 400, 450, 500, 550],
        "anomalies_by_method": {
            "statistical": 8,
            "isolation_forest": 5,
            "domain_rules": 2
        },
        "anomalies_by_column": {
            "amount": 6,
            "date": 4,
            "description": 5
        },
        "anomaly_records": [
            {"id": 12, "amount": 9999.99, "date": "2023-01-15", "description": "Unusual transaction"},
            {"id": 45, "amount": 0.01, "date": "2023-02-20", "description": "Suspicious activity"},
            {"id": 67, "amount": 5000.00, "date": "2023-03-05", "description": "Potential duplicate"}
        ],
        "anomaly_percentage": 1.5
    }
}

DATA_SOURCES = [
    {
        "id": 1,
        "name": "Invoice Data",
        "source_type": "csv",
        "record_count": 1000,
        "created_at": datetime.now(timezone.utc).isoformat(),
        "status": "active"
    },
    {
        "id": 2,
        "name": "Payment Data",
        "source_type": "csv",
        "record_count": 950,
        "created_at": datetime.now(timezone.utc).isoformat(),
        "status": "active"
    }
]

# API endpoints
@app.get("/")
async def root():
    return {"message": "Welcome to Data Oracle API", "version": "1.0.0"}

@app.get("/api/anomaly-detection/jobs")
async def get_active_jobs(pipeline_id: int, status: str = "active"):
    logger.info(f"Getting active jobs for pipeline {pipeline_id} with status {status}")
    if status == "active":
        return ACTIVE_JOBS
    elif status == "completed":
        return COMPLETED_JOBS
    else:
        return []

@app.post("/api/anomaly-detection/start")
async def start_anomaly_detection(request: Dict[str, Any]):
    job_id = f"job-{uuid.uuid4()}"
    new_job = {
        "job_id": job_id,
        "data_source_id": request.get("data_source_id", 1),
        "pipeline_id": request.get("pipeline_id", 1),
        "status": "pending",
        "progress": 0.0,
        "created_at": datetime.now(timezone.utc).isoformat(),
        "updated_at": datetime.now(timezone.utc).isoformat(),
    }
    ACTIVE_JOBS.append(new_job)
    return new_job

@app.get("/api/anomaly-detection/jobs/{job_id}")
async def get_job_status(job_id: str):
    # Check active jobs
    for job in ACTIVE_JOBS:
        if job["job_id"] == job_id:
            # Update progress for simulation
            job["progress"] = min(job["progress"] + random.uniform(5, 15), 100)
            job["updated_at"] = datetime.now(timezone.utc).isoformat()

            # If job is complete, move to completed jobs
            if job["progress"] >= 100:
                job["status"] = "completed"
                job["result_id"] = f"result-{uuid.uuid4()}"
                job["completed_at"] = datetime.now(timezone.utc).isoformat()
                COMPLETED_JOBS.append(job)
                ACTIVE_JOBS.remove(job)

                # Create a result
                result_id = job["result_id"]
                ANOMALY_RESULTS[result_id] = {
                    "id": result_id,
                    "total_rows": 1000,
                    "anomaly_count": random.randint(5, 20),
                    "anomaly_indices": [random.randint(1, 1000) for _ in range(random.randint(5, 20))],
                    "anomalies_by_method": {
                        "statistical": random.randint(2, 8),
                        "isolation_forest": random.randint(2, 8),
                        "domain_rules": random.randint(1, 4)
                    },
                    "anomalies_by_column": {
                        "amount": random.randint(2, 8),
                        "date": random.randint(1, 5),
                        "description": random.randint(1, 7)
                    },
                    "anomaly_records": [
                        {"id": random.randint(1, 1000), "amount": random.uniform(1, 10000), "date": "2023-01-15", "description": "Unusual transaction"},
                        {"id": random.randint(1, 1000), "amount": random.uniform(1, 10000), "date": "2023-02-20", "description": "Suspicious activity"},
                        {"id": random.randint(1, 1000), "amount": random.uniform(1, 10000), "date": "2023-03-05", "description": "Potential duplicate"}
                    ],
                    "anomaly_percentage": random.uniform(0.5, 3.0)
                }

            return job

    # Check completed jobs
    for job in COMPLETED_JOBS:
        if job["job_id"] == job_id:
            return job

    raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")

@app.get("/api/anomaly-detection/results/{result_id}")
async def get_anomaly_results(result_id: str):
    if result_id in ANOMALY_RESULTS:
        return ANOMALY_RESULTS[result_id]
    raise HTTPException(status_code=404, detail=f"Result with ID {result_id} not found")

@app.get("/api/data-sources")
async def get_data_sources():
    return DATA_SOURCES

@app.post("/api/data-sources/from-data")
async def create_from_data(request: Dict[str, Any]):
    logger.info(f"Received request to create data source from data: {request}")

    # Extract data from request
    name = request.get("name", "Unnamed Data Source")
    description = request.get("description")
    source_type = request.get("source_type", "file")
    file_type = request.get("file_type", "csv")
    data = request.get("data", [])

    logger.info(f"Creating data source with name: {name}, type: {source_type}, records: {len(data)}")

    # Create a new data source
    new_data_source = {
        "id": len(DATA_SOURCES) + 1,
        "name": name,
        "source_type": source_type,
        "record_count": len(data),
        "created_at": datetime.now(timezone.utc).isoformat(),
        "status": "active"
    }

    DATA_SOURCES.append(new_data_source)
    logger.info(f"Created data source: {new_data_source}")

    return new_data_source

@app.post("/api/data-cleaning/standardize")
async def standardize_data(request: Dict[str, Any]):
    return {
        "originalRows": 1000,
        "standardizedRows": 950,
        "changes": {
            "dates": 120,
            "amounts": 85,
            "names": 45,
            "addresses": 30
        },
        "sampleData": [
            {"before": {"date": "01/15/2023"}, "after": {"date": "2023-01-15"}},
            {"before": {"amount": "1,234.56"}, "after": {"amount": 1234.56}},
            {"before": {"name": "john smith"}, "after": {"name": "John Smith"}}
        ]
    }

@app.post("/api/data-cleaning/classify")
async def classify_data(request: Dict[str, Any]):
    return {
        "totalRows": 1000,
        "processedRows": 980,
        "categories": {
            "invoice": 450,
            "payment": 380,
            "credit_note": 120,
            "other": 30
        },
        "confidence": {
            "high": 800,
            "medium": 150,
            "low": 30
        },
        "sampleData": [
            {"id": 1, "text": "Invoice #12345", "category": "invoice", "confidence": 0.95},
            {"id": 2, "text": "Payment received", "category": "payment", "confidence": 0.92},
            {"id": 3, "text": "Credit note issued", "category": "credit_note", "confidence": 0.88}
        ]
    }

@app.post("/api/reconciliation/match")
async def match_data(request: Dict[str, Any]):
    return {
        "matches": [
            {
                "source_record": {"id": 1, "amount": 100.00, "date": "2023-01-15"},
                "target_record": {"id": 101, "amount": 100.00, "date": "2023-01-15"},
                "source_index": 0,
                "target_index": 0,
                "confidence": 0.95,
                "matched_fields": {"amount": "exact", "date": "exact"},
                "match_method": "exact"
            },
            {
                "source_record": {"id": 2, "amount": 200.00, "date": "2023-01-16"},
                "target_record": {"id": 102, "amount": 200.00, "date": "2023-01-16"},
                "source_index": 1,
                "target_index": 1,
                "confidence": 0.90,
                "matched_fields": {"amount": "exact", "date": "exact"},
                "match_method": "exact"
            }
        ],
        "anomalies": [
            {
                "source_record": {"id": 3, "amount": 300.00, "date": "2023-01-17"},
                "target_record": {"id": 103, "amount": 350.00, "date": "2023-01-17"},
                "source_index": 2,
                "target_index": 2,
                "confidence": 0.75,
                "matched_fields": {"date": "exact"},
                "match_method": "partial",
                "anomaly_type": "amount_mismatch"
            }
        ],
        "stats": {
            "total_matches": 2,
            "total_anomalies": 1,
            "methods": {
                "exact": {
                    "matches": 2,
                    "anomalies": 0
                },
                "partial": {
                    "matches": 0,
                    "anomalies": 1
                }
            }
        }
    }

if __name__ == "__main__":
    logger.info("Starting API server...")
    uvicorn.run(app, host="0.0.0.0", port=8001)
