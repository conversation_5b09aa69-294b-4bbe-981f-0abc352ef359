import logging
import os
from datetime import datetime, timezone

from sqlalchemy.orm import Session

from app.db.base import Base
from app.db.session import engine, SessionLocal
from app.core.config import settings

# Import models to ensure they are registered with SQLAlchemy
try:
    from app.models import user, data_source, pipeline, reconciliation, audit, anomaly_detection
except ImportError as e:
    logging.error(f"Error importing models: {e}")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def init_db() -> None:
    try:
        # Create tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created")

        # Add sample data if in development mode
        if os.environ.get("ENVIRONMENT") == "development":
            create_sample_data()
    except Exception as e:
        logger.error(f"Error initializing database: {e}")


def create_sample_data():
    """Create sample data for development."""
    try:
        from app.models.user import User
        from app.models.data_source import DataSource
        from app.models.pipeline import Pipeline
        from app.models.anomaly_detection import AnomalyDetectionJob
    except ImportError as e:
        logger.error(f"Error importing models for sample data: {e}")
        return

    db = SessionLocal()

    try:
        # Check if we already have data
        if db.query(User).count() > 0:
            logger.info("Sample data already exists")
            return

        # Create sample user
        user = User(
            email="<EMAIL>",
            hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # "password"
            full_name="Test User",
            is_active=True,
            is_superuser=False
        )
        db.add(user)

        # Create sample data sources
        data_sources = [
            DataSource(
                name="Invoice Data",
                source_type="file",
                file_path="invoices.csv",
                file_type="csv",
                schema={"columns": ["invoice_id", "amount", "date", "customer", "description"]},
                owner_id=1
            ),
            DataSource(
                name="Payment Data",
                source_type="file",
                file_path="payments.csv",
                file_type="csv",
                schema={"columns": ["payment_id", "amount", "date", "customer", "method"]},
                owner_id=1
            )
        ]
        db.add_all(data_sources)

        # Create sample pipeline
        pipeline = Pipeline(
            name="Invoice-Payment Reconciliation",
            description="Reconcile invoices with payments",
            status="draft",
            config={
                "matching": {
                    "method": "hybrid",
                    "threshold": 0.8
                },
                "anomaly_detection": {
                    "methods": ["statistical", "isolation_forest"],
                    "threshold": 0.95
                }
            },
            owner_id=1
        )
        db.add(pipeline)

        # Create sample anomaly detection jobs
        jobs = [
            AnomalyDetectionJob(
                id="job-1",
                data_source_id=1,
                pipeline_id=1,
                status="completed",
                progress=100.0,
                config={
                    "methods": ["statistical"],
                    "threshold": 0.95
                },
                result={
                    "total_rows": 1000,
                    "anomaly_count": 15,
                    "anomaly_indices": [12, 45, 67, 89, 120, 145, 190, 210, 250, 300, 350, 400, 450, 500, 550],
                    "anomalies_by_method": {
                        "statistical": 15
                    }
                },
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                completed_at=datetime.now(timezone.utc)
            ),
            AnomalyDetectionJob(
                id="job-2",
                data_source_id=2,
                pipeline_id=1,
                status="completed",
                progress=100.0,
                config={
                    "methods": ["isolation_forest"],
                    "threshold": 0.9
                },
                result={
                    "total_rows": 950,
                    "anomaly_count": 10,
                    "anomaly_indices": [23, 56, 78, 90, 130, 155, 200, 220, 260, 310],
                    "anomalies_by_method": {
                        "isolation_forest": 10
                    }
                },
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc),
                completed_at=datetime.now(timezone.utc)
            ),
            AnomalyDetectionJob(
                id="job-3",
                data_source_id=1,
                pipeline_id=1,
                status="processing",
                progress=45.0,
                config={
                    "methods": ["statistical", "isolation_forest"],
                    "threshold": 0.95
                },
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
        ]
        db.add_all(jobs)

        db.commit()
        logger.info("Sample data created")
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating sample data: {e}")
    finally:
        db.close()


if __name__ == "__main__":
    logger.info("Creating initial database tables")
    init_db()
    logger.info("Initial database tables created")
