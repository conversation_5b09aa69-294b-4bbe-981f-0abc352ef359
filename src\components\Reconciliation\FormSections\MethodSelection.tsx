import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { MatchingConfigFormValues } from '../validationSchema';
import { 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { InfoIcon } from 'lucide-react';

interface MethodSelectionProps {
  form: UseFormReturn<MatchingConfigFormValues>;
  methods: { id: string; name: string; description: string }[];
}

const MethodSelection: React.FC<MethodSelectionProps> = ({ form, methods }) => {
  const selectedMethods = form.watch('methods');
  
  const handleMethodToggle = (methodId: string, checked: boolean) => {
    const currentMethods = form.getValues('methods');
    
    if (checked) {
      // Add method if not already selected
      if (!currentMethods.includes(methodId)) {
        form.setValue('methods', [...currentMethods, methodId], { shouldValidate: true });
      }
    } else {
      // Remove method if selected
      form.setValue(
        'methods', 
        currentMethods.filter(id => id !== methodId), 
        { shouldValidate: true }
      );
    }
  };

  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="methods"
        render={() => (
          <FormItem>
            <FormLabel>Select Matching Methods</FormLabel>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
              {methods.map((method) => (
                <Card key={method.id} className={`cursor-pointer transition-colors ${
                  selectedMethods.includes(method.id) ? 'border-primary bg-primary/5' : ''
                }`}>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">{method.name}</CardTitle>
                      <FormControl>
                        <Checkbox
                          checked={selectedMethods.includes(method.id)}
                          onCheckedChange={(checked) => 
                            handleMethodToggle(method.id, checked as boolean)
                          }
                        />
                      </FormControl>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <CardDescription>{method.description}</CardDescription>
                  </CardContent>
                </Card>
              ))}
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
      
      {selectedMethods.length > 1 && (
        <Alert className="mt-4">
          <InfoIcon className="h-4 w-4" />
          <AlertDescription>
            You've selected multiple matching methods. Results will be combined, with the highest confidence matches taking precedence.
          </AlertDescription>
        </Alert>
      )}
      
      {selectedMethods.includes('hybrid') && (
        <Alert className="mt-4 bg-blue-50 text-blue-800 border-blue-200">
          <InfoIcon className="h-4 w-4 text-blue-800" />
          <AlertDescription className="text-blue-800">
            Hybrid matching combines TF-IDF, embeddings, and fuzzy matching. You can configure the weights in the Advanced Settings tab.
          </AlertDescription>
        </Alert>
      )}
      
      {selectedMethods.includes('phonetic_name') && (
        <Alert className="mt-4 bg-purple-50 text-purple-800 border-purple-200">
          <InfoIcon className="h-4 w-4 text-purple-800" />
          <AlertDescription className="text-purple-800">
            Phonetic name matching requires you to specify name fields in the Advanced Settings tab.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default MethodSelection;
