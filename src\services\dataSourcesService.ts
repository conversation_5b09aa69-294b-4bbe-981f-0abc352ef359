import axios from 'axios';
import { API_URL } from '@/config';

// Types
export interface DataSource {
  id: string;
  name: string;
  description?: string;
  source_type?: string;
  file_type?: string;
  file_name?: string;
  row_count?: number;
  created_at: string;
  updated_at?: string;
  status?: string;
}

const dataSourcesService = {
  /**
   * Get all data sources
   */
  getDataSources: async (): Promise<DataSource[]> => {
    try {
      const response = await axios.get(`${API_URL}/api/data-sources`);
      return response.data;
    } catch (error) {
      console.error('Error getting data sources:', error);
      throw error;
    }
  },

  /**
   * Get a specific data source by ID
   */
  getDataSource: async (id: string): Promise<DataSource> => {
    try {
      const response = await axios.get(`${API_URL}/api/data-sources/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error getting data source ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get data for a specific data source
   */
  getDataSourceData: async (id: string, limit: number = 100): Promise<{ data: any[], total: number }> => {
    try {
      const response = await axios.get(`${API_URL}/api/data-sources/${id}/data`, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error(`Error getting data for source ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a data source
   */
  deleteDataSource: async (id: string): Promise<void> => {
    try {
      await axios.delete(`${API_URL}/api/data-sources/${id}`);
    } catch (error) {
      console.error(`Error deleting data source ${id}:`, error);
      throw error;
    }
  },

  /**
   * Update a data source
   */
  updateDataSource: async (id: string, data: Partial<DataSource>): Promise<DataSource> => {
    try {
      const response = await axios.patch(`${API_URL}/api/data-sources/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Error updating data source ${id}:`, error);
      throw error;
    }
  }
};

export default dataSourcesService;
