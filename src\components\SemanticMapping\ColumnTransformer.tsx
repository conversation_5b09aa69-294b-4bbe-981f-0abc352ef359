import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Input } from '../ui/input';
import { But<PERSON> } from '../ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Separator } from '../ui/separator';
import { useToast } from '../ui/use-toast';
import {
  Scissors,
  Merge,
  RefreshCw,
  FileSpreadsheet,
  AlertTriangle,
  Loader2,
  Check,
  Plus,
  Trash2,
  ArrowRight
} from 'lucide-react';
import semanticMappingService, {
  SchemaInfo,
  TransformationConfig
} from '../../services/semanticMappingService';

interface ColumnTransformerProps {
  schemas: SchemaInfo[];
  onTransformationApplied: () => void;
}

const ColumnTransformer: React.FC<ColumnTransformerProps> = ({
  schemas,
  onTransformationApplied
}) => {
  const [selectedSource, setSelectedSource] = useState<string>('');
  const [selectedColumn, setSelectedColumn] = useState<string>('');
  const [transformationType, setTransformationType] = useState<'split' | 'merge' | 'recode'>('split');
  const [isLoading, setIsLoading] = useState(false);
  const [previewData, setPreviewData] = useState<any[] | null>(null);
  const [newColumns, setNewColumns] = useState<string[]>([]);

  // Split transformation state
  const [splitSeparator, setSplitSeparator] = useState(' ');
  const [splitColumnNames, setSplitColumnNames] = useState<string[]>(['', '']);

  // Merge transformation state
  const [mergeColumns, setMergeColumns] = useState<string[]>([]);
  const [mergeSeparator, setMergeSeparator] = useState(' ');
  const [mergeColumnName, setMergeColumnName] = useState('');

  // Recode transformation state
  const [recodeValues, setRecodeValues] = useState<{from: string, to: string}[]>([{from: '', to: ''}]);
  const [recodeColumnName, setRecodeColumnName] = useState('');

  const { toast } = useToast();

  // Get selected schema
  const selectedSchema = schemas.find(schema => schema.source_id === selectedSource);

  // Get columns for selected schema
  const columns = selectedSchema?.columns || [];

  // Handle source change
  const handleSourceChange = (value: string) => {
    setSelectedSource(value);
    setSelectedColumn('');
    setPreviewData(null);
    setNewColumns([]);
  };

  // Handle column change
  const handleColumnChange = (value: string) => {
    setSelectedColumn(value);
    setPreviewData(null);
    setNewColumns([]);

    // Reset transformation-specific state
    if (transformationType === 'split') {
      const column = columns.find(col => col.name === value);
      if (column) {
        setSplitColumnNames([`${column.name}_part1`, `${column.name}_part2`]);
      }
    } else if (transformationType === 'merge') {
      setMergeColumns([value]);
      setMergeColumnName(`${value}_merged`);
    } else if (transformationType === 'recode') {
      setRecodeColumnName(`${value}_recoded`);
      setRecodeValues([{from: '', to: ''}]);
    }
  };

  // Handle transformation type change
  const handleTransformationTypeChange = (value: 'split' | 'merge' | 'recode') => {
    setTransformationType(value);
    setPreviewData(null);
    setNewColumns([]);

    // Reset transformation-specific state
    if (value === 'split') {
      const column = columns.find(col => col.name === selectedColumn);
      if (column) {
        setSplitColumnNames([`${column.name}_part1`, `${column.name}_part2`]);
      }
    } else if (value === 'merge') {
      setMergeColumns(selectedColumn ? [selectedColumn] : []);
      setMergeColumnName(selectedColumn ? `${selectedColumn}_merged` : '');
    } else if (value === 'recode') {
      setRecodeColumnName(selectedColumn ? `${selectedColumn}_recoded` : '');
      setRecodeValues([{from: '', to: ''}]);
    }
  };

  // Add merge column
  const addMergeColumn = (value: string) => {
    if (!mergeColumns.includes(value)) {
      setMergeColumns([...mergeColumns, value]);
    }
  };

  // Remove merge column
  const removeMergeColumn = (index: number) => {
    const newMergeColumns = [...mergeColumns];
    newMergeColumns.splice(index, 1);
    setMergeColumns(newMergeColumns);
  };

  // Add recode value
  const addRecodeValue = () => {
    setRecodeValues([...recodeValues, {from: '', to: ''}]);
  };

  // Remove recode value
  const removeRecodeValue = (index: number) => {
    const newRecodeValues = [...recodeValues];
    newRecodeValues.splice(index, 1);
    setRecodeValues(newRecodeValues);
  };

  // Update recode value
  const updateRecodeValue = (index: number, field: 'from' | 'to', value: string) => {
    const newRecodeValues = [...recodeValues];
    newRecodeValues[index][field] = value;
    setRecodeValues(newRecodeValues);
  };

  // Generate preview
  const generatePreview = async () => {
    if (!selectedSource || !selectedColumn) {
      toast({
        title: "Missing selection",
        description: "Please select a source and column first.",
        variant: "destructive"
      });
      return;
    }

    let transformation: TransformationConfig;

    if (transformationType === 'split') {
      transformation = {
        type: 'split',
        params: {
          separator: splitSeparator,
          new_columns: splitColumnNames
        }
      };
    } else if (transformationType === 'merge') {
      if (mergeColumns.length < 2) {
        toast({
          title: "Not enough columns",
          description: "Please select at least two columns to merge.",
          variant: "destructive"
        });
        return;
      }

      transformation = {
        type: 'merge',
        params: {
          source_columns: mergeColumns,
          separator: mergeSeparator,
          new_column: mergeColumnName
        }
      };
    } else {
      // Recode
      const mapping: Record<string, string> = {};
      for (const { from, to } of recodeValues) {
        if (from && to) {
          mapping[from] = to;
        }
      }

      if (Object.keys(mapping).length === 0) {
        toast({
          title: "No recode values",
          description: "Please add at least one recode value.",
          variant: "destructive"
        });
        return;
      }

      transformation = {
        type: 'recode',
        params: {
          mapping,
          new_column: recodeColumnName
        }
      };
    }

    setIsLoading(true);
    try {
      const result = await semanticMappingService.transformColumn(
        selectedSource,
        selectedColumn,
        transformation
      );

      setPreviewData(result.preview);
      setNewColumns(result.new_columns);

      toast({
        title: "Preview generated",
        description: `Generated preview with ${result.new_columns.length} new columns.`,
      });
    } catch (error) {
      console.error('Error generating preview:', error);
      toast({
        title: "Error generating preview",
        description: "There was an error generating the preview. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Apply transformation
  const applyTransformation = async () => {
    if (!previewData) {
      toast({
        title: "No preview",
        description: "Please generate a preview first.",
        variant: "destructive"
      });
      return;
    }

    // In a real implementation, this would apply the transformation to the actual data
    // For now, we'll just simulate success
    toast({
      title: "Transformation applied",
      description: `Applied transformation to ${selectedColumn} with ${newColumns.length} new columns.`,
    });

    // Call the callback to refresh schemas
    onTransformationApplied();

    // Reset state
    setPreviewData(null);
    setNewColumns([]);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Column Transformations</CardTitle>
        <CardDescription>
          Split, merge, or recode columns to prepare your data
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">Data Source</label>
            <Select value={selectedSource} onValueChange={handleSourceChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select a data source" />
              </SelectTrigger>
              <SelectContent>
                {schemas.map(schema => (
                  <SelectItem key={schema.source_id} value={schema.source_id}>
                    {schema.source_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Column</label>
            <Select
              value={selectedColumn}
              onValueChange={handleColumnChange}
              disabled={!selectedSource}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a column" />
              </SelectTrigger>
              <SelectContent>
                {columns.map(column => (
                  <SelectItem key={column.name} value={column.name}>
                    {column.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Transformation Type</label>
            <Select
              value={transformationType}
              onValueChange={handleTransformationTypeChange}
              disabled={!selectedColumn}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="split">
                  <div className="flex items-center">
                    <Scissors className="h-4 w-4 mr-2" />
                    Split Column
                  </div>
                </SelectItem>
                <SelectItem value="merge">
                  <div className="flex items-center">
                    <Merge className="h-4 w-4 mr-2" />
                    Merge Columns
                  </div>
                </SelectItem>
                <SelectItem value="recode">
                  <div className="flex items-center">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Recode Values
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Separator className="my-4" />

        {/* Transformation-specific configuration */}
        <div className="mb-4">
          {transformationType === 'split' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Separator</label>
                <Input
                  value={splitSeparator}
                  onChange={e => setSplitSeparator(e.target.value)}
                  placeholder="Space, comma, etc."
                  className="max-w-xs"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">New Column Names</label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {splitColumnNames.map((name, index) => (
                    <Input
                      key={index}
                      value={name}
                      onChange={e => {
                        const newNames = [...splitColumnNames];
                        newNames[index] = e.target.value;
                        setSplitColumnNames(newNames);
                      }}
                      placeholder={`Column ${index + 1} name`}
                    />
                  ))}
                </div>
              </div>
            </div>
          )}

          {transformationType === 'merge' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Columns to Merge</label>
                <div className="space-y-2">
                  {mergeColumns.map((column, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Select
                        value={column}
                        onValueChange={value => {
                          const newColumns = [...mergeColumns];
                          newColumns[index] = value;
                          setMergeColumns(newColumns);
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {columns.map(col => (
                            <SelectItem key={col.name} value={col.name}>
                              {col.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeMergeColumn(index)}
                        disabled={mergeColumns.length <= 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>

                <div className="mt-2">
                  <Select
                    value=""
                    onValueChange={addMergeColumn}
                    disabled={columns.length === 0 || mergeColumns.length >= columns.length}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Add column" />
                    </SelectTrigger>
                    <SelectContent>
                      {columns
                        .filter(col => !mergeColumns.includes(col.name))
                        .map(col => (
                          <SelectItem key={col.name} value={col.name}>
                            {col.name}
                          </SelectItem>
                        ))
                      }
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Separator</label>
                <Input
                  value={mergeSeparator}
                  onChange={e => setMergeSeparator(e.target.value)}
                  placeholder="Space, comma, etc."
                  className="max-w-xs"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">New Column Name</label>
                <Input
                  value={mergeColumnName}
                  onChange={e => setMergeColumnName(e.target.value)}
                  placeholder="Merged column name"
                  className="max-w-xs"
                />
              </div>
            </div>
          )}

          {transformationType === 'recode' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Value Mapping</label>
                <div className="space-y-2">
                  {recodeValues.map((value, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Input
                        value={value.from}
                        onChange={e => updateRecodeValue(index, 'from', e.target.value)}
                        placeholder="Original value"
                      />
                      <span>→</span>
                      <Input
                        value={value.to}
                        onChange={e => updateRecodeValue(index, 'to', e.target.value)}
                        placeholder="New value"
                      />

                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeRecodeValue(index)}
                        disabled={recodeValues.length <= 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={addRecodeValue}
                  className="mt-2"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Value Mapping
                </Button>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">New Column Name</label>
                <Input
                  value={recodeColumnName}
                  onChange={e => setRecodeColumnName(e.target.value)}
                  placeholder="Recoded column name"
                  className="max-w-xs"
                />
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-between items-center mb-4">
          <Button
            onClick={generatePreview}
            disabled={isLoading || !selectedSource || !selectedColumn}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Generate Preview
              </>
            )}
          </Button>

          <Button
            onClick={applyTransformation}
            disabled={!previewData || isLoading}
            variant="default"
          >
            <Check className="h-4 w-4 mr-2" />
            Apply Transformation
          </Button>
        </div>

        {/* Preview */}
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-500">Generating preview...</span>
          </div>
        ) : previewData ? (
          <div>
            <div className="flex items-center mb-2">
              <FileSpreadsheet className="h-5 w-5 mr-2 text-blue-500" />
              <span className="font-medium">Transformation Preview</span>
              {newColumns.length > 0 && (
                <Badge variant="outline" className="ml-2">
                  {newColumns.length} new columns
                </Badge>
              )}
            </div>

            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    {Object.keys(previewData[0] || {}).map(column => (
                      <TableHead key={column} className="whitespace-nowrap">
                        <div className="flex items-center">
                          <span>{column}</span>
                          {newColumns.includes(column) && (
                            <Badge variant="default" className="ml-2 text-xs">
                              New
                            </Badge>
                          )}
                        </div>
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {previewData.map((row, rowIndex) => (
                    <TableRow key={rowIndex}>
                      {Object.keys(row).map(column => (
                        <TableCell key={`${rowIndex}-${column}`} className="whitespace-nowrap">
                          {row[column] !== null ? String(row[column]) : (
                            <span className="text-gray-400 italic">null</span>
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        ) : (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>No preview available</AlertTitle>
            <AlertDescription>
              Configure your transformation and click "Generate Preview" to see the results.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default ColumnTransformer;
