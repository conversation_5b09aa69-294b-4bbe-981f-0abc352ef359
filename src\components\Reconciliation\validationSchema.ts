import * as z from 'zod';

// Validation schema for matching configuration
export const matchingConfigSchema = z.object({
  // Source and target datasets
  sourceDatasetId: z.string().min(1, "Source dataset is required"),
  targetDatasetId: z.string().min(1, "Target dataset is required"),
  
  // Matching methods
  methods: z.array(z.string()).min(1, "At least one matching method is required"),
  
  // Field mappings
  fieldMappings: z.record(z.string(), z.string()).refine(
    (mappings) => Object.keys(mappings).length > 0,
    { message: "At least one field mapping is required" }
  ),
  
  // Thresholds
  thresholds: z.object({
    tfidf: z.number().min(0).max(1).optional().default(0.7),
    embeddings: z.number().min(0).max(1).optional().default(0.7),
    hybrid: z.number().min(0).max(1).optional().default(0.7),
    weighted_field: z.number().min(0).max(1).optional().default(0.7),
    phonetic_name: z.number().min(0).max(1).optional().default(0.7),
    anomaly_factor: z.number().min(0).max(1).optional().default(0.7)
  }),
  
  // Optional configurations
  maxMatches: z.number().int().positive().optional().default(1),
  batchSize: z.number().int().positive().optional().default(10),
  
  // Hybrid matching weights
  hybridWeights: z.object({
    tfidf: z.number().min(0).max(1).optional().default(0.4),
    embeddings: z.number().min(0).max(1).optional().default(0.4),
    fuzzy: z.number().min(0).max(1).optional().default(0.2)
  }).optional(),
  
  // Name fields for phonetic matching
  nameFields: z.object({
    source: z.array(z.string()).optional(),
    target: z.array(z.string()).optional()
  }).optional(),
  
  // Field weights for weighted matching
  fieldWeights: z.record(
    z.string(), 
    z.record(z.string(), z.number().min(0).max(1))
  ).optional(),
  
  // Use fuzzy matching
  useFuzzy: z.boolean().optional().default(true)
});

// Type for the form values
export type MatchingConfigFormValues = z.infer<typeof matchingConfigSchema>;

// Default values for the form
export const defaultMatchingConfig: MatchingConfigFormValues = {
  sourceDatasetId: "",
  targetDatasetId: "",
  methods: ["tfidf"],
  fieldMappings: {},
  thresholds: {
    tfidf: 0.7,
    embeddings: 0.7,
    hybrid: 0.7,
    weighted_field: 0.7,
    phonetic_name: 0.7,
    anomaly_factor: 0.7
  },
  maxMatches: 1,
  batchSize: 10,
  hybridWeights: {
    tfidf: 0.4,
    embeddings: 0.4,
    fuzzy: 0.2
  },
  nameFields: {
    source: [],
    target: []
  },
  fieldWeights: {},
  useFuzzy: true
};

// Helper function to convert form values to API request format
export const formValuesToApiRequest = (
  values: MatchingConfigFormValues
): {
  source_data_id: string;
  target_data_id: string;
  config: any;
} => {
  // Convert field mappings to the format expected by the API
  const fieldMappings: Record<string, string> = {};
  Object.entries(values.fieldMappings).forEach(([source, target]) => {
    if (source && target) {
      fieldMappings[source] = target;
    }
  });

  // Convert thresholds to the format expected by the API
  const thresholds: Record<string, number> = {};
  Object.entries(values.thresholds).forEach(([key, value]) => {
    if (value !== undefined) {
      thresholds[key] = value;
    }
  });

  // Convert field weights to the format expected by the API
  const weights: Record<string, Record<string, number>> = {};
  if (values.fieldWeights) {
    Object.entries(values.fieldWeights).forEach(([source, targetWeights]) => {
      if (Object.keys(targetWeights).length > 0) {
        weights[source] = targetWeights;
      }
    });
  }

  return {
    source_data_id: values.sourceDatasetId,
    target_data_id: values.targetDatasetId,
    config: {
      methods: values.methods,
      field_mappings: fieldMappings,
      thresholds,
      max_matches: values.maxMatches,
      batch_size: values.batchSize,
      weights: Object.keys(weights).length > 0 ? weights : undefined,
      name_fields: values.nameFields && 
                  values.nameFields.source && 
                  values.nameFields.source.length > 0 ? 
                  values.nameFields : undefined,
      tfidf_weight: values.hybridWeights?.tfidf,
      embeddings_weight: values.hybridWeights?.embeddings,
      fuzzy_weight: values.hybridWeights?.fuzzy,
      use_fuzzy: values.useFuzzy
    }
  };
};
