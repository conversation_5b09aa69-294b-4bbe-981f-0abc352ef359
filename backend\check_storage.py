import os
import logging
from dotenv import load_dotenv
from app.utils.local_storage import storage_client

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

def check_storage():
    """Check the local storage for files."""
    # List all files in storage
    files = storage_client.list_files()
    logger.info(f"Found {len(files)} files in storage:")
    for file in files:
        logger.info(f"  - {file}")

if __name__ == "__main__":
    check_storage()
