import logging
import time
from datetime import datetime
from typing import List, Optional

from sqlalchemy.orm import Session

from app.db.session import SessionL<PERSON>al
from app.models.anomaly_detection import AnomalyDetectionJob
from app.models.data_source import DataSource
from app.services.data_cleaning.anomaly_detection import detect_anomalies

logger = logging.getLogger(__name__)


def process_job(job_id: str, db: Session) -> None:
    """
    Process an anomaly detection job.
    
    Args:
        job_id: ID of the job to process
        db: Database session
    """
    try:
        # Get job
        job = db.query(AnomalyDetectionJob).filter(AnomalyDetectionJob.id == job_id).first()
        if not job:
            logger.error(f"Job with ID {job_id} not found")
            return
        
        # Update job status
        job.status = "processing"
        job.progress = 10.0
        db.add(job)
        db.commit()
        db.refresh(job)
        
        # Get data source
        data_source = db.query(DataSource).filter(DataSource.id == job.data_source_id).first()
        if not data_source:
            logger.error(f"Data source with ID {job.data_source_id} not found")
            job.status = "failed"
            job.error_message = f"Data source with ID {job.data_source_id} not found"
            db.add(job)
            db.commit()
            return
        
        # Update progress
        job.progress = 20.0
        db.add(job)
        db.commit()
        
        # Run anomaly detection
        result = detect_anomalies(
            data_source=data_source,
            config=job.config
        )
        
        # Update progress
        job.progress = 90.0
        db.add(job)
        db.commit()
        
        # Update job with result
        job.status = "completed"
        job.progress = 100.0
        job.result = result
        job.completed_at = datetime.utcnow()
        job.output_path = result.get("output_path")
        
        db.add(job)
        db.commit()
        
        logger.info(f"Job {job_id} completed successfully")
    except Exception as e:
        logger.error(f"Error processing job {job_id}: {e}")
        try:
            # Update job status
            job = db.query(AnomalyDetectionJob).filter(AnomalyDetectionJob.id == job_id).first()
            if job:
                job.status = "failed"
                job.error_message = str(e)
                db.add(job)
                db.commit()
        except Exception as inner_e:
            logger.error(f"Error updating job status: {inner_e}")


def get_pending_jobs(db: Session, limit: int = 10) -> List[AnomalyDetectionJob]:
    """
    Get pending anomaly detection jobs.
    
    Args:
        db: Database session
        limit: Maximum number of jobs to return
        
    Returns:
        List of pending jobs
    """
    return db.query(AnomalyDetectionJob).filter(
        AnomalyDetectionJob.status == "pending"
    ).order_by(AnomalyDetectionJob.created_at).limit(limit).all()


def run_worker(interval: int = 60, max_jobs: int = 5) -> None:
    """
    Run the worker process to process pending jobs.
    
    Args:
        interval: Interval between checks for pending jobs (in seconds)
        max_jobs: Maximum number of jobs to process in each iteration
    """
    logger.info("Starting anomaly detection worker")
    
    while True:
        try:
            # Create a new database session
            db = SessionLocal()
            
            # Get pending jobs
            pending_jobs = get_pending_jobs(db, limit=max_jobs)
            
            if pending_jobs:
                logger.info(f"Found {len(pending_jobs)} pending jobs")
                
                # Process each job
                for job in pending_jobs:
                    logger.info(f"Processing job {job.id}")
                    process_job(job.id, db)
            
            # Close the database session
            db.close()
            
            # Sleep for the specified interval
            time.sleep(interval)
        except Exception as e:
            logger.error(f"Error in worker process: {e}")
            time.sleep(interval)


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )
    
    # Run the worker
    run_worker()
