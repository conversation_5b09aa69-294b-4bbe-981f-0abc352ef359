from datetime import datetime
from sqlalchemy import Column, DateTime, Enum, Foreign<PERSON>ey, Integer, JSON, String, Text
from sqlalchemy.orm import relationship

from app.db.base_class import Base


class DataSource(Base):
    __tablename__ = "datasource"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True, nullable=False)
    description = Column(Text, nullable=True)
    source_type = Column(Enum("file", "database", "cloud_service", name="source_type"), nullable=False)
    connection_details = Column(JSON, nullable=True)  # Store connection parameters as JSON
    file_path = Column(String, nullable=True)  # For file uploads, store GCS path
    file_type = Column(String, nullable=True)  # CSV, Excel, etc.
    # sheet_name column is not in the database schema yet
    schema = Column(JSON, nullable=True)  # JSON schema of the data
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Foreign keys
    owner_id = Column(Integer, ForeignKey("user.id"))

    # Relationships
    owner = relationship("User", back_populates="data_sources")
