import { useState, useEffect } from 'react';
import { Card, CardContent } from '../components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../components/ui/tabs';
import { Button } from '../components/ui/button';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>p, <PERSON><PERSON><PERSON><PERSON>gle, Download } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Badge } from '../components/ui/badge';

interface PnLReportProps {
  report: any;
  isLoading: boolean;
  onExport: () => void;
}

const PnLReportView: React.FC<PnLReportProps> = ({
  report,
  isLoading,
  onExport
}) => {
  const [activeTab, setActiveTab] = useState('monthly');

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-center items-center py-12">
            <RefreshCw size={24} className="animate-spin text-gray-400" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!report) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">P&L Report</h2>
          </div>
          <div className="flex flex-col items-center justify-center py-12">
            <BarChart size={48} className="text-gray-300 mb-4" />
            <p className="text-gray-500 mb-4">No report data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Sort months chronologically
  const sortedMonths = Object.keys(report.monthly_breakdown).sort();
  
  // Calculate totals
  const totals = {
    income: 0,
    expenses: 0,
    net: 0
  };
  
  sortedMonths.forEach(month => {
    const data = report.monthly_breakdown[month];
    totals.income += data.income;
    totals.expenses += data.expenses;
    totals.net += data.net;
  });
  
  // Sort categories by net value
  const sortedCategories = Object.keys(report.category_breakdown).sort((a, b) => {
    return report.category_breakdown[b].net - report.category_breakdown[a].net;
  });

  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">P&L Report: {report.name}</h2>
          <Button onClick={onExport} variant="outline" size="sm">
            <Download size={16} className="mr-2" />
            Export
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-green-50 p-4 rounded-md border border-green-200">
            <p className="text-sm text-gray-500">Total Income</p>
            <p className="text-2xl font-bold text-green-600">${totals.income.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</p>
          </div>
          <div className="bg-red-50 p-4 rounded-md border border-red-200">
            <p className="text-sm text-gray-500">Total Expenses</p>
            <p className="text-2xl font-bold text-red-600">${totals.expenses.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</p>
          </div>
          <div className={`p-4 rounded-md border ${totals.net >= 0 ? 'bg-blue-50 border-blue-200' : 'bg-amber-50 border-amber-200'}`}>
            <p className="text-sm text-gray-500">Net Profit/Loss</p>
            <p className={`text-2xl font-bold ${totals.net >= 0 ? 'text-blue-600' : 'text-amber-600'}`}>
              ${totals.net.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
            </p>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="monthly">Monthly Breakdown</TabsTrigger>
            <TabsTrigger value="category">Category Breakdown</TabsTrigger>
          </TabsList>

          <TabsContent value="monthly" className="mt-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Month</TableHead>
                  <TableHead className="text-right">Income</TableHead>
                  <TableHead className="text-right">Expenses</TableHead>
                  <TableHead className="text-right">Net</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedMonths.map(month => {
                  const data = report.monthly_breakdown[month];
                  return (
                    <TableRow key={month}>
                      <TableCell className="font-medium">{formatMonth(month)}</TableCell>
                      <TableCell className="text-right text-green-600">
                        ${data.income.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </TableCell>
                      <TableCell className="text-right text-red-600">
                        ${data.expenses.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </TableCell>
                      <TableCell className={`text-right ${data.net >= 0 ? 'text-blue-600' : 'text-amber-600'}`}>
                        ${data.net.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </TableCell>
                    </TableRow>
                  );
                })}
                <TableRow className="bg-gray-50 font-bold">
                  <TableCell>Total</TableCell>
                  <TableCell className="text-right text-green-600">
                    ${totals.income.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </TableCell>
                  <TableCell className="text-right text-red-600">
                    ${totals.expenses.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </TableCell>
                  <TableCell className={`text-right ${totals.net >= 0 ? 'text-blue-600' : 'text-amber-600'}`}>
                    ${totals.net.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
            
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-4">Monthly Trend</h3>
              <div className="h-60 bg-gray-50 rounded-md border border-gray-200 p-4">
                {/* In a real implementation, this would be a chart */}
                <div className="flex h-full items-end space-x-2">
                  {sortedMonths.map(month => {
                    const data = report.monthly_breakdown[month];
                    const maxValue = Math.max(...sortedMonths.map(m => Math.abs(report.monthly_breakdown[m].net)));
                    const height = maxValue ? (Math.abs(data.net) / maxValue) * 100 : 0;
                    
                    return (
                      <div key={month} className="flex-1 flex flex-col items-center">
                        <div 
                          className={`w-full rounded-t-sm ${data.net >= 0 ? 'bg-blue-500' : 'bg-amber-500'}`} 
                          style={{ height: `${height}%` }}
                        ></div>
                        <div className="text-xs mt-2 text-gray-600 rotate-45 origin-left">
                          {formatMonth(month)}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="category" className="mt-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Category</TableHead>
                  <TableHead className="text-right">Income</TableHead>
                  <TableHead className="text-right">Expenses</TableHead>
                  <TableHead className="text-right">Net</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedCategories.map(category => {
                  const data = report.category_breakdown[category];
                  return (
                    <TableRow key={category}>
                      <TableCell className="font-medium">{category}</TableCell>
                      <TableCell className="text-right text-green-600">
                        ${data.income.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </TableCell>
                      <TableCell className="text-right text-red-600">
                        ${data.expenses.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </TableCell>
                      <TableCell className={`text-right ${data.net >= 0 ? 'text-blue-600' : 'text-amber-600'}`}>
                        ${data.net.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </TableCell>
                    </TableRow>
                  );
                })}
                <TableRow className="bg-gray-50 font-bold">
                  <TableCell>Total</TableCell>
                  <TableCell className="text-right text-green-600">
                    ${totals.income.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </TableCell>
                  <TableCell className="text-right text-red-600">
                    ${totals.expenses.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </TableCell>
                  <TableCell className={`text-right ${totals.net >= 0 ? 'text-blue-600' : 'text-amber-600'}`}>
                    ${totals.net.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
            
            <div className="mt-6">
              <h3 className="text-lg font-medium mb-4">Category Distribution</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 rounded-md border border-gray-200 p-4">
                  <h4 className="text-sm font-medium mb-3 text-center">Income by Category</h4>
                  <div className="space-y-2">
                    {sortedCategories
                      .filter(category => report.category_breakdown[category].income > 0)
                      .sort((a, b) => report.category_breakdown[b].income - report.category_breakdown[a].income)
                      .slice(0, 5)
                      .map(category => {
                        const data = report.category_breakdown[category];
                        const percentage = (data.income / totals.income) * 100;
                        
                        return (
                          <div key={category} className="flex items-center">
                            <div className="w-32 truncate">{category}</div>
                            <div className="flex-1 mx-2 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-green-500 h-2 rounded-full" 
                                style={{ width: `${percentage}%` }}
                              ></div>
                            </div>
                            <div className="text-sm text-gray-500 w-20 text-right">
                              {percentage.toFixed(1)}%
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </div>
                
                <div className="bg-gray-50 rounded-md border border-gray-200 p-4">
                  <h4 className="text-sm font-medium mb-3 text-center">Expenses by Category</h4>
                  <div className="space-y-2">
                    {sortedCategories
                      .filter(category => report.category_breakdown[category].expenses > 0)
                      .sort((a, b) => report.category_breakdown[b].expenses - report.category_breakdown[a].expenses)
                      .slice(0, 5)
                      .map(category => {
                        const data = report.category_breakdown[category];
                        const percentage = (data.expenses / totals.expenses) * 100;
                        
                        return (
                          <div key={category} className="flex items-center">
                            <div className="w-32 truncate">{category}</div>
                            <div className="flex-1 mx-2 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-red-500 h-2 rounded-full" 
                                style={{ width: `${percentage}%` }}
                              ></div>
                            </div>
                            <div className="text-sm text-gray-500 w-20 text-right">
                              {percentage.toFixed(1)}%
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

// Helper function to format month (YYYY-MM to Month YYYY)
function formatMonth(month: string): string {
  const [year, monthNum] = month.split('-');
  const date = new Date(parseInt(year), parseInt(monthNum) - 1, 1);
  return date.toLocaleDateString(undefined, { month: 'long', year: 'numeric' });
}

export default PnLReportView;
