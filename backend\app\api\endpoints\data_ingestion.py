import logging
from typing import Any, List, Optional

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
from sqlalchemy.orm import Session

from app.api.dependencies import get_current_user, get_db
from app.models.data_source import DataSource
from app.models.user import User
from app.schemas.data_source import DataSourceCreate, DataSourceResponse
from app.utils.file_handlers import detect_file_type
from app.utils.gcp_storage import storage_client

router = APIRouter()
logger = logging.getLogger(__name__)


@router.post("/upload", response_model=DataSourceResponse)
async def upload_file(
    file: UploadFile = File(...),
    name: str = Form(...),
    description: Optional[str] = Form(None),
    sheet_name: Optional[str] = Form(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Upload a file for data ingestion.
    """
    try:
        logger.info(f"Received file upload request: filename={file.filename}, name={name}, sheet_name={sheet_name}")

        # Detect file type
        file_type = detect_file_type(file.filename)
        logger.info(f"Detected file type: {file_type}")

        if file_type == "unknown":
            logger.warning(f"Unsupported file type for file: {file.filename}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported file type. Please upload CSV, Excel, or JSON files.",
            )

        # Upload file to GCS
        file_path = f"uploads/{current_user.id}/{file.filename}"
        logger.info(f"Reading file contents for {file.filename}")

        try:
            contents = await file.read()
            file.file.seek(0)  # Reset file pointer
            logger.info(f"Successfully read file contents, size: {len(contents)} bytes")
        except Exception as read_error:
            logger.error(f"Error reading file contents: {read_error}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Error reading file contents: {str(read_error)}",
            )

        # Create a BytesIO object from the contents
        import io
        file_obj = io.BytesIO(contents)

        # Upload to GCS
        try:
            logger.info(f"Uploading file to storage: {file_path}")
            storage_client.upload_file(file_obj, file_path)
            logger.info(f"Successfully uploaded file to storage: {file_path}")
        except Exception as storage_error:
            logger.error(f"Error uploading to storage: {storage_error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error uploading to storage: {str(storage_error)}",
            )

        # Create data source record
        try:
            logger.info(f"Creating data source record in database: {name}")
            data_source = DataSource(
                name=name,
                description=description,
                source_type="file",
                file_path=file_path,
                file_type=file_type,
                # sheet_name field is not in the database schema yet
                owner_id=current_user.id,
            )

            db.add(data_source)
            db.commit()
            db.refresh(data_source)
            logger.info(f"Successfully created data source record with ID: {data_source.id}")

            # Return the data source with a data_source wrapper for compatibility with frontend
            return {"data_source": data_source}
        except Exception as db_error:
            logger.error(f"Database error creating data source: {db_error}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Database error creating data source: {str(db_error)}",
            )
    except HTTPException:
        # Re-raise HTTP exceptions as they already have the correct format
        raise
    except Exception as e:
        logger.error(f"Unexpected error uploading file: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Unexpected error uploading file: {str(e)}",
        )


@router.post("/database", response_model=DataSourceResponse)
async def connect_database(
    data_source: DataSourceCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Connect to a database for data ingestion.
    """
    try:
        # Validate connection details
        if data_source.source_type != "database":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid source type. Expected 'database'.",
            )

        if not data_source.connection_details:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Connection details are required for database sources.",
            )

        # Create data source record
        db_data_source = DataSource(
            name=data_source.name,
            description=data_source.description,
            source_type=data_source.source_type,
            connection_details=data_source.connection_details,
            owner_id=current_user.id,
        )

        db.add(db_data_source)
        db.commit()
        db.refresh(db_data_source)

        return db_data_source
    except Exception as e:
        logger.error(f"Error connecting to database: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error connecting to database: {str(e)}",
        )


@router.post("/cloud-service", response_model=DataSourceResponse)
async def connect_cloud_service(
    data_source: DataSourceCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Connect to a cloud service for data ingestion.
    """
    try:
        # Validate connection details
        if data_source.source_type != "cloud_service":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid source type. Expected 'cloud_service'.",
            )

        if not data_source.connection_details:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Connection details are required for cloud service sources.",
            )

        # Create data source record
        db_data_source = DataSource(
            name=data_source.name,
            description=data_source.description,
            source_type=data_source.source_type,
            connection_details=data_source.connection_details,
            owner_id=current_user.id,
        )

        db.add(db_data_source)
        db.commit()
        db.refresh(db_data_source)

        return db_data_source
    except Exception as e:
        logger.error(f"Error connecting to cloud service: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error connecting to cloud service: {str(e)}",
        )


@router.get("/sources", response_model=List[DataSourceResponse])
async def get_data_sources(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),  # Used in query filter
) -> Any:
    """
    Get all data sources for the current user.
    """
    data_sources = db.query(DataSource).filter(DataSource.owner_id == current_user.id).all()
    return data_sources


@router.get("/sources/{data_source_id}", response_model=DataSourceResponse)
async def get_data_source(
    data_source_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> Any:
    """
    Get a specific data source.
    """
    data_source = db.query(DataSource).filter(
        DataSource.id == data_source_id,
        DataSource.owner_id == current_user.id
    ).first()

    if not data_source:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data source not found",
        )

    return data_source
