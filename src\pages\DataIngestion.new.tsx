import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Upload, 
  Database, 
  Cloud, 
  ArrowLeft, 
  ArrowRight, 
  FileText, 
  CheckCircle2, 
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Function to parse CSV files
const parseCSVFile = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      try {
        const csvData = event.target?.result as string;
        if (!csvData) {
          reject(new Error('Failed to read file'));
          return;
        }
        
        // Split by lines and get headers
        const lines = csvData.split('\n');
        if (lines.length === 0) {
          resolve([]);
          return;
        }
        
        const headers = lines[0].split(',').map(header => 
          header.trim().replace(/^"(.*)"$/, '$1') // Remove quotes if present
        );
        
        // Parse data rows
        const result = [];
        for (let i = 1; i < lines.length; i++) {
          if (!lines[i].trim()) continue; // Skip empty lines
          
          const values = lines[i].split(',').map(value => 
            value.trim().replace(/^"(.*)"$/, '$1') // Remove quotes if present
          );
          
          if (values.length !== headers.length) continue; // Skip malformed rows
          
          const row: Record<string, any> = {};
          headers.forEach((header, index) => {
            // Try to convert to number if possible
            const value = values[index];
            const numValue = Number(value);
            row[header] = isNaN(numValue) ? value : numValue;
          });
          
          result.push(row);
        }
        
        resolve(result);
      } catch (error) {
        reject(error);
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };
    
    reader.readAsText(file);
  });
};

const DataIngestion = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState(1);
  const [selectedSource, setSelectedSource] = useState<string | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [connectionString, setConnectionString] = useState('');
  const [cloudService, setCloudService] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [previewData, setPreviewData] = useState<any[] | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Mock data for preview (only used for database and cloud sources)
  const mockPreviewData = [
    { invoice_id: 'INV-001', date: '2023-09-01', amount: 1250.00, customer: 'Acme Corp', status: 'Paid' },
    { invoice_id: 'INV-002', date: '2023-09-05', amount: 750.50, customer: 'Globex Inc', status: 'Pending' },
    { invoice_id: 'INV-003', date: '2023-09-10', amount: 2100.75, customer: 'Stark Industries', status: 'Paid' },
    { invoice_id: 'INV-004', date: '2023-09-15', amount: 1800.25, customer: 'Wayne Enterprises', status: 'Overdue' },
    { invoice_id: 'INV-005', date: '2023-09-20', amount: 950.00, customer: 'Umbrella Corp', status: 'Paid' },
  ];

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      
      // Validate file type
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        setValidationErrors(['File must be in CSV format']);
      } else {
        setValidationErrors([]);
      }
    }
  };

  const handleSourceSelect = (source: string) => {
    setSelectedSource(source);
    setValidationErrors([]);
  };

  const handleNextStep = async () => {
    if (step === 1 && !selectedSource) {
      setValidationErrors(['Please select a data source']);
      return;
    }

    if (step === 2) {
      if (selectedSource === 'file' && !file) {
        setValidationErrors(['Please select a file to upload']);
        return;
      } else if (selectedSource === 'database' && !connectionString) {
        setValidationErrors(['Please enter a connection string']);
        return;
      } else if (selectedSource === 'cloud' && !cloudService) {
        setValidationErrors(['Please select a cloud service']);
        return;
      }

      // Load actual data for preview
      setIsLoading(true);
      
      try {
        if (selectedSource === 'file' && file) {
          // Parse the actual CSV file
          const parsedData = await parseCSVFile(file);
          
          if (parsedData.length === 0) {
            setValidationErrors(['The file appears to be empty or improperly formatted']);
            setPreviewData(null);
          } else {
            // Limit to first 10 rows for preview
            setPreviewData(parsedData.slice(0, 10));
            setValidationErrors([]);
          }
        } else {
          // For other sources, still use mock data for now
          setTimeout(() => {
            setPreviewData(mockPreviewData);
          }, 1000);
        }
      } catch (error) {
        console.error('Error parsing file:', error);
        setValidationErrors([`Error parsing file: ${error instanceof Error ? error.message : 'Unknown error'}`]);
        setPreviewData(null);
      } finally {
        setTimeout(() => {
          setIsLoading(false);
        }, 1000);
      }
    }

    if (step < 3) {
      setStep(step + 1);
    }
  };

  const handlePreviousStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleStartIngestion = () => {
    setIsLoading(true);
    
    // Simulate upload progress
    const interval = setInterval(() => {
      setUploadProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            navigate('/data-pipeline');
          }, 1000);
          return 100;
        }
        return prev + 10;
      });
    }, 300);
  };

  return (
    <div className="pt-20 pb-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center mb-8">
          <Button 
            variant="ghost" 
            size="sm" 
            className="mr-4"
            onClick={() => navigate('/data-pipeline')}
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Pipeline
          </Button>
          <h1 className="text-3xl font-bold text-navy">Data Ingestion</h1>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${step >= 1 ? 'bg-teal text-white' : 'bg-gray-200 text-gray-500'}`}>
                1
              </div>
              <div className={`h-1 w-12 mx-1 ${step >= 2 ? 'bg-teal' : 'bg-gray-200'}`}></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${step >= 2 ? 'bg-teal text-white' : 'bg-gray-200 text-gray-500'}`}>
                2
              </div>
              <div className={`h-1 w-12 mx-1 ${step >= 3 ? 'bg-teal' : 'bg-gray-200'}`}></div>
              <div className={`flex items-center justify-center w-8 h-8 rounded-full ${step >= 3 ? 'bg-teal text-white' : 'bg-gray-200 text-gray-500'}`}>
                3
              </div>
            </div>
            <div className="text-sm text-gray-500">
              Step {step} of 3
            </div>
          </div>
          <div className="flex justify-between text-sm">
            <span className={step >= 1 ? 'text-teal font-medium' : 'text-gray-500'}>Choose Source</span>
            <span className={step >= 2 ? 'text-teal font-medium' : 'text-gray-500'}>Configure & Preview</span>
            <span className={step >= 3 ? 'text-teal font-medium' : 'text-gray-500'}>Confirm & Start</span>
          </div>
        </div>

        {/* Step Content */}
        <Card>
          <CardHeader>
            <CardTitle>
              {step === 1 && 'Choose Data Source'}
              {step === 2 && 'Configure & Preview Data'}
              {step === 3 && 'Confirm & Start Ingestion'}
            </CardTitle>
            <CardDescription>
              {step === 1 && 'Select the source of your financial data'}
              {step === 2 && 'Configure your data source and preview the data'}
              {step === 3 && 'Review your configuration and start the ingestion process'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {/* Step 1: Choose Data Source */}
            {step === 1 && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card 
                  className={`cursor-pointer hover:border-teal transition-colors ${selectedSource === 'file' ? 'border-teal bg-teal/5' : ''}`}
                  onClick={() => handleSourceSelect('file')}
                >
                  <CardHeader className="text-center pb-2">
                    <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                      <Upload size={24} className="text-blue-600" />
                    </div>
                    <CardTitle className="text-lg">Upload File</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-500 text-center">
                      Upload CSV, Excel, or JSON files containing your financial data.
                    </p>
                  </CardContent>
                </Card>

                <Card 
                  className={`cursor-pointer hover:border-teal transition-colors ${selectedSource === 'database' ? 'border-teal bg-teal/5' : ''}`}
                  onClick={() => handleSourceSelect('database')}
                >
                  <CardHeader className="text-center pb-2">
                    <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                      <Database size={24} className="text-green-600" />
                    </div>
                    <CardTitle className="text-lg">Link Database</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-500 text-center">
                      Connect directly to your SQL, PostgreSQL, or other database systems.
                    </p>
                  </CardContent>
                </Card>

                <Card 
                  className={`cursor-pointer hover:border-teal transition-colors ${selectedSource === 'cloud' ? 'border-teal bg-teal/5' : ''}`}
                  onClick={() => handleSourceSelect('cloud')}
                >
                  <CardHeader className="text-center pb-2">
                    <div className="mx-auto w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                      <Cloud size={24} className="text-purple-600" />
                    </div>
                    <CardTitle className="text-lg">Cloud Service</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-500 text-center">
                      Connect to QuickBooks, Xero, or other cloud accounting services.
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Step 2: Configure & Preview */}
            {step === 2 && (
              <div className="space-y-6">
                {selectedSource === 'file' && (
                  <div className="space-y-4">
                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="file-upload">Upload CSV File</Label>
                      <Input
                        id="file-upload"
                        type="file"
                        accept=".csv"
                        onChange={handleFileChange}
                      />
                      <p className="text-xs text-gray-500">
                        Supported format: CSV (Comma Separated Values)
                      </p>
                    </div>

                    {file && (
                      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                        <FileText size={16} className="text-gray-500" />
                        <span className="text-sm font-medium">{file.name}</span>
                        <span className="text-xs text-gray-500">
                          ({Math.round(file.size / 1024)} KB)
                        </span>
                      </div>
                    )}
                  </div>
                )}

                {selectedSource === 'database' && (
                  <div className="space-y-4">
                    <div className="grid w-full items-center gap-1.5">
                      <Label htmlFor="connection-string">Database Connection String</Label>
                      <Input
                        id="connection-string"
                        type="text"
                        placeholder="e.g., postgresql://username:password@localhost:5432/database"
                        value={connectionString}
                        onChange={(e) => setConnectionString(e.target.value)}
                      />
                      <p className="text-xs text-gray-500">
                        Enter your database connection string. This will be encrypted and stored securely.
                      </p>
                    </div>

                    <div className="grid w-full items-center gap-1.5">
                      <Label htmlFor="table-select">Select Table</Label>
                      <Select disabled={!connectionString}>
                        <SelectTrigger id="table-select">
                          <SelectValue placeholder="Select a table" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="invoices">Invoices</SelectItem>
                          <SelectItem value="payments">Payments</SelectItem>
                          <SelectItem value="customers">Customers</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                {selectedSource === 'cloud' && (
                  <div className="space-y-4">
                    <div className="grid w-full items-center gap-1.5">
                      <Label htmlFor="cloud-service">Select Cloud Service</Label>
                      <Select onValueChange={setCloudService}>
                        <SelectTrigger id="cloud-service">
                          <SelectValue placeholder="Select a service" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="quickbooks">QuickBooks</SelectItem>
                          <SelectItem value="xero">Xero</SelectItem>
                          <SelectItem value="sage">Sage</SelectItem>
                          <SelectItem value="freshbooks">FreshBooks</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {cloudService && (
                      <Button className="w-full">
                        Connect to {cloudService.charAt(0).toUpperCase() + cloudService.slice(1)}
                      </Button>
                    )}
                  </div>
                )}

                {isLoading ? (
                  <div className="flex flex-col items-center justify-center py-8">
                    <RefreshCw size={32} className="animate-spin text-teal mb-4" />
                    <p className="text-gray-500">Loading data preview...</p>
                  </div>
                ) : previewData && previewData.length > 0 ? (
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold mb-2">Data Preview</h3>
                    <div className="border rounded-md overflow-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            {Object.keys(previewData[0]).map((key) => (
                              <th
                                key={key}
                                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                              >
                                {key}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {previewData.map((row, rowIndex) => (
                            <tr key={rowIndex}>
                              {Object.keys(previewData[0]).map((key) => (
                                <td
                                  key={`${rowIndex}-${key}`}
                                  className="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                                >
                                  {row[key] !== undefined ? String(row[key]) : ''}
                                </td>
                              ))}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                    <p className="text-sm text-gray-500 mt-2">
                      Showing {previewData.length} {previewData.length === 10 ? '(preview)' : ''} records
                    </p>
                  </div>
                ) : (
                  <div className="mt-6">
                    <Alert className="bg-yellow-50 border-yellow-200">
                      <AlertCircle className="h-4 w-4 text-yellow-600" />
                      <AlertTitle className="text-yellow-800">No Data</AlertTitle>
                      <AlertDescription className="text-yellow-700">
                        No data could be previewed from the selected file. Please ensure it's a valid CSV file with data.
                      </AlertDescription>
                    </Alert>
                  </div>
                )}
              </div>
            )}

            {/* Step 3: Confirm & Start */}
            {step === 3 && (
              <div className="space-y-6">
                <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
                  <h3 className="text-lg font-semibold mb-2">Configuration Summary</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between py-2 border-b border-gray-200">
                      <span className="text-gray-600">Data Source</span>
                      <span className="font-medium">
                        {selectedSource === 'file' ? 'File Upload' : 
                         selectedSource === 'database' ? 'Database Connection' : 
                         'Cloud Service'}
                      </span>
                    </div>
                    {selectedSource === 'file' && file && (
                      <div className="flex justify-between py-2 border-b border-gray-200">
                        <span className="text-gray-600">File Name</span>
                        <span className="font-medium">{file.name}</span>
                      </div>
                    )}
                    {selectedSource === 'database' && (
                      <div className="flex justify-between py-2 border-b border-gray-200">
                        <span className="text-gray-600">Database</span>
                        <span className="font-medium">PostgreSQL</span>
                      </div>
                    )}
                    {selectedSource === 'cloud' && (
                      <div className="flex justify-between py-2 border-b border-gray-200">
                        <span className="text-gray-600">Service</span>
                        <span className="font-medium">
                          {cloudService.charAt(0).toUpperCase() + cloudService.slice(1)}
                        </span>
                      </div>
                    )}
                    <div className="flex justify-between py-2 border-b border-gray-200">
                      <span className="text-gray-600">Records</span>
                      <span className="font-medium">{previewData ? previewData.length : 0} (preview)</span>
                    </div>
                    <div className="flex justify-between py-2">
                      <span className="text-gray-600">Estimated Processing Time</span>
                      <span className="font-medium">~2 minutes</span>
                    </div>
                  </div>
                </div>

                {isLoading ? (
                  <div className="space-y-4">
                    <div className="flex justify-between mb-1">
                      <span className="text-sm font-medium">Uploading data...</span>
                      <span className="text-sm font-medium">{uploadProgress}%</span>
                    </div>
                    <Progress value={uploadProgress} className="h-2" />
                    <p className="text-sm text-gray-500 mt-2">
                      Please wait while we process your data. This may take a few moments.
                    </p>
                  </div>
                ) : (
                  <Alert className="bg-blue-50 border-blue-200">
                    <AlertCircle className="h-4 w-4 text-blue-600" />
                    <AlertTitle className="text-blue-800">Ready to Start</AlertTitle>
                    <AlertDescription className="text-blue-700">
                      Your data is ready to be processed. Click the button below to start the ingestion process.
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            {/* Validation Errors */}
            {validationErrors.length > 0 && (
              <Alert className="mt-4 bg-red-50 border-red-200">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <AlertTitle className="text-red-800">Validation Error</AlertTitle>
                <AlertDescription className="text-red-700">
                  <ul className="list-disc pl-5 mt-2">
                    {validationErrors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <Button
                variant="outline"
                onClick={handlePreviousStep}
                disabled={step === 1 || isLoading}
              >
                <ArrowLeft size={16} className="mr-2" />
                Previous
              </Button>
              
              {step < 3 ? (
                <Button onClick={handleNextStep} disabled={isLoading}>
                  Next
                  <ArrowRight size={16} className="ml-2" />
                </Button>
              ) : (
                <Button 
                  onClick={handleStartIngestion} 
                  disabled={isLoading}
                  className="bg-teal hover:bg-teal/90"
                >
                  {isLoading ? (
                    <>
                      <RefreshCw size={16} className="mr-2 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    <>
                      Start Ingestion
                      <ArrowRight size={16} className="ml-2" />
                    </>
                  )}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DataIngestion;
