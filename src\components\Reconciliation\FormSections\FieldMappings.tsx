import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { MatchingConfigFormValues } from '../validationSchema';
import { 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { 
  Plus, 
  Trash2, 
  ArrowRight, 
  Search, 
  RefreshCw 
} from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface FieldMappingsProps {
  form: UseFormReturn<MatchingConfigFormValues>;
  sourceFields: string[];
  targetFields: string[];
}

const FieldMappings: React.FC<FieldMappingsProps> = ({ 
  form, 
  sourceFields, 
  targetFields 
}) => {
  const [sourceField, setSourceField] = useState<string>('');
  const [targetField, setTargetField] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');
  
  const fieldMappings = form.watch('fieldMappings');
  
  // Filter fields based on search term
  const filteredSourceFields = sourceFields.filter(field => 
    field.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const filteredTargetFields = targetFields.filter(field => 
    field.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // Add a new field mapping
  const addFieldMapping = () => {
    if (sourceField && targetField) {
      const updatedMappings = { ...fieldMappings, [sourceField]: targetField };
      form.setValue('fieldMappings', updatedMappings, { shouldValidate: true });
      setSourceField('');
      setTargetField('');
    }
  };
  
  // Remove a field mapping
  const removeFieldMapping = (sourceField: string) => {
    const updatedMappings = { ...fieldMappings };
    delete updatedMappings[sourceField];
    form.setValue('fieldMappings', updatedMappings, { shouldValidate: true });
  };
  
  // Auto-map fields with the same name
  const autoMapFields = () => {
    const autoMappings = { ...fieldMappings };
    
    sourceFields.forEach(sourceField => {
      if (targetFields.includes(sourceField)) {
        autoMappings[sourceField] = sourceField;
      }
    });
    
    form.setValue('fieldMappings', autoMappings, { shouldValidate: true });
  };

  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="fieldMappings"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Field Mappings</FormLabel>
            <div className="flex items-center space-x-2 mb-4">
              <Input
                placeholder="Search fields..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-xs"
                prefix={<Search className="h-4 w-4 text-gray-400" />}
              />
              <Button 
                type="button" 
                variant="outline" 
                onClick={autoMapFields}
                className="whitespace-nowrap"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Auto-Map Fields
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end mb-4">
              <div className="md:col-span-2">
                <Select value={sourceField} onValueChange={setSourceField}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select source field" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {filteredSourceFields.map((field) => (
                      <SelectItem 
                        key={field} 
                        value={field}
                        disabled={Object.keys(fieldMappings).includes(field)}
                      >
                        {field}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex justify-center">
                <ArrowRight className="h-6 w-6 text-gray-400" />
              </div>
              
              <div className="md:col-span-2">
                <Select value={targetField} onValueChange={setTargetField}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select target field" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {filteredTargetFields.map((field) => (
                      <SelectItem key={field} value={field}>
                        {field}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Button 
                  type="button" 
                  onClick={addFieldMapping}
                  disabled={!sourceField || !targetField}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add
                </Button>
              </div>
            </div>
            
            {Object.keys(fieldMappings).length > 0 ? (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Source Field</TableHead>
                      <TableHead>Target Field</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Object.entries(fieldMappings).map(([source, target]) => (
                      <TableRow key={source}>
                        <TableCell>
                          <Badge variant="outline" className="font-mono">
                            {source}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-mono">
                            {target}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFieldMapping(source)}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <Alert>
                <AlertDescription>
                  No field mappings defined yet. Add mappings to specify how fields should be matched between datasets.
                </AlertDescription>
              </Alert>
            )}
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default FieldMappings;
