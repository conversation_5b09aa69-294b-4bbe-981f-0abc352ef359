from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class ReportRequest(BaseModel):
    name: str
    report_type: str = Field(..., description="Type of report: 'summary', 'detailed', 'pivot'")
    reconciliation_result_id: int
    config: Optional[Dict[str, Any]] = None


class ReportResponse(BaseModel):
    id: int
    name: str
    report_type: str
    reconciliation_result_id: int
    config: Optional[Dict[str, Any]] = None
    data: Dict[str, Any]
    created_at: datetime
    user_id: int


class NLQueryRequest(BaseModel):
    query: str
    reconciliation_result_id: int
