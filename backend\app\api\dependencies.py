import os
import logging
from typing import Generator, Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON>2<PERSON><PERSON>wordBearer
from jose import jwt
from pydantic import ValidationError
from sqlalchemy.orm import Session

from app.core.config import settings
from app.db.session import SessionLocal
from app.models.user import User
from app.schemas.token import TokenPayload

logger = logging.getLogger(__name__)

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/auth/login", auto_error=False)


def get_db() -> Generator:
    """
    Dependency for getting a database session.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_current_user(
    db: Session = Depends(get_db), token: Optional[str] = Depends(oauth2_scheme)
) -> User:
    """
    Dependency for getting the current authenticated user.
    In development mode, returns a mock user without validating the token.
    """
    # Check if we're in development mode
    if os.environ.get("ENVIRONMENT") == "development" or not token:
        # Return a mock user for development
        logger.warning("Using mock user for development")
        mock_user = User(id=1, email="<EMAIL>", full_name="Development User")
        return mock_user

    # Production mode - validate token
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=["HS256"]
        )
        token_data = TokenPayload(**payload)
    except (jwt.JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )
    user = db.query(User).filter(User.id == token_data.sub).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user
