import logging
import threading
import time
from datetime import datetime
from typing import Dict, Any

from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.models.anomaly_detection import AnomalyDetectionJob
from app.models.data_source import DataSource
from app.services.data_cleaning.anomaly_detection import detect_anomalies

logger = logging.getLogger(__name__)

class BackgroundTaskProcessor:
    """Process background tasks in a separate thread."""
    
    def __init__(self):
        self.running = False
        self.thread = None
    
    def start(self):
        """Start the background task processor."""
        if self.running:
            logger.warning("Background task processor is already running")
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._process_tasks)
        self.thread.daemon = True
        self.thread.start()
        logger.info("Background task processor started")
    
    def stop(self):
        """Stop the background task processor."""
        if not self.running:
            logger.warning("Background task processor is not running")
            return
        
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)
        logger.info("Background task processor stopped")
    
    def _process_tasks(self):
        """Process background tasks."""
        while self.running:
            try:
                # Process anomaly detection jobs
                self._process_anomaly_detection_jobs()
                
                # Sleep for a while
                time.sleep(5)
            except Exception as e:
                logger.error(f"Error processing background tasks: {e}")
                time.sleep(10)
    
    def _process_anomaly_detection_jobs(self):
        """Process pending anomaly detection jobs."""
        # Create a new database session
        db = SessionLocal()
        
        try:
            # Get pending jobs
            pending_jobs = db.query(AnomalyDetectionJob).filter(
                AnomalyDetectionJob.status == "pending"
            ).all()
            
            for job in pending_jobs:
                try:
                    # Update job status
                    job.status = "processing"
                    job.progress = 10.0
                    job.updated_at = datetime.now()
                    db.add(job)
                    db.commit()
                    db.refresh(job)
                    
                    # Get data source
                    data_source = db.query(DataSource).filter(
                        DataSource.id == job.data_source_id
                    ).first()
                    
                    if not data_source:
                        logger.error(f"Data source with ID {job.data_source_id} not found")
                        job.status = "failed"
                        job.error_message = f"Data source with ID {job.data_source_id} not found"
                        job.updated_at = datetime.now()
                        db.add(job)
                        db.commit()
                        continue
                    
                    # Update progress
                    job.progress = 30.0
                    job.updated_at = datetime.now()
                    db.add(job)
                    db.commit()
                    
                    # Run anomaly detection
                    result = detect_anomalies(
                        data_source=data_source,
                        config=job.config
                    )
                    
                    # Update progress
                    job.progress = 90.0
                    job.updated_at = datetime.now()
                    db.add(job)
                    db.commit()
                    
                    # Update job with result
                    job.status = "completed"
                    job.progress = 100.0
                    job.result = result
                    job.completed_at = datetime.now()
                    job.updated_at = datetime.now()
                    db.add(job)
                    db.commit()
                    
                    logger.info(f"Completed anomaly detection job {job.id}")
                except Exception as e:
                    logger.error(f"Error processing anomaly detection job {job.id}: {e}")
                    job.status = "failed"
                    job.error_message = str(e)
                    job.updated_at = datetime.now()
                    db.add(job)
                    db.commit()
        finally:
            db.close()

# Create a singleton instance
background_task_processor = BackgroundTaskProcessor()
