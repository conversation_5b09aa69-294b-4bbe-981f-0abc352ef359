import { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Progress } from '../components/ui/progress';
import { useToast } from '../components/ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../components/ui/dialog';
import SchemaReviewPanel from '../components/SchemaReviewPanel';
import CombinationReviewPanel from '../components/CombinationReviewPanel';
import MultiFileSheetSelector from '../components/MultiFileSheetSelector';
import {
  FileSpreadsheet,
  Upload,
  AlertCircle,
  ArrowRight,
  X,
  Plus,
  Link as LinkIcon,
  RefreshCw,
  Database,
  LineC<PERSON>,
  Brain,
  Layers
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select';
import fileService, { DataSource, CombinedDataset } from '../services/excelService';

interface FileItem {
  file: File;
  name: string;
  type: string;
  size: number;
  category: string;
  selectedSheet?: string;
}

const MultiFileIngestion = () => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [step, setStep] = useState<'select' | 'categorize' | 'relate'>(
    'select'
  );

  // Combined datasets state
  const [combinedDatasets, setCombinedDatasets] = useState<Record<string, CombinedDataset>>({});
  const [showCombineModal, setShowCombineModal] = useState<boolean>(false);
  const [showSchemaReviewModal, setShowSchemaReviewModal] = useState<boolean>(false);
  const [selectedSourceIds, setSelectedSourceIds] = useState<string[]>([]);
  const [selectedSources, setSelectedSources] = useState<Array<{id: string, name: string}>>([]);
  const [combinedDatasetName, setCombinedDatasetName] = useState<string>('');
  const [joinStrategy, setJoinStrategy] = useState<'smart' | 'concat' | 'inner_join' | 'outer_join'>('smart');
  const [isCombining, setIsCombining] = useState<boolean>(false);
  const [fieldMappings, setFieldMappings] = useState<any[]>([]);

  // Sheet selection state
  const [showSheetSelectionModal, setShowSheetSelectionModal] = useState<boolean>(false);
  const [excelFilesForSheetSelection, setExcelFilesForSheetSelection] = useState<File[]>([]);

  const navigate = useNavigate();
  const { toast } = useToast();

  // Fetch existing data sources and combined datasets on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch data sources
        const sources = await fileService.getDataSources();
        setDataSources(sources);

        // Fetch combined datasets
        const combined = await fileService.getCombinedDatasets();
        setCombinedDatasets(combined);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, []);

  const handleFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = event.target.files;
    if (!selectedFiles || selectedFiles.length === 0) return;

    const newFiles: FileItem[] = [];
    const errors: string[] = [];

    // Process each selected file
    Array.from(selectedFiles).forEach(file => {
      // Check if file is an Excel or CSV file
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      const isValidExtension = ['xls', 'xlsx', 'csv'].includes(fileExtension || '');

      if (!isValidExtension) {
        errors.push(`File "${file.name}" is not a valid Excel or CSV file.`);
        return;
      }

      // Add file to the list
      newFiles.push({
        file,
        name: file.name,
        type: fileExtension || '',
        size: file.size,
        category: '' // Will be filled in the categorize step
      });
    });

    if (errors.length > 0) {
      setValidationErrors(errors);
    } else {
      setValidationErrors([]);
    }

    setFiles(prev => [...prev, ...newFiles]);
  }, []);

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const updateFileCategory = (index: number, category: string) => {
    setFiles(prev =>
      prev.map((file, i) =>
        i === index ? { ...file, category } : file
      )
    );
  };

  const handleNextStep = () => {
    if (step === 'select') {
      if (files.length === 0) {
        setValidationErrors(['Please select at least one file to upload.']);
        return;
      }
      setStep('categorize');
    } else if (step === 'categorize') {
      // Check if all files have categories
      const uncategorizedFiles = files.filter(file => !file.category);
      if (uncategorizedFiles.length > 0) {
        setValidationErrors(['Please categorize all files before proceeding.']);
        return;
      }
      setStep('relate');
    }
  };

  const handlePreviousStep = () => {
    if (step === 'categorize') {
      setStep('select');
    } else if (step === 'relate') {
      setStep('categorize');
    }
  };

  const handleUpload = async () => {
    if (files.length === 0) {
      setValidationErrors(['Please select at least one file to upload.']);
      return;
    }

    // Check if there are any Excel files that might need sheet selection
    const excelFiles = files
      .filter(fileItem => {
        const fileExtension = fileItem.file.name.split('.').pop()?.toLowerCase();
        return fileExtension === 'xlsx' || fileExtension === 'xls';
      })
      .filter(fileItem => !fileItem.selectedSheet) // Only include files without a selected sheet
      .map(fileItem => fileItem.file);

    // If there are Excel files without selected sheets, show the sheet selection modal
    if (excelFiles.length > 0) {
      setExcelFilesForSheetSelection(excelFiles);
      setShowSheetSelectionModal(true);
      return;
    }

    // If we get here, all files are ready to be uploaded
    await uploadFiles();
  };

  const uploadFiles = async () => {
    // Use the current files array
    uploadFilesWithSheets([...files]);
  };

  const renderSelectStep = () => (
    <>
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Select Files</h2>
        <p className="text-gray-500 mb-4">
          Select multiple Excel or CSV files to upload and analyze together.
        </p>

        <div className="mb-4">
          <Label htmlFor="file-upload">Select Excel or CSV Files</Label>
          <Input
            id="file-upload"
            type="file"
            accept=".xls,.xlsx,.csv"
            onChange={handleFileChange}
            disabled={isLoading}
            className="mt-1"
            multiple
          />
        </div>
      </div>

      {files.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">Selected Files</h3>
          <div className="space-y-2">
            {files.map((file, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200"
              >
                <div className="flex items-center">
                  <FileSpreadsheet size={20} className="mr-3 text-blue-600" />
                  <div>
                    <p className="font-medium">{file.name}</p>
                    <p className="text-sm text-gray-500">
                      {file.type.toUpperCase()} • {(file.size / 1024).toFixed(2)} KB
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(index)}
                >
                  <X size={16} />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </>
  );

  const renderCategorizeStep = () => (
    <>
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Categorize Files</h2>
        <p className="text-gray-500 mb-4">
          Assign a category to each file to help with data combination.
        </p>
      </div>

      <div className="space-y-4">
        {files.map((file, index) => (
          <div
            key={index}
            className="flex items-center justify-between p-4 bg-gray-50 rounded-md border border-gray-200"
          >
            <div className="flex items-center">
              <FileSpreadsheet size={20} className="mr-3 text-blue-600" />
              <div>
                <p className="font-medium">{file.name}</p>
                <p className="text-sm text-gray-500">
                  {file.type.toUpperCase()} • {(file.size / 1024).toFixed(2)} KB
                </p>
              </div>
            </div>
            <div className="flex items-center">
              <Label htmlFor={`category-${index}`} className="mr-2">Category:</Label>
              <Input
                id={`category-${index}`}
                value={file.category}
                onChange={(e) => updateFileCategory(index, e.target.value)}
                placeholder="e.g., Transactions, Customers"
                className="w-48"
              />
            </div>
          </div>
        ))}
      </div>
    </>
  );

  const renderRelateStep = () => {
    // Group files by category
    const filesByCategory: Record<string, FileItem[]> = {};
    files.forEach(file => {
      if (!filesByCategory[file.category]) {
        filesByCategory[file.category] = [];
      }
      filesByCategory[file.category].push(file);
    });

    return (
      <>
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-4">Define Relationships</h2>
          <p className="text-gray-500 mb-4">
            Review how your files will be combined based on their categories.
          </p>
        </div>

        <div className="bg-gray-50 p-6 rounded-md border border-gray-200 mb-6">
          <h3 className="text-lg font-medium mb-4">Files by Category</h3>

          {Object.entries(filesByCategory).map(([category, categoryFiles]) => (
            <div key={category} className="mb-4">
              <div className="font-medium text-blue-600 mb-2">{category || 'Uncategorized'}</div>
              <div className="pl-4 border-l-2 border-blue-200 space-y-2">
                {categoryFiles.map((file, fileIndex) => (
                  <div
                    key={fileIndex}
                    className="bg-white p-3 rounded-md border border-gray-300 shadow-sm"
                  >
                    <div className="font-medium">{file.name}</div>
                    <div className="text-sm text-gray-500">{file.type.toUpperCase()} • {(file.size / 1024).toFixed(2)} KB</div>
                  </div>
                ))}
              </div>
            </div>
          ))}

          <div className="mt-6 p-4 bg-blue-50 rounded-md border border-blue-200">
            <h4 className="font-medium text-blue-700 mb-2">How Files Will Be Combined</h4>
            <p className="text-sm text-blue-600 mb-2">
              Files will be combined using a simple concatenation approach, with each row tagged with its source file.
            </p>
            <p className="text-sm text-blue-600">
              This allows you to see which file each data point came from in the combined analysis.
            </p>
          </div>
        </div>
      </>
    );
  };

  // Handle combining datasets with AI-powered schema detection
  const handleCombineDatasets = async (strategy: string, name: string) => {
    if (selectedSourceIds.length < 2 || !name) {
      setValidationErrors(['Please select at least 2 files and provide a name for the combined dataset']);
      return;
    }

    setIsCombining(true);
    try {
      // If using smart strategy, show schema review first
      if (strategy === 'smart') {
        // For each source, get field mappings
        const mappings: any[] = [];
        for (const sourceId of selectedSourceIds) {
          try {
            // Get schema inference for this source
            const schemaResult = await fileService.inferSchema(sourceId);
            if (schemaResult && schemaResult.field_mappings) {
              // Get sample data for this source to enhance the field mappings with sample values
              try {
                const sourceData = await fileService.getDataSourceData(sourceId, 10);
                if (sourceData && sourceData.data && sourceData.data.length > 0) {
                  // Add sample values to each field mapping if not already present
                  schemaResult.field_mappings.forEach((mapping: any) => {
                    if (!mapping.sample_values || mapping.sample_values.length === 0) {
                      const sourceField = mapping.source_field;
                      const sampleValues = sourceData.data
                        .map((row: any) => row[sourceField])
                        .filter((val: any) => val !== undefined && val !== null)
                        .slice(0, 5);
                      mapping.sample_values = sampleValues;
                    }
                  });
                }
              } catch (dataError) {
                console.error(`Error getting sample data for source ${sourceId}:`, dataError);
              }

              mappings.push(...schemaResult.field_mappings);
            }
          } catch (error) {
            console.error(`Error getting schema for source ${sourceId}:`, error);
          }
        }

        if (mappings.length > 0) {
          // Store mappings and show schema review modal
          setFieldMappings(mappings);
          setShowSchemaReviewModal(true);
          setIsCombining(false);
          return;
        }
      }

      // Combine the datasets
      const result = await fileService.combineDatasets(
        selectedSourceIds,
        name,
        {}, // No specific relationships for now
        strategy as any // Cast to the expected type
      );

      // Update the combined datasets list
      setCombinedDatasets(prev => ({
        ...prev,
        [result.id]: result
      }));

      // Close the modal and reset state
      setShowCombineModal(false);
      setSelectedSourceIds([]);
      setSelectedSources([]);
      setCombinedDatasetName('');

      // Show success message
      toast({
        title: strategy === 'smart' ? "AI-powered dataset combination complete" : "Datasets combined successfully",
        description: `Created combined dataset "${name}" with ${result.row_count} rows.`,
      });

      // Navigate to the combined analysis page
      navigate(`/combined-analysis/${result.id}`);
    } catch (error) {
      console.error('Error combining datasets:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setValidationErrors([`Error combining datasets: ${errorMessage}`]);
    } finally {
      setIsCombining(false);
    }
  };

  // Handle updating field mappings
  const handleUpdateFieldMappings = (updatedMappings: any[]) => {
    setFieldMappings(updatedMappings);
  };

  // Handle confirming schema and proceeding with combination
  const handleConfirmSchema = async () => {
    setShowSchemaReviewModal(false);
    setIsCombining(true);

    try {
      // Combine the datasets with the updated field mappings
      const result = await fileService.combineDatasets(
        selectedSourceIds,
        combinedDatasetName,
        {}, // No specific relationships for now
        'smart', // Use smart strategy
        fieldMappings // Pass the field mappings
      );

      // Update the combined datasets list
      setCombinedDatasets(prev => ({
        ...prev,
        [result.id]: result
      }));

      // Reset state
      setSelectedSourceIds([]);
      setSelectedSources([]);
      setCombinedDatasetName('');
      setFieldMappings([]);

      // Show success message
      toast({
        title: "AI-powered dataset combination complete",
        description: `Created combined dataset "${combinedDatasetName}" with ${result.row_count} rows.`,
      });

      // Navigate to the combined analysis page
      navigate(`/combined-analysis/${result.id}`);
    } catch (error) {
      console.error('Error combining datasets:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setValidationErrors([`Error combining datasets: ${errorMessage}`]);
    } finally {
      setIsCombining(false);
    }
  };

  // Handle sheet selection completion
  const handleSheetSelectionComplete = (processedFiles: { file: File, name: string, selectedSheet: string }[]) => {
    // Update the files array with the selected sheets
    const updatedFiles = [...files];

    // For each processed file, find the matching file in our state and update it
    processedFiles.forEach(processedFile => {
      const fileIndex = updatedFiles.findIndex(f =>
        f.file.name === processedFile.file.name &&
        f.file.size === processedFile.file.size
      );

      if (fileIndex !== -1) {
        updatedFiles[fileIndex] = {
          ...updatedFiles[fileIndex],
          selectedSheet: processedFile.selectedSheet
        };
      }
    });

    // Update state with the new files array
    setFiles(updatedFiles);

    // Close the modal
    setShowSheetSelectionModal(false);

    // Log the updated files with their selected sheets
    console.log('Files with selected sheets:', updatedFiles.map(f => ({ name: f.name, sheet: f.selectedSheet })));

    // Proceed with upload after a short delay to ensure state is updated
    setTimeout(() => {
      // Use the updated files directly instead of relying on state update
      uploadFilesWithSheets(updatedFiles);
    }, 300); // Increased timeout to ensure state updates are complete
  };

  // New function to upload files with sheets
  const uploadFilesWithSheets = async (filesToUpload: FileItem[]) => {
    setIsLoading(true);
    setValidationErrors([]);

    try {
      // Set up progress tracking
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            return 90; // Hold at 90% until complete
          }
          return prev + 5;
        });
      }, 300);

      console.log('Uploading files with sheets:', filesToUpload.map(f => ({ name: f.name, sheet: f.selectedSheet })));

      // Upload each file sequentially
      const uploadedSources: DataSource[] = [];
      for (const fileItem of filesToUpload) {
        // Check if this is an Excel file that needs a sheet selection
        const fileExtension = fileItem.file.name.split('.').pop()?.toLowerCase();
        const isExcelFile = fileExtension === 'xlsx' || fileExtension === 'xls';

        // Log the file being uploaded
        console.log(`Uploading file: ${fileItem.name}, Sheet: ${fileItem.selectedSheet || 'N/A'}`);

        const result = await fileService.uploadFile(
          fileItem.file,
          fileItem.name,
          isExcelFile ? fileItem.selectedSheet || undefined : undefined
        );

        // Check if the result is a valid DataSource
        if ('requires_sheet_selection' in result) {
          console.error('Sheet selection still required after selection process');
          throw new Error('Sheet selection is required for this Excel file. Please try again.');
        }

        // Cast to DataSource and verify it has an ID
        const dataSource = result as DataSource;
        if (!dataSource || !dataSource.id) {
          console.error('Invalid data source response:', dataSource);
          throw new Error('Invalid data source response from server');
        }

        uploadedSources.push(dataSource);
      }

      // If we have multiple files, combine them
      let combinedDatasetId: string | null = null;
      if (uploadedSources.length > 1) {
        // Create a combined dataset name based on categories
        const categories = filesToUpload.map(f => f.category).filter(Boolean);
        const uniqueCategories = [...new Set(categories)];
        const combinedName = uniqueCategories.length > 0
          ? `Combined ${uniqueCategories.join(' + ')}`
          : `Combined Dataset (${new Date().toLocaleDateString()})`;

        // Combine the datasets
        const sourceIds = uploadedSources.map(source => source.id);
        const combinedDataset = await fileService.combineDatasets(sourceIds, combinedName);
        combinedDatasetId = combinedDataset.id;
      }

      // Complete the progress
      clearInterval(interval);
      setUploadProgress(100);

      toast({
        title: "Files uploaded successfully",
        description: `Uploaded ${uploadedSources.length} files.`,
      });

      // Navigate to the appropriate page
      setTimeout(() => {
        if (combinedDatasetId) {
          // If we combined datasets, go to the combined analysis page
          navigate(`/combined-analysis/${combinedDatasetId}`);
        } else if (uploadedSources.length > 0) {
          // Otherwise go to the first file's analysis page
          navigate(`/excel-analysis/${uploadedSources[0].id}`);
        }
      }, 1000);
    } catch (error) {
      console.error('Error during file upload:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setValidationErrors([`Error during upload: ${errorMessage}`]);
      console.error('Upload error details:', error);
      setUploadProgress(0);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Multi-File Data Ingestion</h1>

      <Card className="mb-6">
        <CardContent className="pt-6">
          {step === 'select' && renderSelectStep()}
          {step === 'categorize' && renderCategorizeStep()}
          {step === 'relate' && renderRelateStep()}

          {validationErrors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
              <div className="flex items-center text-red-800 mb-1">
                <AlertCircle size={16} className="mr-2" />
                <span className="font-medium">Validation errors:</span>
              </div>
              <ul className="list-disc pl-5 text-sm text-red-700">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          {uploadProgress > 0 && (
            <div className="mb-4">
              <Label className="block mb-1">Upload Progress</Label>
              <Progress value={uploadProgress} className="h-2" />
              <p className="text-sm text-gray-500 mt-1">{uploadProgress}% complete</p>
            </div>
          )}

          <div className="flex justify-between mt-6">
            {step !== 'select' ? (
              <Button
                variant="outline"
                onClick={handlePreviousStep}
                disabled={isLoading}
              >
                Back
              </Button>
            ) : (
              <div></div> // Empty div to maintain flex spacing
            )}

            {step !== 'relate' ? (
              <Button
                onClick={handleNextStep}
                disabled={isLoading || files.length === 0}
              >
                Next
              </Button>
            ) : (
              <Button
                onClick={handleUpload}
                disabled={isLoading || files.length === 0}
                className="bg-green-600 hover:bg-green-700"
              >
                {isLoading ? (
                  <>Processing...</>
                ) : (
                  <>
                    <Upload size={16} className="mr-2" />
                    Upload and Process
                  </>
                )}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold">Previously Uploaded Files</h2>
            {dataSources.length >= 2 && (
              <Button
                onClick={() => {
                  // Get selected sources info for the combination panel
                  const sources = dataSources.map(source => ({
                    id: source.id,
                    name: source.name
                  }));
                  setSelectedSources(sources);
                  setSelectedSourceIds(sources.map(s => s.id));
                  setCombinedDatasetName(`Combined Dataset (${new Date().toLocaleDateString()})`);
                  setShowCombineModal(true);
                }}
                className="bg-purple-600 hover:bg-purple-700"
              >
                <Brain size={16} className="mr-2" />
                AI-Powered Combination
              </Button>
            )}
          </div>

          {dataSources.length === 0 ? (
            <p className="text-gray-500">No files have been uploaded yet.</p>
          ) : (
            <div className="space-y-3">
              {dataSources.map((dataSource) => (
                <div
                  key={dataSource.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200 hover:bg-gray-100 cursor-pointer"
                  onClick={() => navigate(`/excel-analysis/${dataSource.id}`)}
                >
                  <div className="flex items-center">
                    <FileSpreadsheet size={20} className="mr-3 text-blue-600" />
                    <div>
                      <p className="font-medium">{dataSource.name}</p>
                      <p className="text-sm text-gray-500">
                        {new Date(dataSource.created_at).toLocaleString()}
                        {dataSource.stats && ` • ${dataSource.stats.cleaned_rows} rows`}
                        {dataSource.file_type && ` • ${dataSource.file_type.toUpperCase()}`}
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/financial-analysis/${dataSource.id}`);
                      }}
                    >
                      <LineChart size={16} />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <ArrowRight size={16} />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Combined Datasets Section */}
      <Card>
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">Combined Datasets</h2>

          <div className="space-y-3">
            {Object.values(combinedDatasets).length === 0 ? (
              <div className="p-6 text-center">
                <Database size={40} className="mx-auto text-gray-300 mb-3" />
                <p className="text-gray-500 mb-2">No combined datasets yet</p>
                <p className="text-sm text-gray-400 mb-4">Select multiple files and click "Combine Files" to create a combined dataset</p>
                {dataSources.length >= 2 && (
                  <Button
                    onClick={() => {
                      // Get selected sources info for the combination panel
                      const sources = dataSources.map(source => ({
                        id: source.id,
                        name: source.name
                      }));
                      setSelectedSources(sources);
                      setSelectedSourceIds(sources.map(s => s.id));
                      setCombinedDatasetName(`Combined Dataset (${new Date().toLocaleDateString()})`);
                      setShowCombineModal(true);
                    }}
                    variant="outline"
                    className="bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100"
                  >
                    <Brain size={16} className="mr-2" />
                    AI-Powered Combination
                  </Button>
                )}
              </div>
            ) : (
              Object.values(combinedDatasets).map((dataset) => (
                <div
                  key={dataset.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200 hover:bg-gray-100 cursor-pointer"
                  onClick={() => navigate(`/combined-analysis/${dataset.id}`)}
                >
                  <div className="flex items-center">
                    <Database size={20} className="mr-3 text-purple-600" />
                    <div>
                      <p className="font-medium">{dataset.name}</p>
                      <p className="text-sm text-gray-500">
                        {new Date(dataset.created_at).toLocaleString()} •
                        {dataset.row_count} rows •
                        {dataset.source_ids.length} source files
                      </p>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <ArrowRight size={16} />
                  </Button>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* AI-Powered Combination Modal */}
      <Dialog open={showCombineModal} onOpenChange={setShowCombineModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>AI-Powered Data Combination</DialogTitle>
          </DialogHeader>
          <CombinationReviewPanel
            sources={selectedSources}
            onCombine={handleCombineDatasets}
            isLoading={isCombining}
          />
        </DialogContent>
      </Dialog>

      {/* Schema Review Modal */}
      <Dialog open={showSchemaReviewModal} onOpenChange={setShowSchemaReviewModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Review AI Schema Detection</DialogTitle>
          </DialogHeader>
          <SchemaReviewPanel
            fieldMappings={fieldMappings}
            onUpdateMapping={handleUpdateFieldMappings}
            onConfirm={handleConfirmSchema}
            isLoading={isCombining}
          />
        </DialogContent>
      </Dialog>

      {/* Sheet Selection Modal */}
      <MultiFileSheetSelector
        isOpen={showSheetSelectionModal}
        onClose={() => setShowSheetSelectionModal(false)}
        files={excelFilesForSheetSelection}
        onComplete={handleSheetSelectionComplete}
      />
    </div>
  );
};

export default MultiFileIngestion;
