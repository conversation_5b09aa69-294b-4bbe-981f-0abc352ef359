import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip, Legend, ResponsiveContainer,
  Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid,
  <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axi<PERSON>
} from 'recharts';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertTriangle, Info } from 'lucide-react';

interface AnomalyDetectionProps {
  totalRows: number;
  anomalyIndices: number[];
  anomaliesByMethod: Record<string, number>;
  anomaliesByColumn: Record<string, number>;
  anomalyRecords: any[];
  anomalyPercentage: number;
}

const AnomalyDetectionChart: React.FC<AnomalyDetectionProps> = ({
  totalRows,
  anomalyIndices,
  anomaliesByMethod,
  anomaliesByColumn,
  anomalyRecords,
  anomalyPercentage
}) => {
  const [selectedAnomaly, setSelectedAnomaly] = useState<any | null>(null);

  // Transform data for charts
  const pieData = [
    { name: 'Normal Data', value: totalRows - anomalyIndices.length },
    { name: 'Anomalies', value: anomalyIndices.length }
  ];

  const methodData = Object.entries(anomaliesByMethod).map(([method, count]) => ({
    method: formatMethodName(method),
    count
  }));

  const columnData = Object.entries(anomaliesByColumn)
    .map(([column, count]) => ({ column, count }))
    .sort((a, b) => b.count - a.count);

  // Colors
  const COLORS = ['#0088FE', '#FF8042', '#00C49F', '#FFBB28', '#A28BFF', '#FF6B6B'];
  const RADIAN = Math.PI / 180;

  // Custom label for pie chart
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }: any) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
        {`${(percent * 100).toFixed(1)}%`}
      </text>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>Anomaly Detection Results</CardTitle>
            <CardDescription>
              Summary of anomalies detected in the data
            </CardDescription>
          </div>
          <Badge 
            className={anomalyPercentage > 10 ? "bg-red-100 text-red-800" : 
                      anomalyPercentage > 5 ? "bg-amber-100 text-amber-800" : 
                      "bg-green-100 text-green-800"}
          >
            {anomalyPercentage.toFixed(1)}% Anomalies
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="summary">
          <TabsList className="mb-4">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="methods">Detection Methods</TabsTrigger>
            <TabsTrigger value="columns">Affected Columns</TabsTrigger>
            <TabsTrigger value="records">Anomaly Records</TabsTrigger>
          </TabsList>
          
          <TabsContent value="summary" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Total Records</p>
                <p className="text-2xl font-bold">{totalRows.toLocaleString()}</p>
              </div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Anomalies Detected</p>
                <p className="text-2xl font-bold">{anomalyIndices.length.toLocaleString()}</p>
              </div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Anomaly Rate</p>
                <p className="text-2xl font-bold">{anomalyPercentage.toFixed(1)}%</p>
              </div>
            </div>
            
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Anomaly Distribution</h3>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={renderCustomizedLabel}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {pieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value: number) => [value.toLocaleString(), 'Records']} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Top Affected Columns</h3>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={columnData.slice(0, 5)}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" />
                      <YAxis dataKey="column" type="category" width={100} />
                      <Tooltip formatter={(value: number) => [value.toLocaleString(), 'Anomalies']} />
                      <Bar dataKey="count" fill="#8884d8" radius={[0, 4, 4, 0]}>
                        {columnData.slice(0, 5).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="methods">
            <div className="space-y-6">
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={methodData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="method" />
                    <YAxis />
                    <Tooltip formatter={(value: number) => [value.toLocaleString(), 'Anomalies']} />
                    <Legend />
                    <Bar dataKey="count" name="Anomalies Detected" fill="#8884d8" radius={[4, 4, 0, 0]}>
                      {methodData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-lg flex items-start">
                <Info className="text-blue-500 mr-2 mt-0.5 shrink-0" size={16} />
                <div className="text-sm text-blue-700">
                  <p className="font-medium mb-1">About Detection Methods</p>
                  <ul className="list-disc pl-5 space-y-1">
                    <li><strong>Statistical:</strong> Uses Z-score or IQR to identify values that deviate significantly from the norm</li>
                    <li><strong>Isolation Forest:</strong> Machine learning algorithm that isolates observations by randomly selecting a feature and a split value</li>
                    <li><strong>Local Outlier Factor:</strong> Identifies anomalies by measuring the local deviation of a data point with respect to its neighbors</li>
                    <li><strong>Domain Rules:</strong> Custom business rules specific to the data domain</li>
                  </ul>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="columns">
            <div className="space-y-4">
              {columnData.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium">{item.column}</h4>
                    <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                      <div 
                        className="bg-blue-600 h-2.5 rounded-full" 
                        style={{ width: `${(item.count / Math.max(...columnData.map(d => d.count))) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  <Badge variant="outline" className="ml-4 text-sm whitespace-nowrap">
                    {item.count.toLocaleString()} anomalies
                  </Badge>
                </div>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="records">
            {anomalyRecords.length > 0 ? (
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-slate-100">
                        {Object.keys(anomalyRecords[0]).map((key) => (
                          <th key={key} className="p-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {key}
                          </th>
                        ))}
                        <th className="p-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {anomalyRecords.map((record, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-slate-50'}>
                          {Object.entries(record).map(([key, value]) => (
                            <td key={key} className="p-2 text-sm">
                              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                            </td>
                          ))}
                          <td className="p-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => setSelectedAnomaly(record)}
                            >
                              Details
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                {selectedAnomaly && (
                  <div className="mt-4 p-4 border rounded-lg bg-slate-50">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-lg font-medium">Anomaly Details</h3>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => setSelectedAnomaly(null)}
                      >
                        Close
                      </Button>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(selectedAnomaly).map(([key, value]) => (
                        <div key={key}>
                          <p className="text-sm text-gray-500">{key}</p>
                          <p className="font-medium">
                            {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <AlertTriangle className="h-12 w-12 text-amber-500 mb-4" />
                <h3 className="text-lg font-medium">No Anomaly Records Available</h3>
                <p className="text-gray-500 mt-2">
                  Detailed anomaly records are not available for preview.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

// Helper function to format method names
function formatMethodName(method: string): string {
  switch (method) {
    case 'statistical':
      return 'Statistical';
    case 'isolation_forest':
      return 'Isolation Forest';
    case 'local_outlier_factor':
      return 'Local Outlier Factor';
    case 'domain_rules':
      return 'Domain Rules';
    default:
      return method
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
  }
}

export default AnomalyDetectionChart;
