"""
Advanced matching algorithms for data reconciliation.

This module provides sophisticated matching algorithms including:
- TF-IDF based fuzzy matching
- Embeddings-based semantic matching
- Hybrid matching
- Weighted field matching
- Phonetic matching
- Configurable threshold system
"""

import logging
import re
from typing import Any, Dict, List, Optional, Tuple, Union

import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import jellyfish
from fuzzywuzzy import fuzz

from app.utils.llm_client import llm_client

logger = logging.getLogger(__name__)

def tfidf_match(
    data_a: List[Dict[str, Any]],
    data_b: List[Dict[str, Any]],
    source_fields: List[str],
    target_fields: List[str],
    threshold: float = 0.7,
    max_matches: int = 1
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Match records using TF-IDF vectorization and cosine similarity.

    Args:
        data_a: First dataset
        data_b: Second dataset
        source_fields: Fields to match from the first dataset
        target_fields: Fields to match from the second dataset
        threshold: Similarity threshold for matches
        max_matches: Maximum number of matches to return per record

    Returns:
        Tuple containing:
        - List of matches
        - List of anomalies (partial matches below threshold)
    """
    if len(source_fields) != len(target_fields):
        raise ValueError("Source and target fields must have the same length")

    # Prepare text representations for each record
    texts_a = []
    for record in data_a:
        text_parts = []
        for field in source_fields:
            if field in record and record[field] is not None:
                text_parts.append(str(record[field]))
        texts_a.append(" ".join(text_parts))

    texts_b = []
    for record in data_b:
        text_parts = []
        for field in target_fields:
            if field in record and record[field] is not None:
                text_parts.append(str(record[field]))
        texts_b.append(" ".join(text_parts))

    # Create TF-IDF vectorizer
    vectorizer = TfidfVectorizer(analyzer='word', ngram_range=(1, 2), min_df=1, stop_words='english')

    # Fit and transform all texts
    all_texts = texts_a + texts_b
    tfidf_matrix = vectorizer.fit_transform(all_texts)

    # Split the matrix back into A and B parts
    tfidf_a = tfidf_matrix[:len(texts_a)]
    tfidf_b = tfidf_matrix[len(texts_a):]

    # Calculate cosine similarity between all pairs
    similarity_matrix = cosine_similarity(tfidf_a, tfidf_b)

    # Find matches and anomalies
    matches = []
    anomalies = []
    matched_b_indices = set()  # Track matched indices in B to avoid duplicates

    for i, similarities in enumerate(similarity_matrix):
        # Get indices of top matches
        top_indices = np.argsort(similarities)[::-1][:max_matches]

        for j in top_indices:
            # Skip if this record from B is already matched and we're not allowing duplicates
            if j in matched_b_indices:
                continue

            similarity = similarities[j]

            # Create match details
            match_details = {
                "source_record": data_a[i],
                "target_record": data_b[j],
                "source_index": i,
                "target_index": j,
                "confidence": float(similarity),
                "matched_fields": dict(zip(source_fields, target_fields)),
                "match_method": "tfidf"
            }

            # Add to matches or anomalies based on threshold
            if similarity >= threshold:
                matches.append(match_details)
                matched_b_indices.add(j)
            elif similarity >= threshold * 0.7:  # Lower threshold for anomalies
                match_details["anomaly_type"] = "tfidf_below_threshold"
                anomalies.append(match_details)

    return matches, anomalies


def embeddings_match(
    data_a: List[Dict[str, Any]],
    data_b: List[Dict[str, Any]],
    source_fields: List[str],
    target_fields: List[str],
    threshold: float = 0.7,
    max_matches: int = 1,
    batch_size: int = 10
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Match records using embeddings-based semantic matching.

    Args:
        data_a: First dataset
        data_b: Second dataset
        source_fields: Fields to match from the first dataset
        target_fields: Fields to match from the second dataset
        threshold: Similarity threshold for matches
        max_matches: Maximum number of matches to return per record
        batch_size: Number of records to process in each batch

    Returns:
        Tuple containing:
        - List of matches
        - List of anomalies (partial matches below threshold)
    """
    if len(source_fields) != len(target_fields):
        raise ValueError("Source and target fields must have the same length")

    # Prepare text representations for each record
    texts_a = []
    for record in data_a:
        text_parts = []
        for field in source_fields:
            if field in record and record[field] is not None:
                text_parts.append(f"{field}: {record[field]}")
        texts_a.append(" | ".join(text_parts))

    texts_b = []
    for record in data_b:
        text_parts = []
        for field in target_fields:
            if field in record and record[field] is not None:
                text_parts.append(f"{field}: {record[field]}")
        texts_b.append(" | ".join(text_parts))

    # Generate embeddings using LLM client
    embeddings_a = generate_embeddings_in_batches(texts_a, batch_size)
    embeddings_b = generate_embeddings_in_batches(texts_b, batch_size)

    # Convert to numpy arrays for faster computation
    embeddings_a_np = np.array(embeddings_a)
    embeddings_b_np = np.array(embeddings_b)

    # Calculate cosine similarity between all pairs
    similarity_matrix = cosine_similarity(embeddings_a_np, embeddings_b_np)

    # Find matches and anomalies
    matches = []
    anomalies = []
    matched_b_indices = set()  # Track matched indices in B to avoid duplicates

    for i, similarities in enumerate(similarity_matrix):
        # Get indices of top matches
        top_indices = np.argsort(similarities)[::-1][:max_matches]

        for j in top_indices:
            # Skip if this record from B is already matched and we're not allowing duplicates
            if j in matched_b_indices:
                continue

            similarity = similarities[j]

            # Create match details
            match_details = {
                "source_record": data_a[i],
                "target_record": data_b[j],
                "source_index": i,
                "target_index": j,
                "confidence": float(similarity),
                "matched_fields": dict(zip(source_fields, target_fields)),
                "match_method": "embeddings"
            }

            # Add to matches or anomalies based on threshold
            if similarity >= threshold:
                matches.append(match_details)
                matched_b_indices.add(j)
            elif similarity >= threshold * 0.7:  # Lower threshold for anomalies
                match_details["anomaly_type"] = "embeddings_below_threshold"
                anomalies.append(match_details)

    return matches, anomalies


def generate_embeddings_in_batches(texts: List[str], batch_size: int) -> List[List[float]]:
    """
    Generate embeddings for a list of texts in batches.

    Args:
        texts: List of texts to generate embeddings for
        batch_size: Number of texts to process in each batch

    Returns:
        List of embeddings (each embedding is a list of floats)
    """
    all_embeddings = []

    # Process in batches to avoid overwhelming the API
    for i in range(0, len(texts), batch_size):
        batch_texts = texts[i:i+batch_size]

        try:
            # Use LLM client to generate embeddings
            # This is a placeholder - replace with actual embedding generation code
            # based on your LLM client implementation
            batch_embeddings = llm_client.generate_embeddings(batch_texts)
            all_embeddings.extend(batch_embeddings)
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            # Return zero embeddings as fallback
            zero_embeddings = [[0.0] * 768 for _ in range(len(batch_texts))]
            all_embeddings.extend(zero_embeddings)

    return all_embeddings


def hybrid_match(
    data_a: List[Dict[str, Any]],
    data_b: List[Dict[str, Any]],
    source_fields: List[str],
    target_fields: List[str],
    threshold: float = 0.7,
    max_matches: int = 1,
    tfidf_weight: float = 0.4,
    embeddings_weight: float = 0.4,
    fuzzy_weight: float = 0.2,
    batch_size: int = 10
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Match records using a hybrid approach combining TF-IDF, embeddings, and fuzzy matching.

    Args:
        data_a: First dataset
        data_b: Second dataset
        source_fields: Fields to match from the first dataset
        target_fields: Fields to match from the second dataset
        threshold: Similarity threshold for matches
        max_matches: Maximum number of matches to return per record
        tfidf_weight: Weight for TF-IDF similarity (0-1)
        embeddings_weight: Weight for embeddings similarity (0-1)
        fuzzy_weight: Weight for fuzzy matching similarity (0-1)
        batch_size: Number of records to process in each batch for embeddings

    Returns:
        Tuple containing:
        - List of matches
        - List of anomalies (partial matches below threshold)
    """
    if len(source_fields) != len(target_fields):
        raise ValueError("Source and target fields must have the same length")

    # Ensure weights sum to 1
    total_weight = tfidf_weight + embeddings_weight + fuzzy_weight
    if abs(total_weight - 1.0) > 0.001:  # Allow for small floating point errors
        tfidf_weight /= total_weight
        embeddings_weight /= total_weight
        fuzzy_weight /= total_weight

    # Prepare text representations for each record
    texts_a = []
    for record in data_a:
        text_parts = []
        for field in source_fields:
            if field in record and record[field] is not None:
                text_parts.append(str(record[field]))
        texts_a.append(" ".join(text_parts))

    texts_b = []
    for record in data_b:
        text_parts = []
        for field in target_fields:
            if field in record and record[field] is not None:
                text_parts.append(str(record[field]))
        texts_b.append(" ".join(text_parts))

    # 1. TF-IDF Vectorization
    vectorizer = TfidfVectorizer(analyzer='word', ngram_range=(1, 2), min_df=1, stop_words='english')
    all_texts = texts_a + texts_b
    tfidf_matrix = vectorizer.fit_transform(all_texts)
    tfidf_a = tfidf_matrix[:len(texts_a)]
    tfidf_b = tfidf_matrix[len(texts_a):]
    tfidf_similarity = cosine_similarity(tfidf_a, tfidf_b)

    # 2. Embeddings-based similarity
    if embeddings_weight > 0:
        # Generate embeddings
        embeddings_a = generate_embeddings_in_batches(texts_a, batch_size)
        embeddings_b = generate_embeddings_in_batches(texts_b, batch_size)

        # Convert to numpy arrays
        embeddings_a_np = np.array(embeddings_a)
        embeddings_b_np = np.array(embeddings_b)

        # Calculate similarity
        embeddings_similarity = cosine_similarity(embeddings_a_np, embeddings_b_np)
    else:
        # Create zero matrix if embeddings weight is 0
        embeddings_similarity = np.zeros((len(texts_a), len(texts_b)))

    # 3. Fuzzy matching similarity
    fuzzy_similarity = np.zeros((len(texts_a), len(texts_b)))
    if fuzzy_weight > 0:
        for i, text_a in enumerate(texts_a):
            for j, text_b in enumerate(texts_b):
                # Calculate fuzzy match ratio
                ratio = fuzz.token_sort_ratio(text_a, text_b) / 100.0
                fuzzy_similarity[i, j] = ratio

    # Combine similarities with weights
    combined_similarity = (
        tfidf_weight * tfidf_similarity +
        embeddings_weight * embeddings_similarity +
        fuzzy_weight * fuzzy_similarity
    )

    # Find matches and anomalies
    matches = []
    anomalies = []
    matched_b_indices = set()  # Track matched indices in B to avoid duplicates

    for i, similarities in enumerate(combined_similarity):
        # Get indices of top matches
        top_indices = np.argsort(similarities)[::-1][:max_matches]

        for j in top_indices:
            # Skip if this record from B is already matched and we're not allowing duplicates
            if j in matched_b_indices:
                continue

            similarity = similarities[j]

            # Get individual similarities for detailed reporting
            tfidf_sim = tfidf_similarity[i, j]
            embeddings_sim = embeddings_similarity[i, j] if embeddings_weight > 0 else 0
            fuzzy_sim = fuzzy_similarity[i, j] if fuzzy_weight > 0 else 0

            # Create match details
            match_details = {
                "source_record": data_a[i],
                "target_record": data_b[j],
                "source_index": i,
                "target_index": j,
                "confidence": float(similarity),
                "matched_fields": dict(zip(source_fields, target_fields)),
                "match_method": "hybrid",
                "similarity_details": {
                    "tfidf": float(tfidf_sim),
                    "embeddings": float(embeddings_sim),
                    "fuzzy": float(fuzzy_sim)
                }
            }

            # Add to matches or anomalies based on threshold
            if similarity >= threshold:
                matches.append(match_details)
                matched_b_indices.add(j)
            elif similarity >= threshold * 0.7:  # Lower threshold for anomalies
                match_details["anomaly_type"] = "hybrid_below_threshold"
                anomalies.append(match_details)

    return matches, anomalies


def weighted_field_match(
    data_a: List[Dict[str, Any]],
    data_b: List[Dict[str, Any]],
    field_weights: Dict[str, Dict[str, float]],
    threshold: float = 0.7,
    max_matches: int = 1,
    use_fuzzy: bool = True
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Match records using weighted field matching with configurable weights per field.

    Args:
        data_a: First dataset
        data_b: Second dataset
        field_weights: Dictionary mapping source fields to target fields with weights
                      Format: {"source_field": {"target_field": weight}}
        threshold: Similarity threshold for matches
        max_matches: Maximum number of matches to return per record
        use_fuzzy: Whether to use fuzzy matching for string fields

    Returns:
        Tuple containing:
        - List of matches
        - List of anomalies (partial matches below threshold)
    """
    # Validate field weights
    if not field_weights:
        raise ValueError("Field weights dictionary cannot be empty")

    # Calculate similarity matrix
    similarity_matrix = np.zeros((len(data_a), len(data_b)))
    field_similarities = {}

    # For each record in dataset A
    for i, record_a in enumerate(data_a):
        # For each record in dataset B
        for j, record_b in enumerate(data_b):
            # Calculate weighted similarity across all fields
            total_weight = 0
            weighted_similarity = 0
            field_sim_details = {}

            # For each source field and its target field mappings
            for source_field, target_mappings in field_weights.items():
                # Skip if source field is not in record A
                if source_field not in record_a or record_a[source_field] is None:
                    continue

                source_value = record_a[source_field]

                # For each target field and its weight
                for target_field, weight in target_mappings.items():
                    # Skip if target field is not in record B
                    if target_field not in record_b or record_b[target_field] is None:
                        continue

                    target_value = record_b[target_field]
                    total_weight += weight

                    # Calculate field similarity based on data type
                    field_similarity = calculate_field_similarity(
                        source_value, target_value, use_fuzzy=use_fuzzy
                    )

                    # Add weighted similarity
                    weighted_similarity += field_similarity * weight

                    # Store field similarity details
                    field_sim_details[f"{source_field}:{target_field}"] = field_similarity

            # Normalize by total weight
            if total_weight > 0:
                similarity_matrix[i, j] = weighted_similarity / total_weight

            # Store field similarities for this pair
            field_similarities[(i, j)] = field_sim_details

    # Find matches and anomalies
    matches = []
    anomalies = []
    matched_b_indices = set()  # Track matched indices in B to avoid duplicates

    for i, similarities in enumerate(similarity_matrix):
        # Get indices of top matches
        top_indices = np.argsort(similarities)[::-1][:max_matches]

        for j in top_indices:
            # Skip if this record from B is already matched and we're not allowing duplicates
            if j in matched_b_indices:
                continue

            similarity = similarities[j]

            # Create match details
            match_details = {
                "source_record": data_a[i],
                "target_record": data_b[j],
                "source_index": i,
                "target_index": j,
                "confidence": float(similarity),
                "field_similarities": field_similarities.get((i, j), {}),
                "match_method": "weighted_field"
            }

            # Add to matches or anomalies based on threshold
            if similarity >= threshold:
                matches.append(match_details)
                matched_b_indices.add(j)
            elif similarity >= threshold * 0.7:  # Lower threshold for anomalies
                match_details["anomaly_type"] = "weighted_field_below_threshold"
                anomalies.append(match_details)

    return matches, anomalies


def calculate_field_similarity(value1: Any, value2: Any, use_fuzzy: bool = True) -> float:
    """
    Calculate similarity between two field values based on their types.

    Args:
        value1: First value
        value2: Second value
        use_fuzzy: Whether to use fuzzy matching for string comparison

    Returns:
        Similarity score between 0 and 1
    """
    # Convert to strings for comparison
    str_value1 = str(value1).lower().strip()
    str_value2 = str(value2).lower().strip()

    # Exact match check
    if str_value1 == str_value2:
        return 1.0

    # Handle numeric values
    if isinstance(value1, (int, float)) and isinstance(value2, (int, float)):
        # Calculate relative difference
        max_val = max(abs(value1), abs(value2))
        if max_val > 0:
            diff = abs(value1 - value2) / max_val
            return max(0, 1 - diff)  # Ensure non-negative
        else:
            return 1.0  # Both values are 0

    # Handle string values with fuzzy matching
    if isinstance(value1, str) and isinstance(value2, str) and use_fuzzy:
        # Use token sort ratio for better handling of word order differences
        return fuzz.token_sort_ratio(str_value1, str_value2) / 100.0

    # Default case - no match
    return 0.0


def phonetic_name_match(
    data_a: List[Dict[str, Any]],
    data_b: List[Dict[str, Any]],
    source_name_fields: List[str],
    target_name_fields: List[str],
    threshold: float = 0.7,
    max_matches: int = 1,
    additional_fields: Optional[Dict[str, Dict[str, float]]] = None
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Match records using phonetic matching for name fields, with optional additional fields.

    Args:
        data_a: First dataset
        data_b: Second dataset
        source_name_fields: Name fields to match from the first dataset
        target_name_fields: Name fields to match from the second dataset
        threshold: Similarity threshold for matches
        max_matches: Maximum number of matches to return per record
        additional_fields: Optional additional fields to consider with weights
                          Format: {"source_field": {"target_field": weight}}

    Returns:
        Tuple containing:
        - List of matches
        - List of anomalies (partial matches below threshold)
    """
    if len(source_name_fields) != len(target_name_fields):
        raise ValueError("Source and target name fields must have the same length")

    # Calculate similarity matrix
    similarity_matrix = np.zeros((len(data_a), len(data_b)))
    phonetic_details = {}

    # For each record in dataset A
    for i, record_a in enumerate(data_a):
        # For each record in dataset B
        for j, record_b in enumerate(data_b):
            # Calculate phonetic similarity for name fields
            name_similarity = 0
            name_count = 0
            phonetic_sim_details = {}

            # Compare each name field pair
            for source_field, target_field in zip(source_name_fields, target_name_fields):
                # Skip if fields are not in records
                if (source_field not in record_a or record_a[source_field] is None or
                    target_field not in record_b or record_b[target_field] is None):
                    continue

                source_name = str(record_a[source_field]).lower().strip()
                target_name = str(record_b[target_field]).lower().strip()

                # Skip empty names
                if not source_name or not target_name:
                    continue

                # Calculate phonetic similarity
                phonetic_sim = calculate_phonetic_similarity(source_name, target_name)
                name_similarity += phonetic_sim
                name_count += 1

                # Store phonetic similarity details
                phonetic_sim_details[f"{source_field}:{target_field}"] = phonetic_sim

            # Calculate average name similarity
            avg_name_similarity = name_similarity / name_count if name_count > 0 else 0

            # If additional fields are provided, include them in the similarity calculation
            if additional_fields and avg_name_similarity > 0:
                # Calculate similarity for additional fields
                additional_similarity = 0
                additional_weight = 0

                for source_field, target_mappings in additional_fields.items():
                    # Skip if source field is not in record A
                    if source_field not in record_a or record_a[source_field] is None:
                        continue

                    source_value = record_a[source_field]

                    # For each target field and its weight
                    for target_field, weight in target_mappings.items():
                        # Skip if target field is not in record B
                        if target_field not in record_b or record_b[target_field] is None:
                            continue

                        target_value = record_b[target_field]

                        # Calculate field similarity
                        field_similarity = calculate_field_similarity(source_value, target_value)
                        additional_similarity += field_similarity * weight
                        additional_weight += weight

                        # Store similarity details
                        phonetic_sim_details[f"{source_field}:{target_field}"] = field_similarity

                # Combine name similarity with additional fields
                # Name similarity gets 70% weight, additional fields get 30%
                if additional_weight > 0:
                    similarity_matrix[i, j] = (0.7 * avg_name_similarity) + \
                                             (0.3 * (additional_similarity / additional_weight))
                else:
                    similarity_matrix[i, j] = avg_name_similarity
            else:
                # Use only name similarity
                similarity_matrix[i, j] = avg_name_similarity

            # Store phonetic details for this pair
            phonetic_details[(i, j)] = phonetic_sim_details

    # Find matches and anomalies
    matches = []
    anomalies = []
    matched_b_indices = set()  # Track matched indices in B to avoid duplicates

    for i, similarities in enumerate(similarity_matrix):
        # Get indices of top matches
        top_indices = np.argsort(similarities)[::-1][:max_matches]

        for j in top_indices:
            # Skip if this record from B is already matched and we're not allowing duplicates
            if j in matched_b_indices:
                continue

            similarity = similarities[j]

            # Skip if similarity is 0
            if similarity == 0:
                continue

            # Create match details
            match_details = {
                "source_record": data_a[i],
                "target_record": data_b[j],
                "source_index": i,
                "target_index": j,
                "confidence": float(similarity),
                "phonetic_similarities": phonetic_details.get((i, j), {}),
                "match_method": "phonetic_name"
            }

            # Add to matches or anomalies based on threshold
            if similarity >= threshold:
                matches.append(match_details)
                matched_b_indices.add(j)
            elif similarity >= threshold * 0.7:  # Lower threshold for anomalies
                match_details["anomaly_type"] = "phonetic_name_below_threshold"
                anomalies.append(match_details)

    return matches, anomalies


def calculate_phonetic_similarity(name1: str, name2: str) -> float:
    """
    Calculate phonetic similarity between two names using multiple phonetic algorithms.

    Args:
        name1: First name
        name2: Second name

    Returns:
        Similarity score between 0 and 1
    """
    # Exact match check
    if name1 == name2:
        return 1.0

    # Split names into tokens
    tokens1 = name1.split()
    tokens2 = name2.split()

    # If one name is a subset of the other, give partial credit
    if all(token in name2 for token in tokens1) or all(token in name1 for token in tokens2):
        return 0.8

    # Calculate Soundex similarity
    try:
        soundex1 = jellyfish.soundex(name1)
        soundex2 = jellyfish.soundex(name2)
        soundex_sim = 1.0 if soundex1 == soundex2 else 0.0
    except Exception:
        soundex_sim = 0.0

    # Calculate Metaphone similarity
    try:
        metaphone1 = jellyfish.metaphone(name1)
        metaphone2 = jellyfish.metaphone(name2)
        metaphone_sim = 1.0 if metaphone1 == metaphone2 else 0.0
    except Exception:
        metaphone_sim = 0.0

    # Calculate NYSIIS similarity
    try:
        nysiis1 = jellyfish.nysiis(name1)
        nysiis2 = jellyfish.nysiis(name2)
        nysiis_sim = 1.0 if nysiis1 == nysiis2 else 0.0
    except Exception:
        nysiis_sim = 0.0

    # Calculate Jaro-Winkler similarity
    try:
        jaro_sim = jellyfish.jaro_winkler_similarity(name1, name2)
    except Exception:
        jaro_sim = 0.0

    # Calculate Levenshtein distance similarity
    try:
        max_len = max(len(name1), len(name2))
        if max_len > 0:
            levenshtein_dist = jellyfish.levenshtein_distance(name1, name2)
            levenshtein_sim = 1.0 - (levenshtein_dist / max_len)
        else:
            levenshtein_sim = 1.0
    except Exception:
        levenshtein_sim = 0.0

    # Calculate token sort ratio for handling word order differences
    try:
        token_sort_sim = fuzz.token_sort_ratio(name1, name2) / 100.0
    except Exception:
        token_sort_sim = 0.0

    # Combine all similarities with weights
    # Giving more weight to Jaro-Winkler and token sort ratio as they're more reliable for names
    combined_sim = (
        0.1 * soundex_sim +
        0.1 * metaphone_sim +
        0.1 * nysiis_sim +
        0.3 * jaro_sim +
        0.2 * levenshtein_sim +
        0.2 * token_sort_sim
    )

    return combined_sim


class ThresholdConfig:
    """
    Configuration class for matching thresholds.

    Attributes:
        exact_match: Threshold for exact matching
        tfidf_match: Threshold for TF-IDF matching
        embeddings_match: Threshold for embeddings-based matching
        hybrid_match: Threshold for hybrid matching
        weighted_field_match: Threshold for weighted field matching
        phonetic_name_match: Threshold for phonetic name matching
        anomaly_factor: Factor to multiply the threshold by for anomaly detection
    """
    def __init__(
        self,
        exact_match: float = 1.0,
        tfidf_match: float = 0.7,
        embeddings_match: float = 0.7,
        hybrid_match: float = 0.7,
        weighted_field_match: float = 0.7,
        phonetic_name_match: float = 0.7,
        anomaly_factor: float = 0.7
    ):
        self.exact_match = exact_match
        self.tfidf_match = tfidf_match
        self.embeddings_match = embeddings_match
        self.hybrid_match = hybrid_match
        self.weighted_field_match = weighted_field_match
        self.phonetic_name_match = phonetic_name_match
        self.anomaly_factor = anomaly_factor

    def get_threshold(self, method: str) -> float:
        """
        Get the threshold for a specific matching method.

        Args:
            method: Matching method name

        Returns:
            Threshold value for the method
        """
        if method == "exact":
            return self.exact_match
        elif method == "tfidf":
            return self.tfidf_match
        elif method == "embeddings":
            return self.embeddings_match
        elif method == "hybrid":
            return self.hybrid_match
        elif method == "weighted_field":
            return self.weighted_field_match
        elif method == "phonetic_name":
            return self.phonetic_name_match
        else:
            # Default to a moderate threshold
            return 0.7

    def get_anomaly_threshold(self, method: str) -> float:
        """
        Get the anomaly threshold for a specific matching method.

        Args:
            method: Matching method name

        Returns:
            Anomaly threshold value for the method
        """
        return self.get_threshold(method) * self.anomaly_factor


def match_records(
    data_a: List[Dict[str, Any]],
    data_b: List[Dict[str, Any]],
    config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Match records using multiple algorithms based on configuration.

    Args:
        data_a: First dataset
        data_b: Second dataset
        config: Configuration for matching
            {
                "methods": ["exact", "tfidf", "embeddings", "hybrid", "weighted_field", "phonetic_name"],
                "field_mappings": {"source_field": "target_field", ...},
                "thresholds": {"exact": 1.0, "tfidf": 0.7, ...},
                "weights": {"source_field": {"target_field": weight}, ...},
                "name_fields": {"source": ["first_name", "last_name"], "target": ["first_name", "last_name"]},
                "max_matches": 1
            }

    Returns:
        Dictionary with matching results
    """
    # Extract configuration
    methods = config.get("methods", ["tfidf"])
    field_mappings = config.get("field_mappings", {})
    threshold_config = config.get("thresholds", {})
    weights = config.get("weights", {})
    name_fields = config.get("name_fields", {})
    max_matches = config.get("max_matches", 1)

    # Create threshold configuration
    thresholds = ThresholdConfig(
        exact_match=threshold_config.get("exact", 1.0),
        tfidf_match=threshold_config.get("tfidf", 0.7),
        embeddings_match=threshold_config.get("embeddings", 0.7),
        hybrid_match=threshold_config.get("hybrid", 0.7),
        weighted_field_match=threshold_config.get("weighted_field", 0.7),
        phonetic_name_match=threshold_config.get("phonetic_name", 0.7),
        anomaly_factor=threshold_config.get("anomaly_factor", 0.7)
    )

    # Prepare source and target fields
    source_fields = list(field_mappings.keys())
    target_fields = [field_mappings[field] for field in source_fields]

    # Initialize results
    all_matches = []
    all_anomalies = []
    method_stats = {}

    # Apply each matching method
    for method in methods:
        matches = []
        anomalies = []

        if method == "exact":
            # Exact matching is handled separately
            # Implement exact matching logic here if needed
            pass

        elif method == "tfidf":
            # TF-IDF matching
            matches, anomalies = tfidf_match(
                data_a, data_b, source_fields, target_fields,
                threshold=thresholds.get_threshold("tfidf"),
                max_matches=max_matches
            )

        elif method == "embeddings":
            # Embeddings-based matching
            matches, anomalies = embeddings_match(
                data_a, data_b, source_fields, target_fields,
                threshold=thresholds.get_threshold("embeddings"),
                max_matches=max_matches,
                batch_size=config.get("batch_size", 10)
            )

        elif method == "hybrid":
            # Hybrid matching
            matches, anomalies = hybrid_match(
                data_a, data_b, source_fields, target_fields,
                threshold=thresholds.get_threshold("hybrid"),
                max_matches=max_matches,
                tfidf_weight=config.get("tfidf_weight", 0.4),
                embeddings_weight=config.get("embeddings_weight", 0.4),
                fuzzy_weight=config.get("fuzzy_weight", 0.2),
                batch_size=config.get("batch_size", 10)
            )

        elif method == "weighted_field":
            # Weighted field matching
            matches, anomalies = weighted_field_match(
                data_a, data_b, weights,
                threshold=thresholds.get_threshold("weighted_field"),
                max_matches=max_matches,
                use_fuzzy=config.get("use_fuzzy", True)
            )

        elif method == "phonetic_name":
            # Phonetic name matching
            source_name_fields = name_fields.get("source", [])
            target_name_fields = name_fields.get("target", [])

            if source_name_fields and target_name_fields:
                matches, anomalies = phonetic_name_match(
                    data_a, data_b, source_name_fields, target_name_fields,
                    threshold=thresholds.get_threshold("phonetic_name"),
                    max_matches=max_matches,
                    additional_fields=weights
                )

        # Add method to match details
        for match in matches:
            match["match_method"] = method

        for anomaly in anomalies:
            anomaly["match_method"] = method

        # Add to overall results
        all_matches.extend(matches)
        all_anomalies.extend(anomalies)

        # Record stats for this method
        method_stats[method] = {
            "matches": len(matches),
            "anomalies": len(anomalies)
        }

    # Remove duplicates (same source and target records matched by different methods)
    unique_matches = {}
    for match in all_matches:
        source_index = match["source_index"]
        target_index = match["target_index"]
        key = f"{source_index}:{target_index}"

        # Keep the match with the highest confidence
        if key not in unique_matches or match["confidence"] > unique_matches[key]["confidence"]:
            unique_matches[key] = match

    # Prepare final results
    results = {
        "matches": list(unique_matches.values()),
        "anomalies": all_anomalies,
        "stats": {
            "total_matches": len(unique_matches),
            "total_anomalies": len(all_anomalies),
            "methods": method_stats
        }
    }

    return results