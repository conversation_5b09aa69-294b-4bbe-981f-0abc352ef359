from datetime import datetime
from sqlalchemy import Column, DateTime, Enum, Foreign<PERSON>ey, Integer, JSON, String, Text
from sqlalchemy.orm import relationship

from app.db.base_class import Base


class Pipeline(Base):
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, index=True, nullable=False)
    description = Column(Text, nullable=True)
    status = Column(Enum("draft", "in_progress", "completed", "failed", name="pipeline_status"), default="draft")
    config = Column(JSON, nullable=True)  # Pipeline configuration as JSON
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Foreign keys
    owner_id = Column(Integer, ForeignKey("user.id"))

    # Relationships
    owner = relationship("User", back_populates="pipelines")
    steps = relationship("PipelineStep", back_populates="pipeline", cascade="all, delete-orphan")
    reconciliation_results = relationship("ReconciliationResult", back_populates="pipeline")


class PipelineStep(Base):
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    step_type = Column(Enum(
        "data_ingestion",
        "data_cleaning",
        "reconciliation",
        "reporting",
        name="step_type"
    ), nullable=False)
    status = Column(Enum(
        "pending",
        "in_progress",
        "completed",
        "failed",
        name="step_status"
    ), default="pending")
    order = Column(Integer, nullable=False)  # Order of execution in the pipeline
    config = Column(JSON, nullable=True)  # Step-specific configuration
    result = Column(JSON, nullable=True)  # Step execution results
    error_message = Column(Text, nullable=True)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Foreign keys
    pipeline_id = Column(Integer, ForeignKey("pipeline.id"))
    data_source_id = Column(Integer, nullable=True)  # No foreign key for now

    # Relationships
    pipeline = relationship("Pipeline", back_populates="steps")
