import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '../components/ui/alert';
import { Badge } from '../components/ui/badge';
import { useToast } from '../components/ui/use-toast';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../components/ui/dialog';
import { 
  ArrowLeft, 
  Brain, 
  FileSpreadsheet, 
  Loader2, 
  AlertTriangle,
  CheckCircle2
} from 'lucide-react';
import MappingWorkspace from '../components/SemanticMapping/MappingWorkspace';
import fileService from '../services/excelService';

const SemanticMappingPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [dataSources, setDataSources] = useState<any[]>([]);
  const [selectedSources, setSelectedSources] = useState<any[]>([]);
  const [selectedSourceIds, setSelectedSourceIds] = useState<string[]>([]);
  const [showMappingWorkspace, setShowMappingWorkspace] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [combinedDatasetId, setCombinedDatasetId] = useState<string | null>(null);
  
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  
  // Get pre-selected sources from location state
  useEffect(() => {
    if (location.state?.sourceIds) {
      setSelectedSourceIds(location.state.sourceIds);
    }
  }, [location.state]);
  
  // Load data sources
  useEffect(() => {
    const loadDataSources = async () => {
      setIsLoading(true);
      try {
        const sources = await fileService.getDataSources();
        setDataSources(sources);
        
        // If we have pre-selected sources, find them in the loaded sources
        if (selectedSourceIds.length > 0) {
          const selected = sources.filter(source => selectedSourceIds.includes(source.id));
          setSelectedSources(selected);
        }
      } catch (error) {
        console.error('Error loading data sources:', error);
        toast({
          title: "Error loading data sources",
          description: "There was an error loading your data sources. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadDataSources();
  }, [selectedSourceIds, toast]);
  
  // Toggle source selection
  const toggleSourceSelection = (source: any) => {
    if (selectedSources.some(s => s.id === source.id)) {
      setSelectedSources(selectedSources.filter(s => s.id !== source.id));
    } else {
      setSelectedSources([...selectedSources, source]);
    }
  };
  
  // Start mapping process
  const startMapping = () => {
    if (selectedSources.length < 2) {
      toast({
        title: "Not enough sources selected",
        description: "Please select at least two data sources to combine.",
        variant: "destructive"
      });
      return;
    }
    
    setShowMappingWorkspace(true);
  };
  
  // Handle mapping completion
  const handleMappingComplete = async (mappingId: string) => {
    try {
      // Combine the datasets using the created mapping
      const result = await fileService.combineDatasets(
        selectedSources.map(s => s.id),
        `Combined Dataset (${selectedSources.length} sources)`,
        {}, // No specific relationships for now
        'smart', // Use smart strategy
        mappingId // Pass the mapping ID
      );
      
      setCombinedDatasetId(result.id);
      setShowSuccessDialog(true);
      
      toast({
        title: "Datasets combined successfully",
        description: `Created combined dataset with ${result.row_count} rows.`,
      });
    } catch (error) {
      console.error('Error combining datasets:', error);
      toast({
        title: "Error combining datasets",
        description: "There was an error combining the datasets. Please try again.",
        variant: "destructive"
      });
    }
  };
  
  // View combined dataset
  const viewCombinedDataset = () => {
    if (combinedDatasetId) {
      navigate(`/combined-analysis/${combinedDatasetId}`);
    }
  };
  
  // Return to data analyzer
  const returnToDataAnalyzer = () => {
    navigate('/data-analyzer');
  };
  
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">Semantic Mapping</h1>
        </div>
        
        {!showMappingWorkspace && (
          <Button onClick={startMapping} disabled={selectedSources.length < 2}>
            <Brain className="mr-2 h-5 w-5" />
            Start Mapping
          </Button>
        )}
      </div>
      
      {!showMappingWorkspace ? (
        <>
          <Card>
            <CardHeader>
              <CardTitle>Select Data Sources to Combine</CardTitle>
              <CardDescription>
                Choose at least two data sources to combine using semantic mapping
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                  <span className="ml-2 text-gray-500">Loading data sources...</span>
                </div>
              ) : dataSources.length === 0 ? (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertTitle>No data sources available</AlertTitle>
                  <AlertDescription>
                    Upload some data files first to combine them using semantic mapping.
                  </AlertDescription>
                </Alert>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {dataSources.map(source => (
                    <Card 
                      key={source.id} 
                      className={`cursor-pointer transition-all ${
                        selectedSources.some(s => s.id === source.id) 
                          ? 'border-primary bg-primary/5' 
                          : 'hover:border-gray-300'
                      }`}
                      onClick={() => toggleSourceSelection(source)}
                    >
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-center">
                            <FileSpreadsheet className="h-5 w-5 mr-2 text-blue-500" />
                            <div>
                              <h3 className="font-medium">{source.name}</h3>
                              <p className="text-sm text-gray-500">
                                {source.row_count} rows, {source.column_count} columns
                              </p>
                            </div>
                          </div>
                          
                          {selectedSources.some(s => s.id === source.id) && (
                            <CheckCircle2 className="h-5 w-5 text-primary" />
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
              
              {selectedSources.length > 0 && (
                <div className="mt-4 p-4 bg-gray-50 rounded-md">
                  <h3 className="font-medium mb-2">Selected Sources ({selectedSources.length})</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedSources.map(source => (
                      <Badge key={source.id} variant="secondary">
                        {source.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          <div className="flex justify-end">
            <Button 
              onClick={startMapping} 
              disabled={selectedSources.length < 2}
              size="lg"
            >
              <Brain className="mr-2 h-5 w-5" />
              Start Semantic Mapping
            </Button>
          </div>
        </>
      ) : (
        <MappingWorkspace 
          sourceIds={selectedSources.map(s => s.id)}
          sourceNames={selectedSources.map(s => s.name)}
          onComplete={handleMappingComplete}
          onCancel={() => setShowMappingWorkspace(false)}
        />
      )}
      
      {/* Success Dialog */}
      <Dialog open={showSuccessDialog} onOpenChange={setShowSuccessDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Datasets Combined Successfully</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <div className="flex items-center justify-center mb-4">
              <div className="rounded-full bg-green-100 p-3">
                <CheckCircle2 className="h-8 w-8 text-green-600" />
              </div>
            </div>
            
            <p className="text-center mb-4">
              Your datasets have been combined successfully using semantic mapping.
            </p>
            
            <div className="flex justify-center space-x-4">
              <Button variant="outline" onClick={returnToDataAnalyzer}>
                Return to Data Analyzer
              </Button>
              <Button onClick={viewCombinedDataset}>
                View Combined Dataset
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SemanticMappingPage;
