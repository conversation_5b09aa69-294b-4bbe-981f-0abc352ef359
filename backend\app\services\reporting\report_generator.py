import logging
from typing import Any, Dict, List, Optional

import pandas as pd

from app.models.reconciliation import ReconciliationResult

logger = logging.getLogger(__name__)


def generate_report(
    result: ReconciliationResult,
    report_type: str,
    config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Generate a report based on reconciliation results.
    
    Args:
        result: Reconciliation result
        report_type: Type of report to generate
        config: Report configuration
        
    Returns:
        Dictionary with report data
    """
    try:
        if report_type == "summary":
            return generate_summary_report(result)
        elif report_type == "detailed":
            return generate_detailed_report(result)
        elif report_type == "pivot":
            return generate_pivot_report(result, config)
        else:
            raise ValueError(f"Unsupported report type: {report_type}")
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        raise ValueError(f"Error generating report: {str(e)}")


def generate_summary_report(result: ReconciliationResult) -> Dict[str, Any]:
    """
    Generate a summary report.
    
    Args:
        result: Reconciliation result
        
    Returns:
        Dictionary with summary report data
    """
    # Extract basic statistics
    match_rate_percent = result.match_rate * 100
    
    # Create summary data
    summary = {
        "statistics": {
            "total_records": result.total_records,
            "matched_records": result.matched_records,
            "anomaly_records": result.anomaly_records,
            "unmatched_records": result.unmatched_records,
            "match_rate": f"{match_rate_percent:.2f}%"
        },
        "charts": {
            "match_status": {
                "labels": ["Matched", "Anomalies", "Unmatched"],
                "values": [result.matched_records, result.anomaly_records, result.unmatched_records]
            },
            "anomaly_types": {
                "labels": list(result.anomaly_details.keys()) if result.anomaly_details else [],
                "values": list(result.anomaly_details.values()) if result.anomaly_details else []
            }
        }
    }
    
    return summary


def generate_detailed_report(result: ReconciliationResult) -> Dict[str, Any]:
    """
    Generate a detailed report.
    
    Args:
        result: Reconciliation result
        
    Returns:
        Dictionary with detailed report data
    """
    # Extract result data
    result_data = result.result_data or {}
    matches = result_data.get("matches", [])
    anomalies = result_data.get("anomalies", [])
    unmatched = result_data.get("unmatched", [])
    
    # Create detailed report
    detailed_report = {
        "statistics": {
            "total_records": result.total_records,
            "matched_records": result.matched_records,
            "anomaly_records": result.anomaly_records,
            "unmatched_records": result.unmatched_records,
            "match_rate": f"{result.match_rate * 100:.2f}%"
        },
        "matches": matches[:100],  # Limit to 100 records for performance
        "anomalies": anomalies[:100],  # Limit to 100 records for performance
        "unmatched": unmatched[:100],  # Limit to 100 records for performance
        "anomaly_details": result.anomaly_details or {}
    }
    
    return detailed_report


def generate_pivot_report(
    result: ReconciliationResult,
    config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Generate a pivot report.
    
    Args:
        result: Reconciliation result
        config: Report configuration
        
    Returns:
        Dictionary with pivot report data
    """
    if not config:
        raise ValueError("Configuration is required for pivot reports")
    
    # Extract configuration
    row_fields = config.get("row_fields", [])
    column_fields = config.get("column_fields", [])
    value_fields = config.get("value_fields", [])
    aggregation = config.get("aggregation", "sum")
    
    if not row_fields or not value_fields:
        raise ValueError("Row fields and value fields are required for pivot reports")
    
    # Extract result data
    result_data = result.result_data or {}
    matches = result_data.get("matches", [])
    
    # Create a DataFrame from matches
    records = []
    for match in matches:
        record = {}
        
        # Extract fields from source record
        source_record = match.get("source_record", {})
        for field in source_record:
            record[f"source_{field}"] = source_record[field]
        
        # Extract fields from target record
        target_record = match.get("target_record", {})
        for field in target_record:
            record[f"target_{field}"] = target_record[field]
        
        # Add confidence
        record["confidence"] = match.get("confidence", 0)
        
        records.append(record)
    
    if not records:
        return {
            "pivot_data": [],
            "row_fields": row_fields,
            "column_fields": column_fields,
            "value_fields": value_fields,
            "aggregation": aggregation
        }
    
    # Create DataFrame
    df = pd.DataFrame(records)
    
    # Create pivot table
    try:
        # Adjust field names to match DataFrame columns
        adjusted_row_fields = [f"source_{field}" if f"source_{field}" in df.columns else field for field in row_fields]
        adjusted_column_fields = [f"source_{field}" if f"source_{field}" in df.columns else field for field in column_fields]
        adjusted_value_fields = [f"source_{field}" if f"source_{field}" in df.columns else field for field in value_fields]
        
        # Create pivot table
        if adjusted_column_fields:
            pivot = pd.pivot_table(
                df,
                values=adjusted_value_fields,
                index=adjusted_row_fields,
                columns=adjusted_column_fields,
                aggfunc=aggregation
            )
        else:
            pivot = pd.pivot_table(
                df,
                values=adjusted_value_fields,
                index=adjusted_row_fields,
                aggfunc=aggregation
            )
        
        # Convert to dictionary
        pivot_dict = pivot.reset_index().to_dict(orient="records")
        
        return {
            "pivot_data": pivot_dict,
            "row_fields": row_fields,
            "column_fields": column_fields,
            "value_fields": value_fields,
            "aggregation": aggregation
        }
    except Exception as e:
        logger.error(f"Error creating pivot table: {e}")
        raise ValueError(f"Error creating pivot table: {str(e)}")
