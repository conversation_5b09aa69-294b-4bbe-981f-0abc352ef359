
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ThemeProvider } from "@/components/ThemeProvider";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import NotFound from "@/pages/NotFound";

// Import simplified components
import Home from "@/pages/Home";
import DataAnalyzer from "@/pages/DataAnalyzer";
import ExcelAnalysis from "@/pages/ExcelAnalysis";
import CombinedAnalysis from "@/pages/CombinedAnalysis";
import SemanticMappingPage from "@/pages/SemanticMappingPage";
import TransactionCategorizationPage from "@/pages/TransactionCategorizationPage";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="light">
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
        <div className="flex flex-col min-h-screen">
          <Navbar />
          <main className="flex-grow pt-20">
            <Routes>
              {/* Make Home the main entry point to showcase Banker Babel branding */}
              <Route path="/" element={<Home />} />
              <Route path="/analyzer" element={<DataAnalyzer />} />
              <Route path="/excel-analysis/:id" element={<ExcelAnalysis />} />
              <Route path="/combined-analysis/:id" element={<CombinedAnalysis />} />
              <Route path="/semantic-mapping" element={<SemanticMappingPage />} />
              <Route path="/transaction-categorization/:sourceId" element={<TransactionCategorizationPage />} />

              {/* Redirect old routes to new simplified routes */}
              <Route path="/excel-ingestion" element={<Navigate to="/" replace />} />
              <Route path="/multi-file-ingestion" element={<Navigate to="/" replace />} />
              <Route path="/data-ingestion" element={<Navigate to="/" replace />} />
              <Route path="/data-pipeline" element={<Navigate to="/" replace />} />
              <Route path="/data-cleaning" element={<Navigate to="/" replace />} />
              <Route path="/financial-analysis/:id" element={<Navigate to="/excel-analysis/:id" replace />} />

              <Route path="*" element={<NotFound />} />
            </Routes>
          </main>
          <Footer />
        </div>
      </BrowserRouter>
      </TooltipProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
