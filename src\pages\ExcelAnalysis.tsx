import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useToast } from '../hooks/use-toast';
import { Card, CardContent } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../components/ui/tabs';
import {
  ArrowLeft,
  LineChart,
  Tag
} from 'lucide-react';
import fileService, { DataSource } from '../services/excelService';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import ProfitLossReport from '../components/Reports/ProfitLossReport';

const ExcelAnalysisSimple = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  const [dataSource, setDataSource] = useState<DataSource | null>(null);
  const [sourceData, setSourceData] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('data');

  // Function to fetch data
  const fetchData = async () => {
    if (!id) {
      console.error('No data source ID provided');
      toast({
        variant: "destructive",
        title: "Error loading data",
        description: "No data source ID was provided.",
      });
      navigate('/');
      return;
    }

    try {
      // Fetch data source info
      const source = await fileService.getDataSource(id);
      setDataSource(source);

      // Fetch source data
      const { data } = await fileService.getDataSourceData(id, 100);
      setSourceData(data);

    } catch (error) {
      console.error('Error fetching data:', error);
      toast({
        variant: "destructive",
        title: "Error loading data",
        description: "There was a problem loading the data. Please try again.",
      });
      navigate('/');
    }
  };

  useEffect(() => {
    fetchData();
  }, [id, toast, navigate]);

  // Refresh data when window gains focus (user returns from categorization)
  useEffect(() => {
    const handleFocus = () => {
      fetchData();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, []);



  // Helper function to render table headers from data with limited fields
  const renderTableHeaders = (data: any[]) => {
    if (!data || data.length === 0) return null;

    // Get all headers
    const allHeaders = Object.keys(data[0]);

    // Limit to important fields first, then add others up to a maximum
    const MAX_FIELDS = 8; // Maximum number of fields to display

    // Priority fields to always show if present
    const priorityFields = [
      'date', 'transaction_date', 'posting_date', 'time',
      'amount', 'value', 'balance',
      'description', 'memo', 'narrative', 'details',
      'category', 'type', 'classification',
      'account', 'payee', 'merchant'
    ];

    // Start with priority fields that exist in the data
    const displayHeaders = priorityFields.filter(field => allHeaders.includes(field));

    // Add remaining fields up to the maximum
    const remainingFields = allHeaders.filter(field => !displayHeaders.includes(field));
    const additionalFields = remainingFields.slice(0, MAX_FIELDS - displayHeaders.length);
    displayHeaders.push(...additionalFields);

    const selectedHeadersCount = displayHeaders.length;
    const totalHeadersCount = allHeaders.length;

    return (
      <>
        <TableHeader>
          <TableRow>
            {displayHeaders.map((header) => (
              <TableHead key={header}>
                {header === 'Expense Category' ? 'Category' : header}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        {selectedHeadersCount < totalHeadersCount && (
          <caption className="mt-2 text-sm text-gray-500 text-right">
            Showing {selectedHeadersCount} of {totalHeadersCount} fields
          </caption>
        )}
      </>
    );
  };

  // Helper function to render table rows from data with limited fields and rows
  const renderTableRows = (data: any[]) => {
    if (!data || data.length === 0) return null;

    // Get all headers
    const allHeaders = Object.keys(data[0]);

    // Use the same logic as renderTableHeaders to get consistent fields
    const MAX_FIELDS = 8;
    const MAX_ROWS = 100; // Maximum number of rows to display

    const priorityFields = [
      'date', 'transaction_date', 'posting_date', 'time',
      'amount', 'value', 'balance',
      'description', 'memo', 'narrative', 'details',
      'category', 'type', 'classification',
      'account', 'payee', 'merchant'
    ];

    const displayHeaders = priorityFields.filter(field => allHeaders.includes(field));
    const remainingFields = allHeaders.filter(field => !displayHeaders.includes(field));
    const additionalFields = remainingFields.slice(0, MAX_FIELDS - displayHeaders.length);
    displayHeaders.push(...additionalFields);

    // Limit the number of rows displayed
    const displayData = data.slice(0, MAX_ROWS);

    return (
      <TableBody>
        {displayData.map((row, index) => (
          <TableRow key={index}>
            {displayHeaders.map((header) => (
              <TableCell key={header}>
                {row[header] !== null && row[header] !== undefined ? String(row[header]) : ''}
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    );
  };

  if (!dataSource) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Loading...</h2>
          <p className="text-gray-500">Please wait while we load your data.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/')}
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Dashboard
          </Button>
          <div>
            <h1 className="text-3xl font-bold">{dataSource.name}</h1>
            <p className="text-gray-500">
              Uploaded on {new Date(dataSource.created_at).toLocaleDateString()}
            </p>
          </div>
        </div>

        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/financial-analysis/${id}`)}
          >
            <LineChart size={16} className="mr-2" />
            Financial Analysis
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="data">Data</TabsTrigger>
          <TabsTrigger value="report">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="data" className="mt-6">
          <div className="space-y-6">
            <Card>
              <CardContent className="pt-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-semibold">Transaction Data</h2>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => navigate(`/transaction-categorization/${id}`)}
                    >
                      <Tag size={16} className="mr-2" />
                      Categorize Transactions
                    </Button>
                  </div>
                </div>

                <div className="border rounded-md overflow-auto max-h-[500px]">
                  <Table>
                    {renderTableHeaders(sourceData)}
                    {renderTableRows(sourceData)}
                  </Table>
                </div>

                <div className="flex justify-between items-center mt-4">
                  <p className="text-sm text-gray-500">
                    Showing data from {dataSource.stats.cleaned_rows} total rows
                  </p>

                  {sourceData.length < dataSource.stats.cleaned_rows && (
                    <p className="text-sm text-gray-500">
                      Displaying first {sourceData.length} rows
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="report" className="mt-6">
          <ProfitLossReport sourceId={id!} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ExcelAnalysisSimple;
