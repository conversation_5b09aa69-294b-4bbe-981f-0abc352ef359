"""
Excel Reports API endpoints for generating downloadable financial reports.
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, date
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from app.models.user import User
from app.api.dependencies import get_current_user
from app.services.excel_export_service import ExcelExportService
import json

logger = logging.getLogger(__name__)

router = APIRouter()

class ExcelReportRequest(BaseModel):
    """Request model for Excel report generation."""
    report_name: Optional[str] = "Financial Report"
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    include_uncategorized: bool = True

@router.post("/{source_id}/excel-report")
async def generate_excel_report(
    source_id: str,
    request: ExcelReportRequest,
    _: User = Depends(get_current_user),  # Keep for auth but rename to avoid warning
):
    """
    Generate a comprehensive Excel report with P&L, reconciliation, and transaction data.
    
    Args:
        source_id: ID of the data source
        request: Report generation parameters
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        StreamingResponse: Excel file download
    """
    try:
        logger.info(f"Generating Excel report for source ID {source_id}")
        
        # Get transaction data - try multiple sources
        transactions = await _get_transaction_data(source_id)
        
        if not transactions:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No transaction data found for source ID {source_id}. Please ensure transactions are categorized first."
            )
        
        logger.info(f"Found {len(transactions)} transactions for Excel report")
        
        # Parse dates if provided
        start_date = None
        end_date = None
        if request.start_date:
            try:
                start_date = datetime.strptime(request.start_date, '%Y-%m-%d').date()
            except ValueError:
                logger.warning(f"Invalid start_date format: {request.start_date}")
        
        if request.end_date:
            try:
                end_date = datetime.strptime(request.end_date, '%Y-%m-%d').date()
            except ValueError:
                logger.warning(f"Invalid end_date format: {request.end_date}")
        
        # Filter transactions by date if specified
        if start_date or end_date:
            transactions = _filter_transactions_by_date(transactions, start_date, end_date)
            logger.info(f"Filtered to {len(transactions)} transactions within date range")
        
        # Create Excel report
        excel_service = ExcelExportService()
        excel_buffer = excel_service.create_financial_report(
            transactions=transactions,
            report_name=request.report_name,
            start_date=start_date,
            end_date=end_date
        )
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{request.report_name.replace(' ', '_')}_{source_id}_{timestamp}.xlsx"
        
        logger.info(f"Excel report generated successfully: {filename}")
        
        # Return as streaming response
        return StreamingResponse(
            iter([excel_buffer.getvalue()]),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating Excel report for source {source_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating Excel report: {str(e)}"
        )

async def _get_transaction_data(source_id: str) -> List[Dict[str, Any]]:
    """
    Get transaction data from multiple possible sources.

    Args:
        source_id: ID of the data source

    Returns:
        List of transaction dictionaries
    """
    transactions = []

    # First, try to get the raw transaction data from the simple data endpoint
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            # Get raw transaction data
            response = await client.get(f"http://localhost:8002/api/v1/simple/simple-data/{source_id}?limit=1000")
            if response.status_code == 200:
                data = response.json()
                raw_transactions = data.get('data', [])
                logger.info(f"Loaded {len(raw_transactions)} raw transactions from simple data API")

                # Now try to get categorization results
                try:
                    cat_response = await client.get(f"http://localhost:8002/api/v1/transaction-categorization/clusters/{source_id}")
                    if cat_response.status_code == 200:
                        clusters_data = cat_response.json()
                        clusters = clusters_data.get('clusters', [])

                        if clusters:
                            # Convert clusters to transaction format
                            for cluster in clusters:
                                transactions.append({
                                    'date': '',  # Clusters don't have individual dates
                                    'description': f"{cluster.get('label', 'Unknown')} ({cluster.get('row_count', 0)} transactions)",
                                    'amount': float(cluster.get('amount_total', 0)),
                                    'category': cluster.get('label', 'Uncategorized'),
                                    'count': cluster.get('row_count', 0)
                                })
                            logger.info(f"Loaded {len(transactions)} categorized clusters")
                            return transactions
                except Exception as e:
                    logger.warning(f"Could not load categorization clusters: {e}")

                # If no clusters, use raw data with simple categorization
                transactions = _convert_raw_data_to_transactions(raw_transactions)
                logger.info(f"Using raw data with {len(transactions)} transactions")
                return transactions

    except Exception as e:
        logger.warning(f"Could not load from simple data API: {e}")

    # Fallback: Try to get data from excel_api
    try:
        from excel_api import DATA_SOURCES

        # Find the data source
        data_source = None
        for source in DATA_SOURCES:
            if source["id"] == source_id:
                data_source = source
                break

        if data_source and "json_path" in data_source:
            # Load the raw transaction data
            with open(data_source["json_path"], "r") as f:
                raw_data = json.load(f)

            transactions = _convert_raw_data_to_transactions(raw_data)
            logger.info(f"Loaded {len(transactions)} transactions from excel_api fallback")

    except Exception as e:
        logger.warning(f"Could not load from excel_api fallback: {e}")

    return transactions



def _convert_raw_data_to_transactions(raw_data: List[Dict]) -> List[Dict[str, Any]]:
    """Convert raw data to transaction format with basic categorization."""
    transactions = []
    
    for transaction in raw_data:
        # Basic categorization based on amount and description
        amount = float(transaction.get('Amount', 0))
        description = str(transaction.get('Booking details', '')).lower()
        
        # Simple categorization logic
        if amount > 0:
            category = 'Income - Other'
        elif 'zoom' in description:
            category = 'Software Expense'
        elif 'bank' in description or 'fee' in description:
            category = 'Bank Fees'
        else:
            category = 'Uncategorized'
        
        processed_transaction = {
            'date': transaction.get('Booking Date', ''),
            'description': transaction.get('Booking details', ''),
            'amount': amount,
            'category': category,
            'original_data': transaction
        }
        transactions.append(processed_transaction)
    
    return transactions

def _filter_transactions_by_date(
    transactions: List[Dict[str, Any]], 
    start_date: Optional[date], 
    end_date: Optional[date]
) -> List[Dict[str, Any]]:
    """Filter transactions by date range."""
    if not start_date and not end_date:
        return transactions
    
    filtered = []
    for transaction in transactions:
        transaction_date_str = transaction.get('date', '')
        if not transaction_date_str:
            continue
        
        try:
            # Try multiple date formats
            transaction_date = None
            for date_format in ['%Y-%m-%d', '%d.%m.%Y', '%m/%d/%Y']:
                try:
                    transaction_date = datetime.strptime(transaction_date_str, date_format).date()
                    break
                except ValueError:
                    continue
            
            if not transaction_date:
                continue
            
            # Check if within range
            if start_date and transaction_date < start_date:
                continue
            if end_date and transaction_date > end_date:
                continue
            
            filtered.append(transaction)
            
        except Exception as e:
            logger.debug(f"Could not parse date {transaction_date_str}: {e}")
            continue
    
    return filtered
