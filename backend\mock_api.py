from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import random
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
import json
import time

app = FastAPI(title="Mock Data Oracle API")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mock data
class JobStatus(BaseModel):
    job_id: str
    status: str
    progress: int
    created_at: str
    updated_at: str
    result_id: Optional[str] = None

class DataSourceResponse(BaseModel):
    id: int
    name: str
    source_type: str
    record_count: int
    created_at: str
    status: str

class AnomalyDetectionResult(BaseModel):
    id: str
    total_rows: int
    anomaly_indices: List[int]
    anomalies_by_method: Dict[str, int]
    anomalies_by_column: Dict[str, int]
    anomaly_records: List[Dict[str, Any]]
    anomaly_percentage: float

# Mock endpoints
@app.get("/")
async def root():
    return {"message": "Welcome to Mock Data Oracle API", "version": "1.0.0"}

# Handle both /api/v1/... and /api/... paths
@app.get("/api/v1/anomaly-detection/jobs")
@app.get("/api/anomaly-detection/jobs")
async def get_active_jobs(pipeline_id: int, status: str = "active"):
    # Return empty list for now
    return []

@app.post("/api/v1/anomaly-detection/start")
@app.post("/api/anomaly-detection/start")
async def start_anomaly_detection(request: Dict[str, Any]):
    job_id = f"job_{random.randint(1000, 9999)}"
    return {
        "job_id": job_id,
        "status": "pending",
        "progress": 0,
        "created_at": "2023-10-15T10:00:00Z",
        "updated_at": "2023-10-15T10:00:00Z"
    }

@app.get("/api/v1/anomaly-detection/jobs/{job_id}")
@app.get("/api/anomaly-detection/jobs/{job_id}")
async def get_job_status(job_id: str):
    # Simulate a job that's in progress
    return {
        "job_id": job_id,
        "status": "processing",
        "progress": random.randint(10, 90),
        "created_at": "2023-10-15T10:00:00Z",
        "updated_at": "2023-10-15T10:05:00Z"
    }

@app.get("/api/v1/anomaly-detection/results/{result_id}")
@app.get("/api/anomaly-detection/results/{result_id}")
async def get_anomaly_results(result_id: str):
    # Return mock anomaly detection results
    return {
        "id": result_id,
        "total_rows": 1000,
        "anomaly_indices": [12, 45, 67, 89, 120, 145, 190, 210, 250, 300],
        "anomalies_by_method": {
            "statistical": 5,
            "isolation_forest": 3,
            "domain_rules": 2
        },
        "anomalies_by_column": {
            "amount": 4,
            "date": 2,
            "description": 4
        },
        "anomaly_records": [
            {"id": 12, "amount": 9999.99, "date": "2023-01-15", "description": "Unusual transaction"},
            {"id": 45, "amount": 0.01, "date": "2023-02-20", "description": "Suspicious activity"},
            {"id": 67, "amount": 5000.00, "date": "2023-03-05", "description": "Potential duplicate"}
        ],
        "anomaly_percentage": 1.0
    }

@app.post("/api/v1/data-cleaning/standardize")
@app.post("/api/data-cleaning/standardize")
async def standardize_data(request: Dict[str, Any]):
    # Return mock standardization results
    time.sleep(1)  # Simulate processing time
    return {
        "originalRows": 1000,
        "standardizedRows": 950,
        "changes": {
            "dates": 120,
            "amounts": 85,
            "names": 45,
            "addresses": 30
        },
        "sampleData": [
            {"before": {"date": "01/15/2023"}, "after": {"date": "2023-01-15"}},
            {"before": {"amount": "1,234.56"}, "after": {"amount": 1234.56}},
            {"before": {"name": "john smith"}, "after": {"name": "John Smith"}}
        ]
    }

@app.post("/api/v1/data-cleaning/classify")
@app.post("/api/data-cleaning/classify")
async def classify_data(request: Dict[str, Any]):
    # Return mock classification results
    time.sleep(1)  # Simulate processing time
    return {
        "totalRows": 1000,
        "processedRows": 980,
        "categories": {
            "invoice": 450,
            "payment": 380,
            "credit_note": 120,
            "other": 30
        },
        "confidence": {
            "high": 800,
            "medium": 150,
            "low": 30
        },
        "sampleData": [
            {"id": 1, "text": "Invoice #12345", "category": "invoice", "confidence": 0.95},
            {"id": 2, "text": "Payment received", "category": "payment", "confidence": 0.92},
            {"id": 3, "text": "Credit note issued", "category": "credit_note", "confidence": 0.88}
        ]
    }

@app.post("/api/v1/data-cleaning/quality")
@app.post("/api/data-cleaning/quality")
async def assess_quality(request: Dict[str, Any]):
    # Return mock quality assessment results
    time.sleep(1)  # Simulate processing time
    return {
        "overallScore": 85,
        "metrics": {
            "completeness": 90,
            "accuracy": 85,
            "consistency": 80,
            "timeliness": 95
        },
        "issuesByColumn": {
            "amount": {
                "missing": 5,
                "invalid": 10,
                "outliers": 8
            },
            "date": {
                "missing": 2,
                "invalid": 5,
                "outliers": 3
            },
            "description": {
                "missing": 15,
                "invalid": 0,
                "outliers": 0
            }
        },
        "recommendations": [
            "Fix missing values in description field",
            "Standardize date formats",
            "Review outlier amounts"
        ]
    }

@app.post("/api/v1/reconciliation/match")
@app.post("/api/reconciliation/match")
async def match_data(request: Dict[str, Any]):
    # Return mock matching results
    time.sleep(2)  # Simulate processing time
    return {
        "matches": [
            {
                "source_record": {"id": 1, "amount": 100.00, "date": "2023-01-15"},
                "target_record": {"id": 101, "amount": 100.00, "date": "2023-01-15"},
                "source_index": 0,
                "target_index": 0,
                "confidence": 0.95,
                "matched_fields": {"amount": "exact", "date": "exact"},
                "match_method": "exact"
            },
            {
                "source_record": {"id": 2, "amount": 200.00, "date": "2023-01-16"},
                "target_record": {"id": 102, "amount": 200.00, "date": "2023-01-16"},
                "source_index": 1,
                "target_index": 1,
                "confidence": 0.90,
                "matched_fields": {"amount": "exact", "date": "exact"},
                "match_method": "exact"
            }
        ],
        "anomalies": [
            {
                "source_record": {"id": 3, "amount": 300.00, "date": "2023-01-17"},
                "target_record": {"id": 103, "amount": 350.00, "date": "2023-01-17"},
                "source_index": 2,
                "target_index": 2,
                "confidence": 0.75,
                "matched_fields": {"date": "exact"},
                "match_method": "partial",
                "anomaly_type": "amount_mismatch"
            }
        ],
        "stats": {
            "total_matches": 2,
            "total_anomalies": 1,
            "methods": {
                "exact": {
                    "matches": 2,
                    "anomalies": 0
                },
                "partial": {
                    "matches": 0,
                    "anomalies": 1
                }
            }
        }
    }

@app.get("/api/v1/reconciliation/methods")
@app.get("/api/reconciliation/methods")
async def get_matching_methods():
    return [
        {
            "id": "tfidf",
            "name": "TF-IDF Matching",
            "description": "Uses term frequency-inverse document frequency to match text fields"
        },
        {
            "id": "embeddings",
            "name": "Embeddings Matching",
            "description": "Uses semantic embeddings to match fields based on meaning"
        },
        {
            "id": "hybrid",
            "name": "Hybrid Matching",
            "description": "Combines TF-IDF, embeddings, and fuzzy matching for best results"
        },
        {
            "id": "weighted_field",
            "name": "Weighted Field Matching",
            "description": "Matches individual fields with configurable weights"
        },
        {
            "id": "phonetic_name",
            "name": "Phonetic Name Matching",
            "description": "Specialized matching for names using phonetic algorithms"
        }
    ]

@app.get("/api/v1/data-sources")
@app.get("/api/data-sources")
async def get_data_sources():
    return [
        {
            "id": "1",
            "name": "Invoice Data",
            "fields": ["invoice_id", "amount", "date", "customer", "description"]
        },
        {
            "id": "2",
            "name": "Payment Data",
            "fields": ["payment_id", "amount", "date", "customer", "method"]
        },
        {
            "id": "3",
            "name": "Customer Data",
            "fields": ["customer_id", "name", "email", "address", "phone"]
        }
    ]

if __name__ == "__main__":
    uvicorn.run("mock_api:app", host="0.0.0.0", port=8000, reload=True)
