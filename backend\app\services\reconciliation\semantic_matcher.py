import logging
from typing import Any, Dict, List, Optional, Tuple

from app.utils.llm_client import llm_client

logger = logging.getLogger(__name__)


def semantic_match(
    data_a: List[Dict[str, Any]],
    data_b: List[Dict[str, Any]],
    source_fields: List[str],
    target_fields: List[str],
    threshold: float = 0.8
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Perform semantic matching between two datasets using LLM.
    
    Args:
        data_a: First dataset
        data_b: Second dataset
        source_fields: Fields to match from the first dataset
        target_fields: Fields to match from the second dataset
        threshold: Confidence threshold for matches
        
    Returns:
        Tuple containing:
        - List of matches
        - List of anomalies
    """
    matches = []
    anomalies = []
    
    # For each record in the first dataset
    for i, record_a in enumerate(data_a):
        # Extract values from source record
        source_values = {}
        for field in source_fields:
            if field in record_a:
                source_values[field] = record_a[field]
        
        best_match = None
        best_score = 0
        best_j = -1
        
        # Compare with each record in the second dataset
        for j, record_b in enumerate(data_b):
            # Extract values from target record
            target_values = {}
            for field in target_fields:
                if field in record_b:
                    target_values[field] = record_b[field]
            
            # Skip if no values to compare
            if not source_values or not target_values:
                continue
            
            # Use LLM to calculate semantic similarity
            try:
                # Prepare text for comparison
                source_text = ", ".join([f"{k}: {v}" for k, v in source_values.items()])
                target_text = ", ".join([f"{k}: {v}" for k, v in target_values.items()])
                
                # Calculate similarity using LLM
                similarity = calculate_semantic_similarity(source_text, target_text)
                
                if similarity > best_score:
                    best_score = similarity
                    best_j = j
                    best_match = record_b
            except Exception as e:
                logger.error(f"Error calculating semantic similarity: {e}")
                continue
        
        # Check if we found a good match
        if best_match and best_score >= threshold:
            matches.append({
                "source_record": record_a,
                "target_record": best_match,
                "source_index": i,
                "target_index": best_j,
                "confidence": best_score,
                "matched_fields": dict(zip(source_fields, target_fields))
            })
        elif best_match and best_score >= 0.5:  # Partial match
            anomalies.append({
                "source_record": record_a,
                "target_record": best_match,
                "source_index": i,
                "target_index": best_j,
                "confidence": best_score,
                "matched_fields": dict(zip(source_fields, target_fields)),
                "anomaly_type": "semantic_match_below_threshold"
            })
    
    return matches, anomalies


def calculate_semantic_similarity(text1: str, text2: str) -> float:
    """
    Calculate semantic similarity between two texts using LLM.
    
    Args:
        text1: First text
        text2: Second text
        
    Returns:
        Similarity score between 0 and 1
    """
    try:
        # Use LLM to classify similarity
        prompt = f"""
        Compare the following two records and determine if they refer to the same entity.
        
        Record 1: {text1}
        Record 2: {text2}
        
        Rate the similarity on a scale from 0 to 1, where:
        - 0 means completely different
        - 1 means exactly the same entity
        
        Return only the numeric score.
        """
        
        # Get response from LLM
        response = llm_client.llm.invoke(prompt).strip()
        
        # Extract numeric score
        try:
            score = float(response)
            return min(max(score, 0), 1)  # Ensure score is between 0 and 1
        except ValueError:
            logger.error(f"Invalid LLM response for similarity calculation: {response}")
            return 0.0
    except Exception as e:
        logger.error(f"Error calculating semantic similarity: {e}")
        return 0.0
