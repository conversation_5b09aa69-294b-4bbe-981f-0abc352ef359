"""
Smart header detection module for Excel and CSV files.
This module provides functions to detect the most likely header row in a file.
"""

import pandas as pd
import numpy as np
import logging
from typing import Tuple, List, Dict, Any, Optional

# Set up logging
logger = logging.getLogger(__name__)

# Common header keywords that might appear in column names
HEADER_KEYWORDS = [
    'date', 'time', 'day', 'month', 'year', 'quarter',
    'amount', 'value', 'price', 'cost', 'fee', 'payment', 'balance', 'total', 'sum',
    'description', 'memo', 'narrative', 'details', 'notes', 'comment',
    'category', 'type', 'class', 'classification', 'group',
    'account', 'acct', 'acc', 'payee', 'vendor', 'merchant', 'customer', 'client',
    'id', 'number', 'no', 'ref', 'reference', 'code',
    'name', 'title', 'label', 'item',
    'quantity', 'qty', 'count', 'units',
    'address', 'city', 'state', 'zip', 'postal',
    'phone', 'email', 'contact',
    'status', 'condition', 'flag',
    'start', 'end', 'begin', 'finish',
    'income', 'revenue', 'expense', 'expenditure', 'cost',
    'debit', 'credit', 'transaction', 'transfer'
]

def score_header_row(row: pd.Series) -> float:
    """
    Score a row on how likely it is to be a header row.
    
    Args:
        row: A pandas Series representing a row from a DataFrame
        
    Returns:
        float: A score indicating how likely the row is to be a header row
    """
    score = 0.0
    
    # Skip completely empty rows
    if row.isna().all():
        return -100.0
    
    # 1. Non-empty cells percentage (headers should have most cells filled)
    non_empty_percentage = row.notna().mean()
    score += non_empty_percentage * 15  # Max 15 points
    
    # 2. String percentage (headers are usually strings)
    string_cells = 0
    total_non_empty = 0
    
    for cell in row:
        if pd.notna(cell):
            total_non_empty += 1
            if isinstance(cell, str):
                string_cells += 1
    
    string_percentage = string_cells / total_non_empty if total_non_empty > 0 else 0
    score += string_percentage * 20  # Max 20 points
    
    # 3. Check for common header keywords
    keyword_matches = 0
    for cell in row:
        if isinstance(cell, str):
            cell_lower = cell.lower()
            if any(keyword in cell_lower for keyword in HEADER_KEYWORDS):
                keyword_matches += 1
    
    keyword_score = (keyword_matches / len(row)) * 30  # Max 30 points
    score += keyword_score
    
    # 4. Check for numeric data in the row (headers usually aren't numeric)
    numeric_cells = 0
    for cell in row:
        if pd.notna(cell) and isinstance(cell, (int, float)):
            numeric_cells += 1
    
    numeric_percentage = numeric_cells / total_non_empty if total_non_empty > 0 else 0
    score -= numeric_percentage * 25  # Penalty for numeric values
    
    # 5. Check for short string lengths (headers are usually short)
    if string_cells > 0:
        string_lengths = []
        for cell in row:
            if isinstance(cell, str):
                string_lengths.append(len(cell))
        
        avg_string_length = sum(string_lengths) / len(string_lengths) if string_lengths else 0
        
        if avg_string_length < 2:  # Too short, might be single letters or symbols
            score -= 10
        elif avg_string_length < 20:  # Good header length
            score += 15
        elif avg_string_length > 50:  # Too long for headers
            score -= 15
    
    # 6. Check for consistent formatting (headers often have consistent formatting)
    if string_cells > 1:
        # Check if all strings have similar case formatting
        all_upper = all(isinstance(cell, str) and cell.isupper() for cell in row if isinstance(cell, str))
        all_lower = all(isinstance(cell, str) and cell.islower() for cell in row if isinstance(cell, str))
        all_title = all(isinstance(cell, str) and cell.istitle() for cell in row if isinstance(cell, str))
        
        if all_upper or all_lower or all_title:
            score += 10  # Bonus for consistent case formatting
    
    # 7. Check for special characters that might indicate data rather than headers
    special_chars = ['$', '%', '€', '£', '¥', '#']
    special_char_count = 0
    
    for cell in row:
        if isinstance(cell, str):
            if any(char in cell for char in special_chars):
                special_char_count += 1
    
    special_char_percentage = special_char_count / string_cells if string_cells > 0 else 0
    score -= special_char_percentage * 15  # Penalty for special characters
    
    # 8. Check for date-like values (headers usually don't contain dates)
    date_like_count = 0
    for cell in row:
        if isinstance(cell, str):
            # Simple check for date-like patterns
            if any(pattern in cell for pattern in ['/', '-']) and any(char.isdigit() for char in cell):
                date_like_count += 1
    
    date_like_percentage = date_like_count / total_non_empty if total_non_empty > 0 else 0
    score -= date_like_percentage * 20  # Penalty for date-like values
    
    logger.debug(f"Row score: {score:.2f} (non-empty: {non_empty_percentage:.2f}, string: {string_percentage:.2f}, "
                f"keywords: {keyword_score:.2f}, numeric: {numeric_percentage:.2f})")
    
    return score

def detect_header_row(df: pd.DataFrame, max_rows_to_check: int = 10) -> Tuple[int, float]:
    """
    Detect the most likely header row in a DataFrame.
    
    Args:
        df: A pandas DataFrame
        max_rows_to_check: Maximum number of rows to check from the top of the file
        
    Returns:
        Tuple[int, float]: The index of the most likely header row and its score
    """
    # Limit the number of rows to check
    rows_to_check = min(max_rows_to_check, len(df))
    
    # Score each row
    header_scores = []
    for i in range(rows_to_check):
        score = score_header_row(df.iloc[i])
        header_scores.append((i, score))
        logger.debug(f"Row {i} score: {score:.2f}")
    
    # Sort by score in descending order
    header_scores.sort(key=lambda x: x[1], reverse=True)
    
    # Get the best header row
    best_header_row = header_scores[0][0]
    best_score = header_scores[0][1]
    
    logger.info(f"Best header row detected at index {best_header_row} with score {best_score:.2f}")
    
    return best_header_row, best_score

def read_file_with_smart_header(
    file_path: str, 
    file_type: str, 
    sheet_name: Optional[str] = None,
    header_detection_threshold: float = 20.0
) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Read a file with smart header detection.
    
    Args:
        file_path: Path to the file
        file_type: Type of file ('csv', 'xlsx', 'xls')
        sheet_name: Sheet name for Excel files
        header_detection_threshold: Minimum score for a row to be considered a header
        
    Returns:
        Tuple[pd.DataFrame, Dict[str, Any]]: The DataFrame with proper headers and metadata about the detection
    """
    # Read the file without assuming headers
    if file_type.lower() in ['xlsx', 'xls']:
        # For Excel files
        sample_df = pd.read_excel(file_path, sheet_name=sheet_name, header=None, nrows=15)
    elif file_type.lower() == 'csv':
        # For CSV files
        sample_df = pd.read_csv(file_path, header=None, nrows=15)
    else:
        raise ValueError(f"Unsupported file type: {file_type}")
    
    # Detect the header row
    header_row, header_score = detect_header_row(sample_df)
    
    # Metadata about the detection
    header_metadata = {
        "detected_header_row": header_row,
        "header_score": header_score,
        "header_detection_method": "smart",
        "original_columns": sample_df.iloc[header_row].tolist() if header_score >= header_detection_threshold else None
    }
    
    # Read the file again with the detected header
    if header_score >= header_detection_threshold:
        logger.info(f"Using detected header row at index {header_row}")
        if file_type.lower() in ['xlsx', 'xls']:
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row)
        elif file_type.lower() == 'csv':
            df = pd.read_csv(file_path, header=header_row)
    else:
        # If no good header row was found, use default behavior but generate column names
        logger.info("No good header row detected, using default column names")
        if file_type.lower() in ['xlsx', 'xls']:
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
        elif file_type.lower() == 'csv':
            df = pd.read_csv(file_path, header=None)
        
        # Generate column names
        df.columns = [f'Column{i+1}' for i in range(len(df.columns))]
        header_metadata["generated_column_names"] = True
    
    return df, header_metadata

def read_excel_sheets_with_smart_header(
    file_path: str,
    max_preview_rows: int = 5
) -> Dict[str, Dict[str, Any]]:
    """
    Read all sheets from an Excel file with smart header detection.
    
    Args:
        file_path: Path to the Excel file
        max_preview_rows: Maximum number of rows to include in the preview
        
    Returns:
        Dict[str, Dict[str, Any]]: Dictionary with sheet names as keys and sheet info as values
    """
    # Get all sheet names
    excel_file = pd.ExcelFile(file_path)
    sheet_names = excel_file.sheet_names
    
    # Process each sheet
    sheet_info = {}
    for sheet in sheet_names:
        try:
            # Read the sheet with smart header detection
            df, header_metadata = read_file_with_smart_header(file_path, 'xlsx', sheet)
            
            # Get row count
            row_count = len(df)
            
            # Get preview data
            preview_data = df.head(max_preview_rows).replace({np.nan: None}).to_dict('records')
            
            # Store sheet info
            sheet_info[sheet] = {
                "columns": list(df.columns),
                "preview": preview_data,
                "row_count": row_count,
                "header_metadata": header_metadata
            }
        except Exception as e:
            logger.error(f"Error processing sheet {sheet}: {str(e)}")
            sheet_info[sheet] = {
                "columns": [],
                "preview": [],
                "row_count": 0,
                "error": str(e)
            }
    
    return sheet_info
