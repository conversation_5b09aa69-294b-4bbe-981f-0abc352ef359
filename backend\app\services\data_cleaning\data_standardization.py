import logging
import re
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

import pandas as pd
import numpy as np
from sqlalchemy.orm import Session

from app.models.data_source import DataSource
from app.utils.file_handlers import dataframe_to_dict_list
from app.utils.gcp_storage import storage_client

logger = logging.getLogger(__name__)


def standardize_data(
    data_source: DataSource,
    config: Dict[str, Any],
    db: Optional[Session] = None
) -> Dict[str, Any]:
    """
    Standardize data from a data source.
    
    Args:
        data_source: Data source model
        config: Configuration for standardization
        db: Database session (optional)
        
    Returns:
        Dictionary with standardization results
    """
    try:
        # Load data from source
        df = load_dataframe_from_source(data_source)
        
        # Get standardization rules from config
        rules = config.get("rules", {})
        
        # Apply standardization rules
        standardized_df, changes = apply_standardization_rules(df, rules)
        
        # Save standardized data if specified
        output_path = None
        if config.get("save_output", False):
            output_path = f"standardized/{data_source.id}/{datetime.now().strftime('%Y%m%d%H%M%S')}.csv"
            save_standardized_data(standardized_df, output_path)
        
        # Return results
        return {
            "original_rows": len(df),
            "standardized_rows": len(standardized_df),
            "changes": changes,
            "output_path": output_path,
            "sample_data": dataframe_to_dict_list(standardized_df.head(10))
        }
    except Exception as e:
        logger.error(f"Error standardizing data: {e}")
        raise ValueError(f"Error standardizing data: {str(e)}")


def load_dataframe_from_source(data_source: DataSource) -> pd.DataFrame:
    """
    Load data from a data source into a pandas DataFrame.
    
    Args:
        data_source: Data source model
        
    Returns:
        DataFrame containing the data
    """
    if data_source.source_type == "file":
        # Download file from GCS
        local_path = f"/tmp/{data_source.file_path.split('/')[-1]}"
        storage_client.download_file(data_source.file_path, local_path)
        
        # Read file based on type
        if data_source.file_type == "csv":
            return pd.read_csv(local_path)
        elif data_source.file_type == "excel":
            return pd.read_excel(local_path)
        elif data_source.file_type == "json":
            return pd.read_json(local_path)
        else:
            raise ValueError(f"Unsupported file type: {data_source.file_type}")
    elif data_source.source_type == "database":
        # Implement database connection logic
        raise NotImplementedError("Database source loading not implemented yet")
    elif data_source.source_type == "cloud_service":
        # Implement cloud service connection logic
        raise NotImplementedError("Cloud service source loading not implemented yet")
    else:
        raise ValueError(f"Unsupported source type: {data_source.source_type}")


def apply_standardization_rules(df: pd.DataFrame, rules: Dict[str, Any]) -> tuple[pd.DataFrame, Dict[str, int]]:
    """
    Apply standardization rules to a DataFrame.
    
    Args:
        df: DataFrame to standardize
        rules: Dictionary of standardization rules
        
    Returns:
        Tuple containing:
        - Standardized DataFrame
        - Dictionary with counts of changes made
    """
    # Create a copy of the DataFrame to avoid modifying the original
    standardized_df = df.copy()
    
    # Track changes made
    changes = {
        "date_format": 0,
        "case_normalization": 0,
        "whitespace_removal": 0,
        "currency_format": 0,
        "numeric_format": 0,
        "missing_values": 0,
        "data_type_conversion": 0,
        "text_normalization": 0
    }
    
    # Apply date format standardization
    if "date_columns" in rules:
        for column in rules["date_columns"]:
            if column in standardized_df.columns:
                date_format = rules.get("date_format", "%Y-%m-%d")
                changes["date_format"] += standardize_dates(standardized_df, column, date_format)
    
    # Apply case normalization
    if "case_normalization" in rules:
        for column, case in rules["case_normalization"].items():
            if column in standardized_df.columns:
                changes["case_normalization"] += normalize_case(standardized_df, column, case)
    
    # Apply whitespace removal
    if "remove_whitespace" in rules:
        for column in rules["remove_whitespace"]:
            if column in standardized_df.columns:
                changes["whitespace_removal"] += remove_whitespace(standardized_df, column)
    
    # Apply currency format standardization
    if "currency_columns" in rules:
        for column in rules["currency_columns"]:
            if column in standardized_df.columns:
                currency_symbol = rules.get("currency_symbol", "$")
                changes["currency_format"] += standardize_currency(standardized_df, column, currency_symbol)
    
    # Apply numeric format standardization
    if "numeric_columns" in rules:
        for column in rules["numeric_columns"]:
            if column in standardized_df.columns:
                decimal_places = rules.get("decimal_places", 2)
                changes["numeric_format"] += standardize_numeric(standardized_df, column, decimal_places)
    
    # Handle missing values
    if "missing_values" in rules:
        for column, strategy in rules["missing_values"].items():
            if column in standardized_df.columns:
                changes["missing_values"] += handle_missing_values(standardized_df, column, strategy)
    
    # Apply data type conversion
    if "data_types" in rules:
        for column, data_type in rules["data_types"].items():
            if column in standardized_df.columns:
                changes["data_type_conversion"] += convert_data_type(standardized_df, column, data_type)
    
    # Apply text normalization
    if "text_normalization" in rules:
        for column in rules["text_normalization"]:
            if column in standardized_df.columns:
                changes["text_normalization"] += normalize_text(standardized_df, column)
    
    return standardized_df, changes


def standardize_dates(df: pd.DataFrame, column: str, date_format: str) -> int:
    """
    Standardize dates in a column to a consistent format.
    
    Args:
        df: DataFrame to modify
        column: Column name
        date_format: Target date format
        
    Returns:
        Number of values changed
    """
    changes = 0
    
    # Skip if column is not in DataFrame
    if column not in df.columns:
        return changes
    
    # Convert to datetime if not already
    if not pd.api.types.is_datetime64_dtype(df[column]):
        # Count non-null values before conversion
        non_null_before = df[column].notna().sum()
        
        # Try to convert to datetime
        try:
            df[column] = pd.to_datetime(df[column], errors='coerce')
            
            # Count non-null values after conversion
            non_null_after = df[column].notna().sum()
            
            # Count changes (values that couldn't be converted to datetime)
            changes += (non_null_before - non_null_after)
        except Exception as e:
            logger.warning(f"Error converting column {column} to datetime: {e}")
            return changes
    
    # Format dates according to specified format
    try:
        # Count non-null values
        non_null = df[column].notna().sum()
        
        # Format dates
        df[column] = df[column].dt.strftime(date_format)
        
        # Count changes (all non-null values were reformatted)
        changes += non_null
    except Exception as e:
        logger.warning(f"Error formatting dates in column {column}: {e}")
    
    return changes


def normalize_case(df: pd.DataFrame, column: str, case: str) -> int:
    """
    Normalize text case in a column.
    
    Args:
        df: DataFrame to modify
        column: Column name
        case: Target case ('upper', 'lower', 'title')
        
    Returns:
        Number of values changed
    """
    changes = 0
    
    # Skip if column is not in DataFrame
    if column not in df.columns:
        return changes
    
    # Skip if column is not string type
    if not pd.api.types.is_string_dtype(df[column]):
        return changes
    
    # Convert to string if needed
    df[column] = df[column].astype(str)
    
    # Store original values
    original_values = df[column].copy()
    
    # Apply case normalization
    if case.lower() == 'upper':
        df[column] = df[column].str.upper()
    elif case.lower() == 'lower':
        df[column] = df[column].str.lower()
    elif case.lower() == 'title':
        df[column] = df[column].str.title()
    else:
        logger.warning(f"Unknown case normalization: {case}")
        return changes
    
    # Count changes
    changes = (original_values != df[column]).sum()
    
    return changes


def remove_whitespace(df: pd.DataFrame, column: str) -> int:
    """
    Remove extra whitespace from a column.
    
    Args:
        df: DataFrame to modify
        column: Column name
        
    Returns:
        Number of values changed
    """
    changes = 0
    
    # Skip if column is not in DataFrame
    if column not in df.columns:
        return changes
    
    # Skip if column is not string type
    if not pd.api.types.is_string_dtype(df[column]):
        return changes
    
    # Convert to string if needed
    df[column] = df[column].astype(str)
    
    # Store original values
    original_values = df[column].copy()
    
    # Remove leading and trailing whitespace
    df[column] = df[column].str.strip()
    
    # Replace multiple spaces with a single space
    df[column] = df[column].str.replace(r'\s+', ' ', regex=True)
    
    # Count changes
    changes = (original_values != df[column]).sum()
    
    return changes


def standardize_currency(df: pd.DataFrame, column: str, currency_symbol: str) -> int:
    """
    Standardize currency values in a column.
    
    Args:
        df: DataFrame to modify
        column: Column name
        currency_symbol: Currency symbol to use
        
    Returns:
        Number of values changed
    """
    changes = 0
    
    # Skip if column is not in DataFrame
    if column not in df.columns:
        return changes
    
    # Store original values
    original_values = df[column].copy()
    
    # If column is string type, extract numeric values
    if pd.api.types.is_string_dtype(df[column]):
        # Remove currency symbols and commas
        df[column] = df[column].astype(str).str.replace(r'[^\d.-]', '', regex=True)
        
        # Convert to numeric
        df[column] = pd.to_numeric(df[column], errors='coerce')
    
    # Format as currency
    # Note: We're not actually adding the currency symbol here
    # as it's better to keep the data numeric and add formatting during display
    
    # Count changes
    changes = (original_values != df[column]).sum()
    
    return changes


def standardize_numeric(df: pd.DataFrame, column: str, decimal_places: int) -> int:
    """
    Standardize numeric values in a column.
    
    Args:
        df: DataFrame to modify
        column: Column name
        decimal_places: Number of decimal places
        
    Returns:
        Number of values changed
    """
    changes = 0
    
    # Skip if column is not in DataFrame
    if column not in df.columns:
        return changes
    
    # Store original values
    original_values = df[column].copy()
    
    # If column is string type, convert to numeric
    if pd.api.types.is_string_dtype(df[column]):
        # Remove non-numeric characters except decimal point and negative sign
        df[column] = df[column].astype(str).str.replace(r'[^\d.-]', '', regex=True)
        
        # Convert to numeric
        df[column] = pd.to_numeric(df[column], errors='coerce')
    
    # Round to specified decimal places
    if pd.api.types.is_numeric_dtype(df[column]):
        df[column] = df[column].round(decimal_places)
    
    # Count changes
    changes = (original_values != df[column]).sum()
    
    return changes


def handle_missing_values(df: pd.DataFrame, column: str, strategy: str) -> int:
    """
    Handle missing values in a column.
    
    Args:
        df: DataFrame to modify
        column: Column name
        strategy: Strategy for handling missing values ('drop', 'mean', 'median', 'mode', 'constant:value')
        
    Returns:
        Number of values changed
    """
    changes = 0
    
    # Skip if column is not in DataFrame
    if column not in df.columns:
        return changes
    
    # Count missing values
    missing_count = df[column].isna().sum()
    
    # Apply strategy
    if strategy == 'drop':
        # Don't actually drop rows here, just count
        changes = missing_count
    elif strategy == 'mean' and pd.api.types.is_numeric_dtype(df[column]):
        df[column] = df[column].fillna(df[column].mean())
        changes = missing_count
    elif strategy == 'median' and pd.api.types.is_numeric_dtype(df[column]):
        df[column] = df[column].fillna(df[column].median())
        changes = missing_count
    elif strategy == 'mode':
        mode_value = df[column].mode().iloc[0] if not df[column].mode().empty else None
        if mode_value is not None:
            df[column] = df[column].fillna(mode_value)
            changes = missing_count
    elif strategy.startswith('constant:'):
        constant_value = strategy.split(':', 1)[1]
        
        # Convert constant value to appropriate type
        if pd.api.types.is_numeric_dtype(df[column]):
            try:
                constant_value = float(constant_value)
            except ValueError:
                constant_value = np.nan
        
        df[column] = df[column].fillna(constant_value)
        changes = missing_count
    else:
        logger.warning(f"Unknown missing value strategy: {strategy}")
    
    return changes


def convert_data_type(df: pd.DataFrame, column: str, data_type: str) -> int:
    """
    Convert a column to a specified data type.
    
    Args:
        df: DataFrame to modify
        column: Column name
        data_type: Target data type ('int', 'float', 'str', 'bool', 'datetime')
        
    Returns:
        Number of values changed
    """
    changes = 0
    
    # Skip if column is not in DataFrame
    if column not in df.columns:
        return changes
    
    # Store original values
    original_values = df[column].copy()
    
    # Apply data type conversion
    try:
        if data_type == 'int':
            df[column] = pd.to_numeric(df[column], errors='coerce').astype('Int64')  # Use nullable integer type
        elif data_type == 'float':
            df[column] = pd.to_numeric(df[column], errors='coerce')
        elif data_type == 'str':
            df[column] = df[column].astype(str)
        elif data_type == 'bool':
            # Handle various boolean representations
            if pd.api.types.is_string_dtype(df[column]):
                # Convert string representations to boolean
                true_values = ['true', 'yes', 'y', '1', 't']
                false_values = ['false', 'no', 'n', '0', 'f']
                
                df[column] = df[column].astype(str).str.lower()
                df[column] = df[column].apply(
                    lambda x: True if x in true_values else (False if x in false_values else None)
                )
            else:
                # For numeric columns, 0 is False, everything else is True
                df[column] = df[column].astype(bool)
        elif data_type == 'datetime':
            df[column] = pd.to_datetime(df[column], errors='coerce')
        else:
            logger.warning(f"Unknown data type: {data_type}")
            return changes
    except Exception as e:
        logger.warning(f"Error converting column {column} to {data_type}: {e}")
        return changes
    
    # Count changes
    changes = (original_values != df[column]).sum()
    
    return changes


def normalize_text(df: pd.DataFrame, column: str) -> int:
    """
    Normalize text in a column (remove special characters, standardize spacing).
    
    Args:
        df: DataFrame to modify
        column: Column name
        
    Returns:
        Number of values changed
    """
    changes = 0
    
    # Skip if column is not in DataFrame
    if column not in df.columns:
        return changes
    
    # Skip if column is not string type
    if not pd.api.types.is_string_dtype(df[column]):
        return changes
    
    # Convert to string if needed
    df[column] = df[column].astype(str)
    
    # Store original values
    original_values = df[column].copy()
    
    # Normalize text
    # 1. Convert to lowercase
    df[column] = df[column].str.lower()
    
    # 2. Remove special characters
    df[column] = df[column].str.replace(r'[^\w\s]', '', regex=True)
    
    # 3. Replace multiple spaces with a single space
    df[column] = df[column].str.replace(r'\s+', ' ', regex=True)
    
    # 4. Remove leading and trailing whitespace
    df[column] = df[column].str.strip()
    
    # Count changes
    changes = (original_values != df[column]).sum()
    
    return changes


def save_standardized_data(df: pd.DataFrame, output_path: str) -> str:
    """
    Save standardized data to GCS.
    
    Args:
        df: DataFrame to save
        output_path: Path in GCS to save to
        
    Returns:
        GCS path where data was saved
    """
    # Save to a temporary file
    local_path = f"/tmp/{output_path.split('/')[-1]}"
    df.to_csv(local_path, index=False)
    
    # Upload to GCS
    with open(local_path, 'rb') as f:
        storage_client.upload_file(f, output_path)
    
    return output_path
