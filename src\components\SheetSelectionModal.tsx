import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { FileSpreadsheet, Check, AlertCircle } from 'lucide-react';

interface SheetPreview {
  columns: string[];
  preview: any[];
  row_count: number;
  error?: string;
}

interface SheetSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectSheet: (sheetName: string) => void;
  sheetNames: string[];
  sheetPreviews: Record<string, SheetPreview>;
  defaultSheet: string;
}

const SheetSelectionModal: React.FC<SheetSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelectSheet,
  sheetNames,
  sheetPreviews,
  defaultSheet
}) => {
  const [selectedSheet, setSelectedSheet] = useState<string>(defaultSheet);

  const handleConfirm = () => {
    onSelectSheet(selectedSheet);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5 text-blue-500" />
            Select Excel Sheet
          </DialogTitle>
          <DialogDescription>
            This Excel file contains multiple sheets. Please select which sheet you want to upload.
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex-1 overflow-hidden">
          <RadioGroup 
            value={selectedSheet} 
            onValueChange={setSelectedSheet}
            className="space-y-4"
          >
            {sheetNames.map((sheetName) => {
              const preview = sheetPreviews[sheetName];
              const hasError = !!preview?.error;
              
              return (
                <div 
                  key={sheetName}
                  className={`border rounded-md p-4 ${selectedSheet === sheetName ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}
                >
                  <div className="flex items-start gap-2">
                    <RadioGroupItem value={sheetName} id={`sheet-${sheetName}`} className="mt-1" />
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <Label 
                          htmlFor={`sheet-${sheetName}`} 
                          className="text-lg font-medium cursor-pointer"
                        >
                          {sheetName}
                        </Label>
                        {hasError ? (
                          <div className="flex items-center text-red-600 text-sm">
                            <AlertCircle size={14} className="mr-1" />
                            Error loading preview
                          </div>
                        ) : (
                          <div className="text-sm text-gray-500">
                            {preview?.row_count || 0} rows
                          </div>
                        )}
                      </div>
                      
                      {!hasError && preview && (
                        <ScrollArea className="h-48 mt-2 border rounded-md">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                {preview.columns.map((column, index) => (
                                  <TableHead key={index} className="text-xs">{column}</TableHead>
                                ))}
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {preview.preview.map((row, rowIndex) => (
                                <TableRow key={rowIndex}>
                                  {preview.columns.map((column, colIndex) => (
                                    <TableCell key={colIndex} className="text-xs py-1">
                                      {row[column]?.toString() || ''}
                                    </TableCell>
                                  ))}
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </ScrollArea>
                      )}
                      
                      {hasError && (
                        <div className="h-48 mt-2 border rounded-md bg-red-50 p-4 flex items-center justify-center">
                          <p className="text-red-600">{preview.error}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </RadioGroup>
        </div>
        
        <DialogFooter className="mt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleConfirm}>
            <Check size={16} className="mr-2" />
            Select Sheet
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default SheetSelectionModal;
