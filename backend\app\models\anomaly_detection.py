from datetime import datetime
from sqlalchemy import Column, DateTime, Enum, ForeignKey, Integer, JSON, String, Text, Float
from sqlalchemy.orm import relationship

from app.db.base_class import Base


class AnomalyDetectionJob(Base):
    """Model for storing anomaly detection jobs."""

    __tablename__ = "anomaly_detection_jobs"

    id = Column(String, primary_key=True, index=True)  # Using job_id as primary key
    data_source_id = Column(Integer)  # No foreign key for now
    pipeline_id = Column(Integer, ForeignKey("pipeline.id"))
    status = Column(Enum("pending", "processing", "completed", "failed", name="job_status"), default="pending")
    progress = Column(Float, default=0.0)
    config = Column(JSON, nullable=True)  # Job configuration as JSON
    result = Column(JSON, nullable=True)  # Job results as JSON
    error_message = Column(Text, nullable=True)
    output_path = Column(String, nullable=True)  # Path to output file in GCS
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)

    # Relationships
    pipeline = relationship("Pipeline", back_populates="anomaly_detection_jobs")


# Update the relationship in Pipeline model
from app.models.pipeline import Pipeline
Pipeline.anomaly_detection_jobs = relationship("AnomalyDetectionJob", back_populates="pipeline")
