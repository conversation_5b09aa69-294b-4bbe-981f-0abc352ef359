from fastapi import APIRouter

from app.api.endpoints import data_ingestion, data_cleaning, reconciliation, reporting, audit, anomaly_detection, data_sources, transaction_categorization, simple_data, excel_reports

api_router = APIRouter()

api_router.include_router(data_sources.router, prefix="/data-sources", tags=["data-sources"])
api_router.include_router(data_ingestion.router, prefix="/data-ingestion", tags=["data-ingestion"])
api_router.include_router(data_cleaning.router, prefix="/data-cleaning", tags=["data-cleaning"])
api_router.include_router(anomaly_detection.router, prefix="/anomaly-detection", tags=["anomaly-detection"])
api_router.include_router(reconciliation.router, prefix="/reconciliation", tags=["reconciliation"])
api_router.include_router(reporting.router, prefix="/reporting", tags=["reporting"])
api_router.include_router(audit.router, prefix="/audit", tags=["audit"])
api_router.include_router(transaction_categorization.router, prefix="/transaction-categorization", tags=["transaction-categorization"])
api_router.include_router(simple_data.router, prefix="/simple", tags=["simple-data"])
api_router.include_router(excel_reports.router, prefix="/excel-reports", tags=["excel-reports"])
