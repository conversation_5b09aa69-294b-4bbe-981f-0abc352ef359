import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Info, 
  BarChart4,
  FileText,
  Calendar,
  Hash,
  DollarSign,
  Percent
} from 'lucide-react';

interface QualityMetric {
  name: string;
  score: number;
  category: string;
  description: string;
  icon?: React.ReactNode;
}

interface DataQualityProps {
  overallScore: number;
  metrics: QualityMetric[];
  recommendations?: string[];
}

const DataQualityScorecard: React.FC<DataQualityProps> = ({
  overallScore,
  metrics,
  recommendations = []
}) => {
  // Group metrics by category
  const metricsByCategory: Record<string, QualityMetric[]> = {};
  metrics.forEach(metric => {
    if (!metricsByCategory[metric.category]) {
      metricsByCategory[metric.category] = [];
    }
    metricsByCategory[metric.category].push(metric);
  });

  // Get score color and label
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-amber-600';
    return 'text-red-600';
  };

  const getScoreLabel = (score: number) => {
    if (score >= 90) return 'Excellent';
    if (score >= 80) return 'Good';
    if (score >= 70) return 'Fair';
    if (score >= 50) return 'Poor';
    return 'Critical';
  };

  const getScoreBadge = (score: number) => {
    if (score >= 90) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>;
    if (score >= 80) return <Badge className="bg-blue-100 text-blue-800">Good</Badge>;
    if (score >= 70) return <Badge className="bg-amber-100 text-amber-800">Fair</Badge>;
    if (score >= 50) return <Badge className="bg-orange-100 text-orange-800">Poor</Badge>;
    return <Badge className="bg-red-100 text-red-800">Critical</Badge>;
  };

  // Get progress color
  const getProgressColor = (score: number) => {
    if (score >= 90) return 'bg-green-600';
    if (score >= 80) return 'bg-blue-600';
    if (score >= 70) return 'bg-amber-600';
    if (score >= 50) return 'bg-orange-600';
    return 'bg-red-600';
  };

  // Get icon for metric
  const getMetricIcon = (metric: QualityMetric) => {
    if (metric.icon) return metric.icon;
    
    switch (metric.category) {
      case 'Completeness':
        return <CheckCircle size={16} />;
      case 'Accuracy':
        return <AlertTriangle size={16} />;
      case 'Consistency':
        return <BarChart4 size={16} />;
      case 'Timeliness':
        return <Calendar size={16} />;
      case 'Uniqueness':
        return <Hash size={16} />;
      case 'Validity':
        return <FileText size={16} />;
      default:
        return <Info size={16} />;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Data Quality Scorecard</CardTitle>
        <CardDescription>
          Overall assessment of data quality after cleaning
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview">
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="metrics">Detailed Metrics</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-6">
            <div className="flex flex-col items-center justify-center p-6 bg-slate-50 rounded-lg">
              <div className="relative w-40 h-40 mb-4">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className={`text-5xl font-bold ${getScoreColor(overallScore)}`}>
                    {overallScore}
                  </div>
                </div>
                <svg className="w-full h-full" viewBox="0 0 100 100">
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke="#e5e7eb"
                    strokeWidth="10"
                  />
                  <circle
                    cx="50"
                    cy="50"
                    r="45"
                    fill="none"
                    stroke={getProgressColor(overallScore).replace('bg-', 'text-')}
                    strokeWidth="10"
                    strokeDasharray={`${2 * Math.PI * 45 * overallScore / 100} ${2 * Math.PI * 45 * (100 - overallScore) / 100}`}
                    strokeDashoffset={2 * Math.PI * 45 * 25 / 100}
                    transform="rotate(-90 50 50)"
                    className="transition-all duration-1000 ease-in-out"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold mb-1">Overall Quality Score</h3>
              <p className="text-gray-500 mb-4">
                {getScoreLabel(overallScore)}
              </p>
              
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 w-full mt-4">
                {Object.entries(metricsByCategory).map(([category, categoryMetrics]) => {
                  // Calculate average score for the category
                  const avgScore = categoryMetrics.reduce((sum, m) => sum + m.score, 0) / categoryMetrics.length;
                  
                  return (
                    <div key={category} className="flex flex-col items-center p-3 bg-white rounded-lg border">
                      <h4 className="text-sm font-medium text-gray-500 mb-1">{category}</h4>
                      <div className={`text-xl font-bold ${getScoreColor(avgScore)}`}>
                        {Math.round(avgScore)}
                      </div>
                      <Progress 
                        value={avgScore} 
                        className="h-1.5 mt-1"
                        indicatorClassName={getProgressColor(avgScore)}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-3">Strengths</h3>
                <div className="space-y-2">
                  {metrics
                    .filter(m => m.score >= 80)
                    .sort((a, b) => b.score - a.score)
                    .slice(0, 3)
                    .map((metric, index) => (
                      <div key={index} className="flex items-center p-3 bg-green-50 rounded-lg">
                        <CheckCircle className="text-green-600 mr-2 shrink-0" size={16} />
                        <div>
                          <p className="font-medium">{metric.name}</p>
                          <p className="text-sm text-gray-600">{metric.description}</p>
                        </div>
                        <Badge className="ml-auto bg-green-100 text-green-800">
                          {metric.score}
                        </Badge>
                      </div>
                    ))}
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-3">Areas for Improvement</h3>
                <div className="space-y-2">
                  {metrics
                    .filter(m => m.score < 70)
                    .sort((a, b) => a.score - b.score)
                    .slice(0, 3)
                    .map((metric, index) => (
                      <div key={index} className="flex items-center p-3 bg-red-50 rounded-lg">
                        <AlertTriangle className="text-red-600 mr-2 shrink-0" size={16} />
                        <div>
                          <p className="font-medium">{metric.name}</p>
                          <p className="text-sm text-gray-600">{metric.description}</p>
                        </div>
                        <Badge className="ml-auto bg-red-100 text-red-800">
                          {metric.score}
                        </Badge>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="metrics">
            <div className="space-y-6">
              {Object.entries(metricsByCategory).map(([category, categoryMetrics]) => (
                <div key={category}>
                  <h3 className="text-lg font-medium mb-3">{category}</h3>
                  <div className="space-y-2">
                    {categoryMetrics.map((metric, index) => (
                      <div key={index} className="flex items-center p-3 bg-slate-50 rounded-lg">
                        <div className="mr-3 text-gray-500">
                          {getMetricIcon(metric)}
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-center mb-1">
                            <p className="font-medium">{metric.name}</p>
                            {getScoreBadge(metric.score)}
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1.5">
                            <div 
                              className={`h-1.5 rounded-full ${getProgressColor(metric.score)}`}
                              style={{ width: `${metric.score}%` }}
                            ></div>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">{metric.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
          
          <TabsContent value="recommendations">
            {recommendations.length > 0 ? (
              <div className="space-y-4">
                {recommendations.map((recommendation, index) => (
                  <div key={index} className="flex p-3 bg-blue-50 rounded-lg">
                    <Info className="text-blue-600 mr-3 shrink-0 mt-0.5" size={16} />
                    <p className="text-blue-800">{recommendation}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
                <h3 className="text-lg font-medium">No Recommendations</h3>
                <p className="text-gray-500 mt-2">
                  Your data quality is good and doesn't require specific recommendations.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default DataQualityScorecard;
