from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class CleaningRequest(BaseModel):
    data_source_id: int
    pipeline_id: int
    step_order: int = 1
    operations: List[str] = Field(..., description="List of cleaning operations: 'standardize', 'anomaly_detection', 'classification'")
    config: Optional[Dict[str, Any]] = None


class CleaningResponse(BaseModel):
    pipeline_id: int
    step_id: int
    data_source_id: int
    operations: List[str]
    result: Dict[str, Any]
