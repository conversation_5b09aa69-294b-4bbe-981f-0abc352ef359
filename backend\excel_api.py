from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Form, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel
import uvicorn
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timezone, date
import uuid
import random
import logging
import pandas as pd
import numpy as np
import io
from io import BytesIO
import json
import os
import re
from pathlib import Path

# Import our custom header detection module
from header_detection import read_file_with_smart_header, read_excel_sheets_with_smart_header, detect_header_row

# Import our semantic mapping module
from semantic_mapping import analyze_dataset_schema, suggest_field_mappings, transform_column, preview_combined_data

# Helper function to convert numpy types to Python native types
def convert_numpy_types(obj):
    """Convert numpy types to Python native types for JSON serialization"""
    if isinstance(obj, dict):
        return {k: convert_numpy_types(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_numpy_types(item) for item in obj)
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return convert_numpy_types(obj.tolist())
    elif isinstance(obj, np.bool_):
        return bool(obj)
    elif obj is np.nan:
        return None
    else:
        return obj

# Data models
class FieldMapping(BaseModel):
    source_field: str
    target_field: str
    semantic_type: str  # amount, date, category, description, etc.
    is_income: Optional[bool] = None  # True for income, False for expense, None for neutral
    confidence: float

class NormalizedRow(BaseModel):
    id: str
    source_id: str
    transaction_date: str
    amount: float
    category: Optional[str] = None
    description: Optional[str] = None
    is_income: bool
    original_data: Dict[str, Any]

class AuditLog(BaseModel):
    id: str
    timestamp: str
    user_id: str = "system"
    action: str  # "schema_inference", "field_mapping_override", "normalization", etc.
    details: Dict[str, Any]

class PnLReport(BaseModel):
    id: str
    name: str
    source_ids: List[str]
    period_start: str
    period_end: str
    monthly_breakdown: Dict[str, Any]
    category_breakdown: Dict[str, Any]
    created_at: str

class SchemaInferenceRequest(BaseModel):
    source_id: str
    override_existing: bool = False

class NormalizeDataRequest(BaseModel):
    source_ids: List[str]
    field_mappings: Optional[Dict[str, List[FieldMapping]]] = None

class GeneratePnLRequest(BaseModel):
    name: str
    source_ids: List[str]
    start_date: Optional[str] = None
    end_date: Optional[str] = None

class FieldMappingOverrideRequest(BaseModel):
    source_id: str
    field_mappings: List[FieldMapping]

# Custom JSON encoder to handle NaN values, numpy types, and datetime objects
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, float) and (np.isnan(obj) or np.isinf(obj)):
            return None
        elif isinstance(obj, (datetime, date)):
            return obj.isoformat()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.bool_):
            return bool(obj)
        return super().default(obj)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create storage directory if it doesn't exist
STORAGE_DIR = Path("./storage")
STORAGE_DIR.mkdir(exist_ok=True)

# Configure FastAPI to use our custom JSON encoder
app = FastAPI(
    title="Excel Data Processor API",
    json_encoder=CustomJSONEncoder
)

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for prototype
DATA_SOURCES = []
ANOMALY_RESULTS = {}
COMBINED_DATASETS = {}
FIELD_MAPPINGS = {}
NORMALIZED_DATA = {}
AUDIT_LOGS = []
PNL_REPORTS = {}

@app.get("/")
async def root():
    return {"message": "Excel Data Processor API", "version": "1.0.0"}

@app.post("/api/get-excel-sheets")
async def get_excel_sheets(file: UploadFile = File(...)):
    """
    Get sheet names and previews from an Excel file without saving it.
    Uses smart header detection to find the correct header row.
    """
    try:
        logger.info(f"Getting sheets from file: {file.filename}")

        # Read file content
        contents = await file.read()

        # Check if it's an Excel file
        file_extension = file.filename.split('.')[-1].lower()
        if file_extension not in ['xlsx', 'xls']:
            raise HTTPException(status_code=400, detail="Not an Excel file. Please upload an Excel file.")

        # Use BytesIO instead of a temporary file to avoid file access issues
        buffer = io.BytesIO(contents)

        try:
            # Get Excel file object
            excel_file = pd.ExcelFile(buffer)
            available_sheets = excel_file.sheet_names

            # Process each sheet
            sheet_previews = {}
            for sheet in available_sheets:
                try:
                    # Read a sample without assuming headers
                    sample_df = pd.read_excel(buffer, sheet_name=sheet, header=None, nrows=15)

                    # Detect the header row
                    header_row, header_score = detect_header_row(sample_df)

                    # Reset buffer position
                    buffer.seek(0)

                    # Read with the detected header
                    if header_score >= 20.0:  # Threshold for a good header
                        df = pd.read_excel(buffer, sheet_name=sheet, header=header_row)
                    else:
                        # If no good header row was found, use default column names
                        df = pd.read_excel(buffer, sheet_name=sheet, header=None)
                        df.columns = [f'Column{i+1}' for i in range(len(df.columns))]

                    # Reset buffer position again for next sheet
                    buffer.seek(0)

                    # Get row count
                    row_count = len(df)

                    # Get preview data
                    preview_data = df.head(5).replace({np.nan: None}).to_dict('records')

                    # Create header metadata
                    header_metadata = {
                        "detected_header_row": header_row,
                        "header_score": header_score,
                        "header_detection_method": "smart",
                        "original_columns": sample_df.iloc[header_row].tolist() if header_score >= 20.0 else None,
                        "generated_column_names": header_score < 20.0
                    }

                    # Store sheet info
                    sheet_previews[sheet] = {
                        "columns": list(df.columns),
                        "preview": preview_data,
                        "row_count": row_count,
                        "header_metadata": header_metadata
                    }
                except Exception as e:
                    logger.error(f"Error processing sheet {sheet}: {str(e)}")
                    sheet_previews[sheet] = {
                        "columns": [],
                        "preview": [],
                        "row_count": 0,
                        "error": str(e)
                    }

            # Convert all numpy types to Python native types for JSON serialization
            result = {
                "sheet_names": available_sheets,
                "previews": sheet_previews,
                "default_sheet": available_sheets[0] if available_sheets else ""
            }

            # Convert numpy types to Python native types
            result = convert_numpy_types(result)

            return result
        except Exception as e:
            logger.error(f"Error processing Excel file: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing Excel file: {str(e)}")
    except Exception as e:
        logger.error(f"Error processing Excel file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing Excel file: {str(e)}")

@app.post("/api/upload-file")
async def upload_file(
    file: UploadFile = File(...),
    name: str = Form(None),
    sheet_name: str = Form(None)
):
    """
    Upload and process an Excel or CSV file.
    Returns basic stats and saves the file for further processing.
    """
    try:
        logger.info(f"Received file: {file.filename}")

        # Read file content
        contents = await file.read()

        # Determine file type and read with pandas
        file_extension = file.filename.split('.')[-1].lower()

        # Use BytesIO instead of a temporary file
        buffer = io.BytesIO(contents)

        try:
            if file_extension in ['xlsx', 'xls']:
                # Excel file
                excel_file = pd.ExcelFile(buffer)
                available_sheets = excel_file.sheet_names

                # If there are multiple sheets and no sheet is specified, return sheet info
                if len(available_sheets) > 1 and not sheet_name:
                    # Process each sheet
                    sheet_previews = {}
                    for sheet in available_sheets:
                        try:
                            # Read a sample without assuming headers
                            sample_df = pd.read_excel(buffer, sheet_name=sheet, header=None, nrows=15)

                            # Detect the header row
                            header_row, header_score = detect_header_row(sample_df)

                            # Read with the detected header
                            if header_score >= 20.0:  # Threshold for a good header
                                df = pd.read_excel(buffer, sheet_name=sheet, header=header_row)
                            else:
                                # If no good header row was found, use default column names
                                df = pd.read_excel(buffer, sheet_name=sheet, header=None)
                                df.columns = [f'Column{i+1}' for i in range(len(df.columns))]

                            # Get row count
                            row_count = len(df)

                            # Get preview data
                            preview_data = df.head(5).replace({np.nan: None}).to_dict('records')

                            # Create header metadata
                            header_metadata = {
                                "detected_header_row": int(header_row),
                                "header_score": float(header_score),
                                "header_detection_method": "smart",
                                "original_columns": sample_df.iloc[header_row].tolist() if header_score >= 20.0 else None,
                                "generated_column_names": bool(header_score < 20.0)
                            }

                            # Store sheet info
                            sheet_previews[sheet] = {
                                "columns": list(df.columns),
                                "preview": preview_data,
                                "row_count": row_count,
                                "header_metadata": header_metadata
                            }
                        except Exception as e:
                            logger.error(f"Error processing sheet {sheet}: {str(e)}")
                            sheet_previews[sheet] = {
                                "columns": [],
                                "preview": [],
                                "row_count": 0,
                                "error": str(e)
                            }

                    # Return sheet information
                    result = {
                        "message": "Multiple sheets detected",
                        "sheet_names": available_sheets,
                        "previews": sheet_previews,
                        "default_sheet": available_sheets[0] if available_sheets else None,
                        "requires_sheet_selection": True
                    }

                    # Convert numpy types to Python native types
                    result = convert_numpy_types(result)

                    return result

                # Read the specified sheet or the first sheet by default with smart header detection
                # Read a sample without assuming headers
                sample_df = pd.read_excel(buffer, sheet_name=sheet_name if sheet_name else available_sheets[0], header=None, nrows=15)

                # Detect the header row
                header_row, header_score = detect_header_row(sample_df)

                # Reset buffer position
                buffer.seek(0)

                # Read with the detected header
                if header_score >= 20.0:  # Threshold for a good header
                    df = pd.read_excel(buffer, sheet_name=sheet_name if sheet_name else available_sheets[0], header=header_row)
                else:
                    # If no good header row was found, use default column names
                    df = pd.read_excel(buffer, sheet_name=sheet_name if sheet_name else available_sheets[0], header=None)
                    df.columns = [f'Column{i+1}' for i in range(len(df.columns))]

                # Create header metadata
                header_metadata = {
                    "detected_header_row": int(header_row),
                    "header_score": float(header_score),
                    "header_detection_method": "smart",
                    "original_columns": sample_df.iloc[header_row].tolist() if header_score >= 20.0 else None,
                    "generated_column_names": bool(header_score < 20.0)
                }

                # Replace NaN values with None for JSON serialization
                df = df.replace({np.nan: None})
                original_extension = file_extension

                # Log header detection results
                logger.info(f"Header detection results: {header_metadata}")

            elif file_extension in ['csv']:
                # CSV file with smart header detection
                try:
                    # Read a sample without assuming headers
                    sample_df = pd.read_csv(buffer, header=None, nrows=15)

                    # Detect the header row
                    header_row, header_score = detect_header_row(sample_df)

                    # Reset buffer position
                    buffer.seek(0)

                    # Read with the detected header
                    if header_score >= 20.0:  # Threshold for a good header
                        df = pd.read_csv(buffer, header=header_row)
                    else:
                        # If no good header row was found, use default column names
                        df = pd.read_csv(buffer, header=None)
                        df.columns = [f'Column{i+1}' for i in range(len(df.columns))]
                except Exception as e:
                    logger.error(f"Error processing CSV file: {str(e)}")
                    # Fallback to basic CSV reading
                    buffer.seek(0)
                    df = pd.read_csv(buffer)
                    header_row = 0
                    header_score = 50.0

                # Create header metadata
                header_metadata = {
                    "detected_header_row": int(header_row),
                    "header_score": float(header_score),
                    "header_detection_method": "smart",
                    "original_columns": sample_df.iloc[header_row].tolist() if header_score >= 20.0 else None,
                    "generated_column_names": bool(header_score < 20.0)
                }

                # Replace NaN values with None for JSON serialization
                df = df.replace({np.nan: None})
                original_extension = 'csv'

                # Log header detection results
                logger.info(f"Header detection results: {header_metadata}")

            else:
                raise HTTPException(status_code=400, detail=f"Unsupported file type: {file_extension}. Please upload an Excel (.xlsx, .xls) or CSV (.csv) file.")
        except Exception as e:
            logger.error(f"Error processing file: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")

        # Basic data cleaning
        # 1. Remove duplicate rows
        df_cleaned = df.drop_duplicates()

        # 2. Remove rows with all NaN values
        df_cleaned = df_cleaned.dropna(how='all')

        # 3. Fill NaN values with appropriate defaults based on column type
        for col in df_cleaned.columns:
            if df_cleaned[col].dtype == 'float64' or df_cleaned[col].dtype == 'int64':
                df_cleaned[col] = df_cleaned[col].fillna(0)
            else:
                df_cleaned[col] = df_cleaned[col].fillna('')

        # Generate a unique ID for this data source
        source_id = str(uuid.uuid4())
        file_name = name or file.filename

        # Save the file to storage with original extension
        file_path = STORAGE_DIR / f"{source_id}.{original_extension}"
        with open(file_path, "wb") as f:
            # Seek to the beginning of the file
            await file.seek(0)
            f.write(await file.read())

        # Save the cleaned data as JSON for easy access
        json_path = STORAGE_DIR / f"{source_id}.json"
        # Convert DataFrame to list of dictionaries
        records = df_cleaned.replace({np.nan: None}).to_dict('records')
        # Save with custom encoder to handle NaN values
        with open(json_path, 'w') as f:
            json.dump(records, f, cls=CustomJSONEncoder)

        # Basic stats about the data
        stats = {
            "total_rows": len(df),
            "cleaned_rows": len(df_cleaned),
            "columns": list(df_cleaned.columns),
            "duplicates_removed": len(df) - len(df_cleaned),
            "file_type": original_extension
        }

        # Create a data source record
        data_source = {
            "id": source_id,
            "name": file_name,
            "file_path": str(file_path),
            "json_path": str(json_path),
            "file_type": original_extension,
            "sheet_name": sheet_name if sheet_name else None,
            "stats": stats,
            "header_metadata": header_metadata,  # Include header detection metadata
            "created_at": datetime.now(timezone.utc).isoformat(),
        }

        # Store the data source
        DATA_SOURCES.append(data_source)

        # Convert numpy types to Python native types for JSON serialization
        result = {
            "message": f"{original_extension.upper()} file processed successfully",
            "data_source": convert_numpy_types(data_source)
        }

        return result

    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")

@app.get("/api/data-sources")
async def get_data_sources():
    """Get all data sources"""
    return DATA_SOURCES

@app.get("/api/data-sources/{source_id}")
async def get_data_source(source_id: str):
    """Get a specific data source by ID"""
    for source in DATA_SOURCES:
        if source["id"] == source_id:
            return source
    raise HTTPException(status_code=404, detail=f"Data source with ID {source_id} not found")

@app.get("/api/data-sources/{source_id}/data")
async def get_data_source_data(source_id: str, limit: int = 100, full: bool = False):
    """Get the data for a specific data source"""
    for source in DATA_SOURCES:
        if source["id"] == source_id:
            json_path = source["json_path"]
            try:
                with open(json_path, "r") as f:
                    data = json.load(f)
                    # Return full dataset if requested, otherwise limit rows
                    if full:
                        return {"data": data, "total": len(data)}
                    else:
                        return {"data": data[:limit], "total": len(data)}
            except Exception as e:
                logger.error(f"Error reading data source data: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Error reading data source data: {str(e)}")

    raise HTTPException(status_code=404, detail=f"Data source with ID {source_id} not found")

@app.get("/api/data-sources/{source_id}/download")
async def download_data_source(source_id: str, format: str = "csv"):
    """Download the full data for a specific data source"""
    for source in DATA_SOURCES:
        if source["id"] == source_id:
            json_path = source["json_path"]
            try:
                with open(json_path, "r") as f:
                    data = json.load(f)

                # Convert to DataFrame
                df = pd.DataFrame(data)

                # Create a BytesIO object to store the file
                output = BytesIO()

                # Export based on requested format
                if format.lower() == "excel" or format.lower() == "xlsx":
                    df.to_excel(output, index=False, engine="openpyxl")
                    media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    filename = f"{source['name'].replace(' ', '_')}.xlsx"
                else:  # Default to CSV
                    df.to_csv(output, index=False)
                    media_type = "text/csv"
                    filename = f"{source['name'].replace(' ', '_')}.csv"

                # Reset the pointer to the beginning of the BytesIO object
                output.seek(0)

                # Create audit log
                audit_log = {
                    "id": str(uuid.uuid4()),
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "user_id": "system",
                    "action": "dataset_download",
                    "details": {
                        "source_id": source_id,
                        "format": format,
                        "row_count": len(df)
                    }
                }
                AUDIT_LOGS.append(audit_log)

                # Return the file as a response
                return StreamingResponse(
                    output,
                    media_type=media_type,
                    headers={"Content-Disposition": f"attachment; filename={filename}"}
                )

            except Exception as e:
                logger.error(f"Error downloading data source: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Error downloading data source: {str(e)}")

    raise HTTPException(status_code=404, detail=f"Data source with ID {source_id} not found")




@app.post("/api/detect-anomalies/{source_id}")
async def detect_anomalies(source_id: str, request: Dict[str, Any]):
    """
    Detect anomalies in the data source using statistical methods.
    This is a simplified version that uses z-score for numerical columns.
    """
    # Find the data source
    data_source = None
    for source in DATA_SOURCES:
        if source["id"] == source_id:
            data_source = source
            break

    if not data_source:
        raise HTTPException(status_code=404, detail=f"Data source with ID {source_id} not found")

    try:
        # Load the data
        with open(data_source["json_path"], "r") as f:
            data = json.load(f)

        # Convert to DataFrame
        df = pd.DataFrame(data)

        # Get numerical columns
        numerical_columns = df.select_dtypes(include=['number']).columns.tolist()

        # Get columns to analyze (either from request or all numerical columns)
        columns_to_analyze = request.get("columns", numerical_columns)

        # Filter to only include numerical columns
        columns_to_analyze = [col for col in columns_to_analyze if col in numerical_columns]

        if not columns_to_analyze:
            return {
                "message": "No numerical columns to analyze",
                "anomalies": []
            }

        # Set threshold (default to 3.0 for z-score)
        threshold = request.get("threshold", 3.0)

        # Dictionary to store anomalies by column
        anomalies_by_column = {}
        all_anomaly_indices = set()

        # Detect anomalies using z-score for each column
        for column in columns_to_analyze:
            # Calculate z-score
            mean = df[column].mean()
            std = df[column].std()

            if std == 0:  # Skip columns with zero standard deviation
                continue

            df[f'{column}_zscore'] = (df[column] - mean) / std

            # Find anomalies
            anomaly_indices = df[abs(df[f'{column}_zscore']) > threshold].index.tolist()

            if anomaly_indices:
                anomalies_by_column[column] = {
                    "indices": anomaly_indices,
                    "count": len(anomaly_indices),
                    "values": df.loc[anomaly_indices, column].tolist()
                }
                all_anomaly_indices.update(anomaly_indices)

        # Create anomaly records
        anomaly_records = []
        for idx in all_anomaly_indices:
            record = df.loc[idx].to_dict()
            # Remove z-score columns
            record = {k: v for k, v in record.items() if not k.endswith('_zscore')}
            anomaly_records.append(record)

        # Create result
        result_id = str(uuid.uuid4())
        result = {
            "id": result_id,
            "data_source_id": source_id,
            "total_rows": len(df),
            "anomaly_count": len(all_anomaly_indices),
            "anomaly_indices": sorted(list(all_anomaly_indices)),
            "anomalies_by_column": anomalies_by_column,
            "anomaly_records": anomaly_records,
            "anomaly_percentage": (len(all_anomaly_indices) / len(df)) * 100 if len(df) > 0 else 0,
            "created_at": datetime.now(timezone.utc).isoformat()
        }

        # Store the result
        ANOMALY_RESULTS[result_id] = result

        return result

    except Exception as e:
        logger.error(f"Error detecting anomalies: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error detecting anomalies: {str(e)}")

@app.get("/api/anomaly-results/{result_id}")
async def get_anomaly_result(result_id: str):
    """Get a specific anomaly detection result"""
    if result_id in ANOMALY_RESULTS:
        return ANOMALY_RESULTS[result_id]
    raise HTTPException(status_code=404, detail=f"Anomaly result with ID {result_id} not found")

@app.get("/api/anomaly-results")
async def get_anomaly_results(data_source_id: Optional[str] = None):
    """Get all anomaly detection results, optionally filtered by data source ID"""
    if data_source_id:
        return {k: v for k, v in ANOMALY_RESULTS.items() if v["data_source_id"] == data_source_id}
    return ANOMALY_RESULTS

@app.post("/api/combine-datasets")
async def combine_datasets(request: Dict[str, Any]):
    """
    Combine multiple datasets into a single dataset with AI-powered schema matching.

    Request should include:
    - source_ids: List of data source IDs to combine
    - name: Name for the combined dataset
    - relationships: Optional mapping of how datasets relate to each other
    - join_strategy: How to combine the data (smart, concat, inner_join, outer_join)
    - join_keys: Optional keys to use for joining datasets
    """
    try:
        source_ids = request.get("source_ids", [])
        name = request.get("name", "Combined Dataset")
        relationships = request.get("relationships", {})
        join_strategy = request.get("join_strategy", "smart")
        join_keys = request.get("join_keys", {})

        if not source_ids or len(source_ids) < 2:
            raise HTTPException(
                status_code=400,
                detail="At least two data source IDs are required for combination"
            )

        # Find the data sources
        sources_to_combine = []
        for source_id in source_ids:
            source = None
            for s in DATA_SOURCES:
                if s["id"] == source_id:
                    source = s
                    break

            if not source:
                raise HTTPException(
                    status_code=404,
                    detail=f"Data source with ID {source_id} not found"
                )

            sources_to_combine.append(source)

        # First, analyze each source to understand its schema
        dataframes = []
        source_schemas = []
        normalized_data_by_source = {}

        for source in sources_to_combine:
            # Infer schema if not already done
            if source["id"] not in FIELD_MAPPINGS:
                schema_result = await infer_schema(SchemaInferenceRequest(source_id=source["id"]))
                field_mappings = schema_result["field_mappings"]
            else:
                field_mappings = FIELD_MAPPINGS[source["id"]]

            # Load the data
            with open(source["json_path"], "r") as f:
                data = json.load(f)
                df = pd.DataFrame(data)

                # Add source metadata columns
                df["_source_id"] = source["id"]
                df["_source_name"] = source["name"]
                df["_source_file_type"] = source.get("file_type", "unknown")

                # Store schema information with enhanced metadata
                schema_info = {
                    "source_id": source["id"],
                    "source_name": source["name"],
                    "columns": list(df.columns),
                    "dtypes": {col: str(df[col].dtype) for col in df.columns},
                    "sample_values": {col: df[col].head(5).tolist() for col in df.columns if col not in ["_source_id", "_source_name", "_source_file_type"]},
                    "field_mappings": field_mappings
                }
                source_schemas.append(schema_info)

                # If using smart join strategy, also normalize the data
                if join_strategy == "smart":
                    # Normalize the data based on field mappings
                    normalized_rows = []
                    for row in data:
                        normalized_row = normalize_row(row, field_mappings, source["id"])
                        if normalized_row:
                            normalized_rows.append(normalized_row)

                    normalized_data_by_source[source["id"]] = normalized_rows

                dataframes.append(df)

        # Perform smart schema matching
        matched_columns = smart_schema_match(source_schemas)

        # Determine the best combination strategy if using smart join
        if join_strategy == "smart":
            # Analyze the data to determine the best join strategy
            logger.info("Using AI-powered smart join strategy")

            # Check if we have normalized data for all sources
            if len(normalized_data_by_source) == len(sources_to_combine):
                # Use the normalized data to determine the best join strategy
                # This is a simplified approach - in a real implementation, you'd use more sophisticated analysis

                # Check for date ranges to see if sources are complementary or overlapping
                date_ranges = []
                for source_id, rows in normalized_data_by_source.items():
                    if rows:
                        dates = [row.get("transaction_date") for row in rows if row.get("transaction_date")]
                        if dates:
                            date_ranges.append({
                                "source_id": source_id,
                                "min_date": min(dates),
                                "max_date": max(dates)
                            })

                # If we have date ranges for all sources, check for overlaps
                if len(date_ranges) == len(sources_to_combine):
                    # Check if date ranges are complementary (minimal overlap)
                    complementary = True
                    for i in range(len(date_ranges)):
                        for j in range(i+1, len(date_ranges)):
                            range1 = date_ranges[i]
                            range2 = date_ranges[j]

                            # Check for overlap
                            if range1["min_date"] <= range2["max_date"] and range2["min_date"] <= range1["max_date"]:
                                # There is some overlap
                                complementary = False
                                break

                    if complementary:
                        # Sources cover different time periods, so concatenation makes sense
                        join_strategy = "concat"
                        logger.info("Smart join determined strategy: concat (complementary date ranges)")
                    else:
                        # Sources have overlapping time periods, try to match transactions
                        # For simplicity, we'll use a basic approach here
                        join_strategy = "smart_merge"
                        logger.info("Smart join determined strategy: smart_merge (overlapping date ranges)")
                else:
                    # Not all sources have date information, fall back to basic concatenation
                    join_strategy = "concat"
                    logger.info("Smart join determined strategy: concat (missing date information)")
            else:
                # Fall back to basic concatenation if we couldn't normalize all data
                join_strategy = "concat"
                logger.info("Smart join determined strategy: concat (normalization failed)")

        # Combine the dataframes based on the determined join strategy
        if join_strategy == "smart_merge":
            # Use the normalized data to perform a smart merge
            # This is where the AI-powered combination happens
            combined_data = []

            # Flatten all normalized data
            all_normalized_rows = []
            for source_id, rows in normalized_data_by_source.items():
                all_normalized_rows.extend(rows)

            # Group by date to find potential matches
            date_groups = {}
            for row in all_normalized_rows:
                date = row.get("transaction_date")
                if date:
                    if date not in date_groups:
                        date_groups[date] = []
                    date_groups[date].append(row)

            # For each date group, look for matching transactions
            for date, group in date_groups.items():
                # If only one transaction on this date, add it directly
                if len(group) == 1:
                    combined_data.append(group[0])
                    continue

                # Group by amount to find potential matches
                amount_groups = {}
                for row in group:
                    amount = row.get("amount")
                    if amount is not None:
                        key = f"{amount:.2f}"
                        if key not in amount_groups:
                            amount_groups[key] = []
                        amount_groups[key].append(row)

                # For each amount group, merge or add transactions
                for amount_key, amount_group in amount_groups.items():
                    if len(amount_group) == 1:
                        # Only one transaction with this date and amount
                        combined_data.append(amount_group[0])
                    else:
                        # Multiple transactions with same date and amount
                        # Check if they're from different sources
                        sources = set(row["source_id"] for row in amount_group)

                        if len(sources) == len(amount_group):
                            # Each transaction is from a different source
                            # Merge them to get the most complete information
                            merged_row = {
                                "id": str(uuid.uuid4()),
                                "source_id": "||".join(row["source_id"] for row in amount_group),
                                "transaction_date": date,
                                "amount": float(amount_key),
                                "is_income": amount_group[0].get("is_income"),
                                "original_sources": [row["source_id"] for row in amount_group],
                                "confidence": sum(row.get("confidence", 0.5) for row in amount_group) / len(amount_group)
                            }

                            # Merge other fields, taking the highest confidence value
                            for field in ["category", "description", "account_or_payee"]:
                                field_values = [(row.get(field), row.get("confidence", 0.5)) for row in amount_group if row.get(field)]
                                if field_values:
                                    # Sort by confidence and take the highest
                                    field_values.sort(key=lambda x: x[1], reverse=True)
                                    merged_row[field] = field_values[0][0]

                            # Store original data
                            merged_row["original_data"] = {row["source_id"]: row.get("original_data", {}) for row in amount_group}

                            combined_data.append(merged_row)
                        else:
                            # Some are from the same source, add them individually
                            combined_data.extend(amount_group)

            # Convert to DataFrame for consistency with other methods
            combined_df = pd.DataFrame(combined_data)

        elif join_strategy == "concat" or not join_keys:
            # Simple concatenation
            combined_df = pd.concat(dataframes, ignore_index=True)

            # Add a column to indicate which rows came from which source
            combined_df["_row_source"] = combined_df["_source_name"]

        elif join_strategy in ["inner_join", "outer_join"]:
            # For joins, we need at least one join key
            if not join_keys:
                raise HTTPException(
                    status_code=400,
                    detail="Join keys are required for inner_join or outer_join strategies"
                )

            # Start with the first dataframe
            result_df = dataframes[0]

            # Join with each subsequent dataframe
            for i, df in enumerate(dataframes[1:], 1):
                # Get the join keys for this pair of dataframes
                left_key = join_keys.get(f"{source_ids[0]}__{source_ids[i]}", [])
                right_key = join_keys.get(f"{source_ids[i]}__{source_ids[0]}", [])

                if not left_key or not right_key:
                    # If no specific join keys provided, try to use matched columns
                    common_columns = set(result_df.columns).intersection(set(df.columns))
                    common_columns = [col for col in common_columns if not col.startswith("_")]

                    if not common_columns:
                        # If no common columns, fall back to concatenation
                        logger.warning(f"No common columns found for join between sources {source_ids[0]} and {source_ids[i]}. Falling back to concatenation.")
                        result_df = pd.concat([result_df, df], ignore_index=True)
                        continue

                    left_key = right_key = common_columns

                # Perform the join
                how = "inner" if join_strategy == "inner_join" else "outer"

                # Rename columns in the right dataframe to avoid conflicts
                rename_dict = {col: f"{col}_{source_ids[i]}" for col in df.columns
                              if col in result_df.columns and col not in right_key and not col.startswith("_")}
                df_renamed = df.rename(columns=rename_dict)

                # Perform the join
                result_df = result_df.merge(df_renamed, left_on=left_key, right_on=right_key, how=how, suffixes=('', f'_{source_ids[i]}'))

            combined_df = result_df
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported join strategy: {join_strategy}. Supported strategies are: concat, inner_join, outer_join"
            )

        # Use provided ID or generate a new one
        combined_id = request.get("id", str(uuid.uuid4()))

        # Save the combined data as JSON
        json_path = STORAGE_DIR / f"combined_{combined_id}.json"
        combined_df.to_json(json_path, orient='records')

        # Create metadata for the combined dataset
        combined_dataset = {
            "id": combined_id,
            "name": name,
            "source_ids": source_ids,
            "json_path": str(json_path),
            "row_count": len(combined_df),
            "column_count": len(combined_df.columns),
            "relationships": relationships,
            "join_strategy": join_strategy,
            "join_keys": join_keys,
            "schema_mapping": matched_columns,
            "source_schemas": source_schemas,
            "created_at": datetime.now(timezone.utc).isoformat()
        }

        # Add AI-specific metadata for smart joins
        if join_strategy == "smart_merge":
            # Add information about how the data was merged
            combined_dataset["ai_metadata"] = {
                "merge_strategy": "date_and_amount_matching",
                "confidence_scoring": True,
                "field_prioritization": True,
                "merged_transaction_count": sum(1 for row in combined_data if "original_sources" in row),
                "total_transaction_count": len(combined_data)
            }

        # Store the combined dataset
        COMBINED_DATASETS[combined_id] = combined_dataset

        # Create audit log
        audit_log = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": "system",
            "action": "dataset_combination",
            "details": {
                "combined_id": combined_id,
                "source_ids": source_ids,
                "join_strategy": join_strategy,
                "row_count": len(combined_df),
                "ai_powered": join_strategy in ["smart", "smart_merge"]
            }
        }
        AUDIT_LOGS.append(audit_log)

        return combined_dataset

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error combining datasets: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error combining datasets: {str(e)}")


def smart_schema_match(source_schemas):
    """
    Perform smart schema matching between multiple data sources.
    This function tries to identify columns that represent the same data across different sources.

    Args:
        source_schemas: List of dictionaries containing schema information for each source

    Returns:
        Dictionary mapping standardized column names to source-specific column names
    """
    if not source_schemas or len(source_schemas) < 2:
        return {}

    # Initialize the result dictionary
    matched_columns = {}

    # Get all columns from all sources
    all_columns = set()
    for schema in source_schemas:
        all_columns.update(schema["columns"])

    # Remove metadata columns
    all_columns = {col for col in all_columns if not col.startswith("_")}

    # For each column, try to find matches across sources
    for col in all_columns:
        # Initialize a list to store potential matches
        matches = []

        # Check each source for this column or similar columns
        for schema in source_schemas:
            # Exact match
            if col in schema["columns"]:
                matches.append({
                    "source_id": schema["source_id"],
                    "column": col,
                    "confidence": 1.0
                })
                continue

            # Look for similar column names
            best_match = None
            best_score = 0

            for source_col in schema["columns"]:
                if source_col.startswith("_"):
                    continue

                # Calculate string similarity
                # Simple case-insensitive comparison for now
                # In a real implementation, you would use more sophisticated algorithms
                # like Levenshtein distance or word embeddings
                score = string_similarity(col.lower(), source_col.lower())

                if score > 0.7 and score > best_score:  # Threshold of 0.7
                    best_match = source_col
                    best_score = score

            if best_match:
                matches.append({
                    "source_id": schema["source_id"],
                    "column": best_match,
                    "confidence": best_score
                })

        # If we found matches in multiple sources, add to the result
        if len(matches) > 1:
            matched_columns[col] = matches

    return matched_columns


def string_similarity(s1, s2):
    """
    Calculate string similarity between two strings.
    This is a simple implementation - in production, you would use a more sophisticated algorithm.

    Args:
        s1: First string
        s2: Second string

    Returns:
        Similarity score between 0 and 1
    """
    # Simple case: exact match
    if s1 == s2:
        return 1.0

    # Check if one is a substring of the other
    if s1 in s2 or s2 in s1:
        return 0.8

    # Check for common words
    words1 = set(s1.split('_'))
    words2 = set(s2.split('_'))

    common_words = words1.intersection(words2)

    if common_words:
        return len(common_words) / max(len(words1), len(words2))

    # Fall back to character-level similarity
    # This is a very simple implementation - in production, use a proper algorithm
    shorter = s1 if len(s1) < len(s2) else s2
    longer = s2 if len(s1) < len(s2) else s1

    if len(longer) == 0:
        return 0.0

    # Count matching characters
    matches = sum(1 for c in shorter if c in longer)
    return matches / len(longer)

@app.get("/api/combined-datasets")
async def get_combined_datasets():
    """Get all combined datasets"""
    return COMBINED_DATASETS

@app.get("/api/combined-datasets/{combined_id}")
async def get_combined_dataset(combined_id: str):
    """Get a specific combined dataset"""
    if combined_id in COMBINED_DATASETS:
        return COMBINED_DATASETS[combined_id]
    raise HTTPException(status_code=404, detail=f"Combined dataset with ID {combined_id} not found")


@app.post("/api/reprocess-dataset/{dataset_id}")
async def reprocess_dataset(dataset_id: str, request: Dict[str, Any]):
    """Reprocess a combined dataset - delete and recreate it with the same sources"""
    try:
        # Check if the dataset exists
        if dataset_id not in COMBINED_DATASETS:
            raise HTTPException(status_code=404, detail=f"Combined dataset with ID {dataset_id} not found")

        # Get the existing dataset
        existing_dataset = COMBINED_DATASETS[dataset_id]
        source_ids = existing_dataset["source_ids"]
        name = existing_dataset["name"]

        # Get the join strategy from the request or use the existing one
        join_strategy = request.get("join_strategy", existing_dataset.get("join_strategy", "smart"))

        # Get field mappings if provided
        field_mappings = request.get("field_mappings", None)

        # Delete the existing dataset
        json_path = existing_dataset["json_path"]
        if os.path.exists(json_path):
            os.remove(json_path)

        del COMBINED_DATASETS[dataset_id]

        # Create a new request for combining datasets
        combine_request = {
            "source_ids": source_ids,
            "name": name,
            "relationships": existing_dataset.get("relationships", {}),
            "join_strategy": join_strategy,
            "id": dataset_id  # Reuse the dataset ID to maintain references
        }

        # If field mappings were provided, store them for each source
        if field_mappings:
            # Store the field mappings for each source
            for mapping in field_mappings:
                source_id = None
                # Find which source this field belongs to
                for sid in source_ids:
                    for source in DATA_SOURCES:
                        if source["id"] == sid:
                            # Check if this source has this field
                            with open(source["json_path"], "r") as f:
                                data = json.load(f)
                                if data and len(data) > 0 and mapping["source_field"] in data[0]:
                                    source_id = sid
                                    break
                    if source_id:
                        break

                if source_id:
                    # Store the mapping for this source
                    if source_id not in FIELD_MAPPINGS:
                        FIELD_MAPPINGS[source_id] = []

                    # Check if this field already exists in the mappings
                    field_exists = False
                    for i, existing_mapping in enumerate(FIELD_MAPPINGS[source_id]):
                        if existing_mapping["source_field"] == mapping["source_field"]:
                            # Update the existing mapping
                            FIELD_MAPPINGS[source_id][i] = mapping
                            field_exists = True
                            break

                    if not field_exists:
                        FIELD_MAPPINGS[source_id].append(mapping)

        # Combine the datasets again
        result = await combine_datasets(combine_request)

        # Create audit log
        audit_log = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": "system",
            "action": "dataset_reprocessing",
            "details": {
                "dataset_id": dataset_id,
                "source_ids": source_ids,
                "join_strategy": join_strategy
            }
        }
        AUDIT_LOGS.append(audit_log)

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error reprocessing dataset: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error reprocessing dataset: {str(e)}")

@app.get("/api/combined-datasets/{combined_id}/data")
async def get_combined_dataset_data(combined_id: str, limit: int = 100):
    """Get the data for a combined dataset"""
    if combined_id not in COMBINED_DATASETS:
        raise HTTPException(status_code=404, detail=f"Combined dataset with ID {combined_id} not found")

    combined_dataset = COMBINED_DATASETS[combined_id]

    try:
        with open(combined_dataset["json_path"], "r") as f:
            data = json.load(f)
            return {"data": data[:limit], "total": len(data)}
    except Exception as e:
        logger.error(f"Error reading combined dataset data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error reading combined dataset data: {str(e)}")

@app.post("/api/detect-anomalies-combined/{combined_id}")
async def detect_anomalies_combined(combined_id: str, request: Dict[str, Any]):
    """
    Detect anomalies in a combined dataset.
    This is similar to the single dataset anomaly detection but works on combined data.
    """
    if combined_id not in COMBINED_DATASETS:
        raise HTTPException(status_code=404, detail=f"Combined dataset with ID {combined_id} not found")

    combined_dataset = COMBINED_DATASETS[combined_id]

    try:
        # Load the data
        with open(combined_dataset["json_path"], "r") as f:
            data = json.load(f)

        # Convert to DataFrame
        df = pd.DataFrame(data)

        # Get numerical columns
        numerical_columns = df.select_dtypes(include=['number']).columns.tolist()

        # Filter out metadata columns
        numerical_columns = [col for col in numerical_columns if not col.startswith('_')]

        # Get columns to analyze (either from request or all numerical columns)
        columns_to_analyze = request.get("columns", numerical_columns)

        # Filter to only include numerical columns
        columns_to_analyze = [col for col in columns_to_analyze if col in numerical_columns]

        if not columns_to_analyze:
            return {
                "message": "No numerical columns to analyze",
                "anomalies": []
            }

        # Set threshold (default to 3.0 for z-score)
        threshold = request.get("threshold", 3.0)

        # Dictionary to store anomalies by column
        anomalies_by_column = {}
        all_anomaly_indices = set()

        # Detect anomalies using z-score for each column
        for column in columns_to_analyze:
            # Calculate z-score
            mean = df[column].mean()
            std = df[column].std()

            if std == 0:  # Skip columns with zero standard deviation
                continue

            df[f'{column}_zscore'] = (df[column] - mean) / std

            # Find anomalies
            anomaly_indices = df[abs(df[f'{column}_zscore']) > threshold].index.tolist()

            if anomaly_indices:
                anomalies_by_column[column] = {
                    "indices": anomaly_indices,
                    "count": len(anomaly_indices),
                    "values": df.loc[anomaly_indices, column].tolist()
                }
                all_anomaly_indices.update(anomaly_indices)

        # Create anomaly records
        anomaly_records = []
        for idx in all_anomaly_indices:
            record = df.loc[idx].to_dict()
            # Remove z-score columns
            record = {k: v for k, v in record.items() if not k.endswith('_zscore')}
            anomaly_records.append(record)

        # Create result
        result_id = str(uuid.uuid4())
        result = {
            "id": result_id,
            "combined_dataset_id": combined_id,
            "total_rows": len(df),
            "anomaly_count": len(all_anomaly_indices),
            "anomaly_indices": sorted(list(all_anomaly_indices)),
            "anomalies_by_column": anomalies_by_column,
            "anomaly_records": anomaly_records,
            "anomaly_percentage": (len(all_anomaly_indices) / len(df)) * 100 if len(df) > 0 else 0,
            "created_at": datetime.now(timezone.utc).isoformat()
        }

        # Store the result
        ANOMALY_RESULTS[result_id] = result

        return result

    except Exception as e:
        logger.error(f"Error detecting anomalies in combined dataset: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error detecting anomalies in combined dataset: {str(e)}")

@app.post("/api/generate-report/{source_id}")
async def generate_report(source_id: str, request: Dict[str, Any]):
    """
    Generate a simple report (P&L or 3-year forecast) from the data source.
    This is a simplified version that returns mock data for the prototype.
    """
    # Find the data source
    data_source = None
    for source in DATA_SOURCES:
        if source["id"] == source_id:
            data_source = source
            break

    if not data_source:
        raise HTTPException(status_code=404, detail=f"Data source with ID {source_id} not found")

    report_type = request.get("type", "p_and_l")

    if report_type == "p_and_l":
        # Mock P&L report
        return {
            "report_type": "p_and_l",
            "data_source_id": source_id,
            "title": "Profit and Loss Statement",
            "period": "2023",
            "data": {
                "revenue": 1250000,
                "cost_of_goods_sold": 750000,
                "gross_profit": 500000,
                "operating_expenses": {
                    "salaries": 200000,
                    "rent": 50000,
                    "utilities": 25000,
                    "marketing": 75000,
                    "other": 50000
                },
                "total_operating_expenses": 400000,
                "operating_income": 100000,
                "other_income": 10000,
                "other_expenses": 5000,
                "income_before_tax": 105000,
                "tax": 21000,
                "net_income": 84000
            },
            "created_at": datetime.now(timezone.utc).isoformat()
        }
    elif report_type == "forecast":
        # Mock 3-year forecast
        return {
            "report_type": "forecast",
            "data_source_id": source_id,
            "title": "3-Year Financial Forecast",
            "years": ["2024", "2025", "2026"],
            "data": {
                "revenue": [1375000, 1512500, 1663750],
                "cost_of_goods_sold": [825000, 907500, 998250],
                "gross_profit": [550000, 605000, 665500],
                "operating_expenses": {
                    "salaries": [210000, 220500, 231525],
                    "rent": [51000, 52020, 53060],
                    "utilities": [26000, 27040, 28122],
                    "marketing": [82500, 90750, 99825],
                    "other": [52500, 55125, 57881]
                },
                "total_operating_expenses": [422000, 445435, 470413],
                "operating_income": [128000, 159565, 195087],
                "other_income": [11000, 12100, 13310],
                "other_expenses": [5500, 6050, 6655],
                "income_before_tax": [133500, 165615, 201742],
                "tax": [26700, 33123, 40348],
                "net_income": [106800, 132492, 161394]
            },
            "growth_assumptions": {
                "revenue": "10% annual growth",
                "costs": "10% annual growth",
                "expenses": "5% annual growth for salaries, 2% for rent and utilities, 10% for marketing"
            },
            "created_at": datetime.now(timezone.utc).isoformat()
        }
    else:
        raise HTTPException(status_code=400, detail=f"Unsupported report type: {report_type}")

@app.post("/api/analyze-schemas")
async def analyze_schemas(request: Dict[str, Any]):
    """Analyze schemas from multiple data sources and suggest mappings"""
    try:
        source_ids = request.get("source_ids", [])

        if not source_ids or len(source_ids) < 2:
            raise HTTPException(
                status_code=400,
                detail="At least two data source IDs are required for schema analysis"
            )

        # Find the data sources
        sources = []
        for source_id in source_ids:
            source = None
            for s in DATA_SOURCES:
                if s["id"] == source_id:
                    source = s
                    break

            if not source:
                raise HTTPException(
                    status_code=404,
                    detail=f"Data source with ID {source_id} not found"
                )

            sources.append(source)

        # Analyze each source schema
        source_schemas = []
        for i, source in enumerate(sources):
            # Load the data
            with open(source["json_path"], "r") as f:
                data = json.load(f)

            # Convert to DataFrame
            df = pd.DataFrame(data)

            # Analyze schema
            schema_info = analyze_dataset_schema(df)
            schema_info["source_id"] = source["id"]
            schema_info["source_name"] = source["name"]
            schema_info["source_index"] = i

            source_schemas.append(schema_info)

        # Suggest field mappings
        field_mappings = suggest_field_mappings(source_schemas)

        # Create audit log
        audit_log = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": "system",
            "action": "schema_analysis",
            "details": {
                "source_ids": source_ids,
                "mapping_count": len(field_mappings["mappings"]),
                "confidence": field_mappings["confidence"]
            }
        }
        AUDIT_LOGS.append(audit_log)

        # Convert numpy types to Python native types
        result = {
            "source_schemas": source_schemas,
            "field_mappings": field_mappings,
            "audit_log": audit_log
        }

        return convert_numpy_types(result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing schemas: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error analyzing schemas: {str(e)}")

@app.post("/api/create-field-mapping")
async def create_field_mapping(request: Dict[str, Any]):
    """Create or update field mappings between data sources"""
    try:
        source_ids = request.get("source_ids", [])
        mappings = request.get("mappings", [])

        if not source_ids or len(source_ids) < 2:
            raise HTTPException(
                status_code=400,
                detail="At least two data source IDs are required for field mapping"
            )

        if not mappings:
            raise HTTPException(
                status_code=400,
                detail="No field mappings provided"
            )

        # Generate a unique ID for this mapping set
        mapping_id = request.get("id", str(uuid.uuid4()))

        # Store the mappings
        FIELD_MAPPINGS[mapping_id] = {
            "id": mapping_id,
            "source_ids": source_ids,
            "mappings": mappings,
            "created_at": datetime.now(timezone.utc).isoformat(),
            "updated_at": datetime.now(timezone.utc).isoformat()
        }

        # Create audit log
        audit_log = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": "system",
            "action": "field_mapping_creation",
            "details": {
                "mapping_id": mapping_id,
                "source_ids": source_ids,
                "mapping_count": len(mappings)
            }
        }
        AUDIT_LOGS.append(audit_log)

        return {
            "message": "Field mappings created successfully",
            "mapping_id": mapping_id,
            "audit_log": audit_log
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating field mappings: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating field mappings: {str(e)}")

@app.post("/api/preview-combined-data")
async def preview_combined_data_endpoint(request: Dict[str, Any]):
    """Generate a preview of combined data based on field mappings"""
    try:
        source_ids = request.get("source_ids", [])
        mappings = request.get("mappings", [])
        limit = request.get("limit", 5)

        if not source_ids or len(source_ids) < 2:
            raise HTTPException(
                status_code=400,
                detail="At least two data source IDs are required for data preview"
            )

        if not mappings:
            raise HTTPException(
                status_code=400,
                detail="No field mappings provided"
            )

        # Find the data sources
        dataframes = []
        for source_id in source_ids:
            source = None
            for s in DATA_SOURCES:
                if s["id"] == source_id:
                    source = s
                    break

            if not source:
                raise HTTPException(
                    status_code=404,
                    detail=f"Data source with ID {source_id} not found"
                )

            # Load the data
            with open(source["json_path"], "r") as f:
                data = json.load(f)

            # Convert to DataFrame
            df = pd.DataFrame(data)
            dataframes.append(df)

        # Generate preview
        preview = preview_combined_data(dataframes, mappings, limit)

        # Convert numpy types to Python native types
        return convert_numpy_types(preview)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating data preview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating data preview: {str(e)}")

@app.post("/api/transform-column")
async def transform_column_endpoint(request: Dict[str, Any]):
    """Apply transformations to columns"""
    try:
        source_id = request.get("source_id")
        column = request.get("column")
        transformation = request.get("transformation", {})

        if not source_id:
            raise HTTPException(
                status_code=400,
                detail="Source ID is required"
            )

        if not column:
            raise HTTPException(
                status_code=400,
                detail="Column name is required"
            )

        if not transformation or "type" not in transformation:
            raise HTTPException(
                status_code=400,
                detail="Valid transformation configuration is required"
            )

        # Find the data source
        source = None
        for s in DATA_SOURCES:
            if s["id"] == source_id:
                source = s
                break

        if not source:
            raise HTTPException(
                status_code=404,
                detail=f"Data source with ID {source_id} not found"
            )

        # Load the data
        with open(source["json_path"], "r") as f:
            data = json.load(f)

        # Convert to DataFrame
        df = pd.DataFrame(data)

        # Check if column exists
        if column not in df.columns:
            raise HTTPException(
                status_code=400,
                detail=f"Column '{column}' not found in data source"
            )

        # Apply transformation
        transformed_df = transform_column(df, column, transformation)

        # Generate preview
        preview = transformed_df.head(5).replace({np.nan: None}).to_dict("records")

        # Get new column names
        new_columns = [col for col in transformed_df.columns if col not in df.columns]

        # Create audit log
        audit_log = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": "system",
            "action": "column_transformation",
            "details": {
                "source_id": source_id,
                "column": column,
                "transformation_type": transformation.get("type"),
                "new_columns": new_columns
            }
        }
        AUDIT_LOGS.append(audit_log)

        # Convert numpy types to Python native types
        result = {
            "preview": preview,
            "new_columns": new_columns,
            "transformation": transformation,
            "audit_log": audit_log
        }

        return convert_numpy_types(result)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error transforming column: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error transforming column: {str(e)}")

@app.post("/api/summarize-dataset/{dataset_id}")
async def summarize_dataset(dataset_id: str, request: Dict[str, Any]):
    """Generate a summary of a dataset (either individual or combined)"""
    # Check if it's a combined dataset
    is_combined = False
    dataset = None

    if dataset_id in COMBINED_DATASETS:
        is_combined = True
        dataset = COMBINED_DATASETS[dataset_id]
    else:
        # Check if it's an individual data source
        for s in DATA_SOURCES:
            if s["id"] == dataset_id:
                dataset = s
                break

    if not dataset:
        raise HTTPException(status_code=404, detail=f"Dataset with ID {dataset_id} not found")

    try:
        # Load the data
        with open(dataset["json_path"], "r") as f:
            data = json.load(f)
            df = pd.DataFrame(data)

        # Generate summary statistics
        summary = {
            "dataset_id": dataset_id,
            "dataset_name": dataset["name"],
            "is_combined": is_combined,
            "row_count": len(df),
            "column_count": len(df.columns),
            "numerical_columns": {},
            "categorical_columns": {},
            "date_columns": [],
            "top_correlations": [],
            "missing_values": {}
        }

        # Identify column types and generate statistics
        for col in df.columns:
            # Skip metadata columns
            if col.startswith("_"):
                continue

            # Check for missing values
            missing_count = df[col].isna().sum()
            if missing_count > 0:
                summary["missing_values"][col] = {
                    "count": int(missing_count),
                    "percentage": float((missing_count / len(df)) * 100)
                }

            # Try to infer if it's a date column
            try:
                pd.to_datetime(df[col])
                summary["date_columns"].append(col)
                continue
            except:
                pass

            # Check if it's a numerical column
            if pd.api.types.is_numeric_dtype(df[col]):
                # Calculate statistics for numerical columns
                summary["numerical_columns"][col] = {
                    "min": float(df[col].min()) if not pd.isna(df[col].min()) else None,
                    "max": float(df[col].max()) if not pd.isna(df[col].max()) else None,
                    "mean": float(df[col].mean()) if not pd.isna(df[col].mean()) else None,
                    "median": float(df[col].median()) if not pd.isna(df[col].median()) else None,
                    "std": float(df[col].std()) if not pd.isna(df[col].std()) else None,
                }
            else:
                # Calculate statistics for categorical columns
                value_counts = df[col].value_counts().head(5).to_dict()
                summary["categorical_columns"][col] = {
                    "unique_values": int(df[col].nunique()),
                    "top_values": {str(k): int(v) for k, v in value_counts.items()}
                }

        # Calculate correlations between numerical columns
        numerical_cols = list(summary["numerical_columns"].keys())
        if len(numerical_cols) >= 2:
            corr_matrix = df[numerical_cols].corr().abs()
            # Get the top 5 correlations (excluding self-correlations)
            correlations = []
            for i in range(len(numerical_cols)):
                for j in range(i+1, len(numerical_cols)):
                    col1 = numerical_cols[i]
                    col2 = numerical_cols[j]
                    corr_value = corr_matrix.iloc[i, j]
                    if not pd.isna(corr_value):
                        correlations.append({
                            "column1": col1,
                            "column2": col2,
                            "correlation": float(corr_value)
                        })

            # Sort by correlation value and take top 5
            correlations.sort(key=lambda x: x["correlation"], reverse=True)
            summary["top_correlations"] = correlations[:5]

        # For combined datasets, add source distribution
        if is_combined and "_source_name" in df.columns:
            source_counts = df["_source_name"].value_counts().to_dict()
            summary["source_distribution"] = {str(k): int(v) for k, v in source_counts.items()}

        return summary

    except Exception as e:
        logger.error(f"Error summarizing dataset: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error summarizing dataset: {str(e)}")

@app.post("/api/analyze-dataset/{dataset_id}")
async def analyze_dataset(dataset_id: str, request: Dict[str, Any]):
    """Perform advanced analysis on a dataset"""
    # Check if it's a combined dataset
    is_combined = False
    dataset = None

    if dataset_id in COMBINED_DATASETS:
        is_combined = True
        dataset = COMBINED_DATASETS[dataset_id]
    else:
        # Check if it's an individual data source
        for s in DATA_SOURCES:
            if s["id"] == dataset_id:
                dataset = s
                break

    if not dataset:
        raise HTTPException(status_code=404, detail=f"Dataset with ID {dataset_id} not found")

    # Get analysis type
    analysis_type = request.get("type", "general")
    columns = request.get("columns", [])

    try:
        # Load the data
        with open(dataset["json_path"], "r") as f:
            data = json.load(f)
            df = pd.DataFrame(data)

        # If no columns specified, use all non-metadata columns
        if not columns:
            columns = [col for col in df.columns if not col.startswith("_")]

        # Filter to only include existing columns
        columns = [col for col in columns if col in df.columns]

        if not columns:
            return {"message": "No valid columns specified for analysis"}

        # Perform the requested analysis
        if analysis_type == "general":
            return perform_general_analysis(df, columns, is_combined)
        elif analysis_type == "time_series":
            return {"message": "Time series analysis not implemented yet"}
        elif analysis_type == "group_by":
            return {"message": "Group by analysis not implemented yet"}
        elif analysis_type == "cross_source":
            if not is_combined:
                raise HTTPException(status_code=400, detail="Cross-source analysis is only available for combined datasets")
            return {"message": "Cross-source analysis not implemented yet"}
        else:
            raise HTTPException(status_code=400, detail=f"Unsupported analysis type: {analysis_type}")

    except Exception as e:
        logger.error(f"Error analyzing dataset: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error analyzing dataset: {str(e)}")

def perform_general_analysis(df, columns, is_combined):
    """Perform general analysis on the dataset"""
    result = {
        "column_stats": {},
        "outliers": {},
        "patterns": []
    }

    # Analyze each column
    for col in columns:
        # Skip non-numeric columns for certain operations
        if pd.api.types.is_numeric_dtype(df[col]):
            # Skip boolean columns for quartile calculations
            if df[col].dtype == 'bool':
                true_count = int(df[col].sum())
                result["column_stats"][col] = {
                    "type": "boolean",
                    "true_count": true_count,
                    "false_count": int(len(df) - true_count),
                    "true_percentage": float((true_count / len(df)) * 100)
                }
                continue

            # For numerical columns, calculate statistics and find outliers
            try:
                q1 = df[col].quantile(0.25)
                q3 = df[col].quantile(0.75)
                iqr = q3 - q1
                lower_bound = q1 - 1.5 * iqr
                upper_bound = q3 + 1.5 * iqr

                outliers = df[(df[col] < lower_bound) | (df[col] > upper_bound)]

                result["column_stats"][col] = {
                    "type": "numerical",
                    "quartiles": {
                        "q1": float(q1),
                        "median": float(df[col].median()),
                        "q3": float(q3)
                    },
                    "outlier_count": len(outliers)
                }

                if len(outliers) > 0:
                    result["outliers"][col] = {
                        "count": len(outliers),
                        "percentage": float((len(outliers) / len(df)) * 100),
                        "min_value": float(outliers[col].min()),
                        "max_value": float(outliers[col].max())
                    }
            except Exception as e:
                # If there's an error with this column, log it and continue
                logger.warning(f"Error analyzing column {col}: {str(e)}")
                result["column_stats"][col] = {
                    "type": "error",
                    "error": str(e)
                }
        else:
            # For categorical columns, find unusual patterns
            try:
                value_counts = df[col].value_counts(normalize=True)
                rare_values = value_counts[value_counts < 0.05].index.tolist()

                result["column_stats"][col] = {
                    "type": "categorical",
                    "unique_count": int(df[col].nunique()),
                    "top_value": str(value_counts.index[0]) if len(value_counts) > 0 else None,
                    "top_value_percentage": float(value_counts.iloc[0] * 100) if len(value_counts) > 0 else 0,
                    "rare_value_count": len(rare_values)
                }
            except Exception as e:
                # If there's an error with this column, log it and continue
                logger.warning(f"Error analyzing column {col}: {str(e)}")
                result["column_stats"][col] = {
                    "type": "error",
                    "error": str(e)
                }

    # Look for patterns across columns
    numerical_cols = [col for col in columns if pd.api.types.is_numeric_dtype(df[col]) and df[col].dtype != 'bool']

    # Check for correlations
    if len(numerical_cols) >= 2:
        try:
            corr_matrix = df[numerical_cols].corr()

            # Find strong correlations (positive or negative)
            for i in range(len(numerical_cols)):
                for j in range(i+1, len(numerical_cols)):
                    col1 = numerical_cols[i]
                    col2 = numerical_cols[j]
                    corr_value = corr_matrix.iloc[i, j]

                    if pd.notna(corr_value) and abs(corr_value) > 0.7:  # Strong correlation threshold
                        result["patterns"].append({
                            "type": "correlation",
                            "columns": [col1, col2],
                            "correlation": float(corr_value),
                            "description": f"Strong {'positive' if corr_value > 0 else 'negative'} correlation between {col1} and {col2}"
                        })
        except Exception as e:
            logger.warning(f"Error calculating correlations: {str(e)}")

    # For combined datasets, look for patterns across sources
    if is_combined and "_source_name" in df.columns:
        for col in numerical_cols:
            try:
                # Check if the column values differ significantly between sources
                source_stats = df.groupby("_source_name")[col].agg(['mean', 'std']).to_dict()

                means = source_stats['mean']
                if len(means) >= 2:
                    overall_mean = df[col].mean()
                    if overall_mean == 0:  # Avoid division by zero
                        continue

                    source_means = list(means.values())

                    # Check if any source mean differs significantly from overall mean
                    for source, mean in means.items():
                        if abs(mean - overall_mean) > abs(overall_mean * 0.5):  # 50% difference threshold
                            result["patterns"].append({
                                "type": "source_difference",
                                "column": col,
                                "source": str(source),
                                "source_mean": float(mean),
                                "overall_mean": float(overall_mean),
                                "difference_percentage": float(abs(mean - overall_mean) / abs(overall_mean) * 100),
                                "description": f"Values for {col} in source '{source}' differ significantly from overall average"
                            })
            except Exception as e:
                logger.warning(f"Error analyzing source differences for column {col}: {str(e)}")

    return result

@app.post("/api/infer-schema")
async def infer_schema(request: SchemaInferenceRequest):
    """Infer the schema of a data source using AI"""
    try:
        source_id = request.source_id
        override_existing = request.override_existing

        # Find the data source
        data_source = None
        for source in DATA_SOURCES:
            if source["id"] == source_id:
                data_source = source
                break

        if not data_source:
            raise HTTPException(status_code=404, detail=f"Data source with ID {source_id} not found")

        # Check if we already have field mappings for this source
        if source_id in FIELD_MAPPINGS and not override_existing:
            return {"message": "Schema already inferred", "field_mappings": FIELD_MAPPINGS[source_id]}

        # Load the data
        with open(data_source["json_path"], "r") as f:
            data = json.load(f)

        # Convert to DataFrame
        df = pd.DataFrame(data)

        # Get sample rows for AI analysis
        sample_rows = df.head(10).to_dict(orient="records")
        column_headers = list(df.columns)

        # Perform AI schema detection
        field_mappings = ai_schema_detection(column_headers, sample_rows)

        # Store the field mappings
        FIELD_MAPPINGS[source_id] = field_mappings

        # Create audit log
        audit_log = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": "system",
            "action": "schema_inference",
            "details": {
                "source_id": source_id,
                "field_mappings": field_mappings
            }
        }
        AUDIT_LOGS.append(audit_log)

        return {"message": "Schema inferred successfully", "field_mappings": field_mappings}
    except Exception as e:
        logger.error(f"Error inferring schema: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error inferring schema: {str(e)}")

@app.post("/api/normalize-data")
async def normalize_data(request: NormalizeDataRequest):
    """Normalize data from multiple sources based on field mappings"""
    try:
        source_ids = request.source_ids
        field_mappings_override = request.field_mappings

        # Validate source IDs
        sources = []
        for source_id in source_ids:
            source = None
            for s in DATA_SOURCES:
                if s["id"] == source_id:
                    source = s
                    break

            if not source:
                raise HTTPException(status_code=404, detail=f"Data source with ID {source_id} not found")

            sources.append(source)

            # Ensure we have field mappings for this source
            if source_id not in FIELD_MAPPINGS and not field_mappings_override:
                # Try to infer schema first
                await infer_schema(SchemaInferenceRequest(source_id=source_id))

        # Use provided field mappings if available
        if field_mappings_override:
            for source_id, mappings in field_mappings_override.items():
                FIELD_MAPPINGS[source_id] = mappings

                # Create audit log for override
                audit_log = {
                    "id": str(uuid.uuid4()),
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "user_id": "system",
                    "action": "field_mapping_override",
                    "details": {
                        "source_id": source_id,
                        "field_mappings": mappings
                    }
                }
                AUDIT_LOGS.append(audit_log)

        # Process each source and normalize the data
        normalized_rows = []

        for source in sources:
            source_id = source["id"]

            # Load the data
            with open(source["json_path"], "r") as f:
                data = json.load(f)

            # Get field mappings for this source
            mappings = FIELD_MAPPINGS.get(source_id, [])

            # Process each row
            for row in data:
                normalized_row = normalize_row(row, mappings, source_id)
                if normalized_row:
                    normalized_rows.append(normalized_row)

        # Generate a unique ID for the normalized dataset
        normalized_id = str(uuid.uuid4())

        # Store the normalized data
        NORMALIZED_DATA[normalized_id] = {
            "id": normalized_id,
            "source_ids": source_ids,
            "rows": normalized_rows,
            "created_at": datetime.now(timezone.utc).isoformat()
        }

        # Create audit log
        audit_log = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": "system",
            "action": "data_normalization",
            "details": {
                "normalized_id": normalized_id,
                "source_ids": source_ids,
                "row_count": len(normalized_rows)
            }
        }
        AUDIT_LOGS.append(audit_log)

        return {
            "message": "Data normalized successfully",
            "normalized_id": normalized_id,
            "row_count": len(normalized_rows)
        }
    except Exception as e:
        logger.error(f"Error normalizing data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error normalizing data: {str(e)}")

@app.post("/api/generate-pnl")
async def generate_pnl(request: GeneratePnLRequest):
    """Generate a P&L report from normalized data"""
    try:
        name = request.name
        source_ids = request.source_ids
        start_date = request.start_date
        end_date = request.end_date

        # Normalize the data first if not already done
        # Check if we have normalized data for these sources
        normalized_id = None
        for nid, data in NORMALIZED_DATA.items():
            if set(data["source_ids"]) == set(source_ids):
                normalized_id = nid
                break

        if not normalized_id:
            # Normalize the data
            normalize_result = await normalize_data(NormalizeDataRequest(source_ids=source_ids))
            normalized_id = normalize_result["normalized_id"]

        # Get the normalized data
        normalized_data = NORMALIZED_DATA[normalized_id]
        rows = normalized_data["rows"]

        # Filter by date range if provided
        if start_date or end_date:
            filtered_rows = []
            for row in rows:
                row_date = datetime.fromisoformat(row["transaction_date"].replace('Z', '+00:00'))

                if start_date:
                    start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                    if row_date < start:
                        continue

                if end_date:
                    end = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                    if row_date > end:
                        continue

                filtered_rows.append(row)

            rows = filtered_rows

        # Generate the P&L report
        pnl_report = generate_pnl_report(rows, name)

        # Store the report
        PNL_REPORTS[pnl_report["id"]] = pnl_report

        # Create audit log
        audit_log = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": "system",
            "action": "pnl_generation",
            "details": {
                "report_id": pnl_report["id"],
                "name": name,
                "source_ids": source_ids,
                "start_date": start_date,
                "end_date": end_date
            }
        }
        AUDIT_LOGS.append(audit_log)

        return pnl_report
    except Exception as e:
        logger.error(f"Error generating P&L report: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating P&L report: {str(e)}")

@app.post("/api/field-mapping-override")
async def override_field_mapping(request: FieldMappingOverrideRequest):
    """Override field mappings for a data source"""
    try:
        source_id = request.source_id
        field_mappings = request.field_mappings

        # Find the data source
        data_source = None
        for source in DATA_SOURCES:
            if source["id"] == source_id:
                data_source = source
                break

        if not data_source:
            raise HTTPException(status_code=404, detail=f"Data source with ID {source_id} not found")

        # Update the field mappings
        FIELD_MAPPINGS[source_id] = field_mappings

        # Create audit log
        audit_log = {
            "id": str(uuid.uuid4()),
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": "system",
            "action": "field_mapping_override",
            "details": {
                "source_id": source_id,
                "field_mappings": field_mappings
            }
        }
        AUDIT_LOGS.append(audit_log)

        return {"message": "Field mappings updated successfully", "field_mappings": field_mappings}
    except Exception as e:
        logger.error(f"Error overriding field mappings: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error overriding field mappings: {str(e)}")

@app.get("/api/audit-logs")
async def get_audit_logs(source_id: Optional[str] = None):
    """Get audit logs, optionally filtered by source ID"""
    if source_id:
        return [log for log in AUDIT_LOGS if log.get("details", {}).get("source_id") == source_id]
    return AUDIT_LOGS

@app.get("/api/pnl-reports")
async def get_pnl_reports(source_id: Optional[str] = None):
    """Get P&L reports, optionally filtered by source ID"""
    if source_id:
        return {k: v for k, v in PNL_REPORTS.items() if source_id in v["source_ids"]}
    return PNL_REPORTS

@app.get("/api/pnl-reports/{report_id}")
async def get_pnl_report(report_id: str):
    """Get a specific P&L report"""
    if report_id in PNL_REPORTS:
        return PNL_REPORTS[report_id]
    raise HTTPException(status_code=404, detail=f"P&L report with ID {report_id} not found")

# Helper functions for AI schema detection and data normalization
def ai_schema_detection(column_headers, sample_rows):
    """Use AI to detect schema and map fields to standardized names with financial domain knowledge"""
    # In a real implementation, this would call an LLM API with a prompt like:
    # "You are a financial data assistant. Given these column headers and sample rows,
    # identify the semantic meaning of each column, whether it represents income or expense,
    # and suggest standardized field names for financial analysis."

    # For this prototype, we'll use an enhanced rule-based approach with financial domain knowledge

    field_mappings = []
    financial_categories = {
        # Income categories
        'income': ['income', 'revenue', 'sales', 'salary', 'wage', 'commission', 'bonus', 'interest', 'dividend',
                  'royalty', 'rent', 'grant', 'refund', 'reimbursement', 'deposit', 'credit', 'inflow'],
        # Expense categories
        'expense': ['expense', 'cost', 'payment', 'fee', 'charge', 'bill', 'invoice', 'purchase', 'subscription',
                   'insurance', 'tax', 'rent', 'mortgage', 'loan', 'debt', 'withdrawal', 'debit', 'outflow']
    }

    # Common financial transaction categories
    transaction_categories = [
        'salary', 'bonus', 'interest', 'dividend', 'rent', 'utilities', 'groceries', 'dining', 'entertainment',
        'transportation', 'travel', 'healthcare', 'insurance', 'education', 'shopping', 'gifts', 'donations',
        'taxes', 'investments', 'savings', 'loan', 'mortgage', 'subscription', 'services', 'miscellaneous'
    ]

    # Analyze all columns together first to understand the dataset context
    dataset_context = analyze_dataset_context(column_headers, sample_rows)

    # Process each column with the dataset context in mind
    for column in column_headers:
        column_lower = column.lower()

        # Get sample values for this column
        sample_values = [str(row.get(column, "")) for row in sample_rows if column in row]
        sample_values = [v for v in sample_values if v]

        # Analyze the column values
        value_analysis = analyze_column_values(sample_values)

        # Determine semantic type and standardized name with confidence scoring
        mapping = determine_column_mapping(column, column_lower, sample_values, value_analysis, dataset_context, financial_categories, transaction_categories)
        field_mappings.append(mapping)

    # Post-process mappings to ensure consistency and resolve conflicts
    field_mappings = post_process_mappings(field_mappings, dataset_context)

    return field_mappings

def analyze_dataset_context(column_headers, sample_rows):
    """Analyze the overall dataset to determine its context and structure"""
    context = {
        "likely_source_type": "unknown",
        "has_transaction_date": False,
        "has_amount": False,
        "has_category": False,
        "has_description": False,
        "date_format": None,
        "currency_symbol": None,
        "amount_columns": [],
        "date_columns": []
    }

    # Check for common financial document types based on column patterns
    headers_lower = [h.lower() for h in column_headers]

    # Bank statement indicators
    if any(h in headers_lower for h in ['balance', 'withdrawal', 'deposit']) or \
       (any('date' in h for h in headers_lower) and any('amount' in h for h in headers_lower)):
        context["likely_source_type"] = "bank_statement"

    # Credit card statement indicators
    elif any(h in headers_lower for h in ['card', 'credit', 'charge', 'transaction']) or \
         (any('date' in h for h in headers_lower) and any('amount' in h for h in headers_lower)):
        context["likely_source_type"] = "credit_card_statement"

    # Invoice or receipt indicators
    elif any(h in headers_lower for h in ['invoice', 'receipt', 'item', 'quantity', 'price', 'total']):
        context["likely_source_type"] = "invoice_or_receipt"

    # Expense report indicators
    elif any(h in headers_lower for h in ['expense', 'reimbursement', 'claim']):
        context["likely_source_type"] = "expense_report"

    # Income statement indicators
    elif any(h in headers_lower for h in ['revenue', 'income', 'profit', 'loss', 'earnings']):
        context["likely_source_type"] = "income_statement"

    # Detect date format from sample values
    for column in column_headers:
        sample_values = [str(row.get(column, "")) for row in sample_rows if column in row and row.get(column, "")]

        # Check for date patterns
        date_formats = detect_date_format(sample_values)
        if date_formats:
            context["has_transaction_date"] = True
            context["date_format"] = date_formats
            context["date_columns"].append(column)

        # Check for currency symbols
        currency_symbol = detect_currency_symbol(sample_values)
        if currency_symbol and not context["currency_symbol"]:
            context["currency_symbol"] = currency_symbol

        # Check for amount columns
        if is_likely_amount_column(sample_values):
            context["has_amount"] = True
            context["amount_columns"].append(column)

    return context

def detect_date_format(values):
    """Detect the date format from a list of values"""
    date_formats = []

    # Common date patterns
    patterns = [
        (r'\d{4}-\d{2}-\d{2}', 'YYYY-MM-DD'),  # ISO format
        (r'\d{2}/\d{2}/\d{4}', 'MM/DD/YYYY'),   # US format
        (r'\d{2}/\d{2}/\d{2}', 'MM/DD/YY'),     # US short format
        (r'\d{2}-\d{2}-\d{4}', 'MM-DD-YYYY'),   # US with hyphens
        (r'\d{2}\.\d{2}\.\d{4}', 'DD.MM.YYYY'), # European format
        (r'\d{1,2}\s+[A-Za-z]{3}\s+\d{4}', 'D MMM YYYY')  # 1 Jan 2023
    ]

    for value in values[:5]:  # Check first few values
        for pattern, format_name in patterns:
            if re.match(pattern, value):
                if format_name not in date_formats:
                    date_formats.append(format_name)

    return date_formats

def detect_currency_symbol(values):
    """Detect currency symbols in values"""
    currency_symbols = {'$': 'USD', '€': 'EUR', '£': 'GBP', '¥': 'JPY', '₹': 'INR'}

    for value in values:
        for symbol in currency_symbols:
            if symbol in value:
                return symbol

    return None

def is_likely_amount_column(values):
    """Check if a column is likely to contain monetary amounts"""
    # Remove currency symbols, commas, and spaces
    cleaned_values = []
    for v in values:
        if not v:
            continue
        cleaned = v.replace('$', '').replace('€', '').replace('£', '').replace('¥', '').replace('₹', '')
        cleaned = cleaned.replace(',', '').replace(' ', '')
        cleaned_values.append(cleaned)

    # Check if values match numeric pattern
    numeric_pattern = r'^-?\d+(\.\d+)?$'
    numeric_count = sum(1 for v in cleaned_values if re.match(numeric_pattern, v))

    # If more than 70% of non-empty values are numeric, it's likely an amount column
    return numeric_count >= len(cleaned_values) * 0.7 if cleaned_values else False

def analyze_column_values(sample_values):
    """Analyze column values to determine their characteristics"""
    analysis = {
        "is_numeric": False,
        "is_date": False,
        "is_categorical": False,
        "is_text": False,
        "unique_ratio": 0,
        "has_negatives": False,
        "common_prefixes": [],
        "common_suffixes": [],
        "avg_length": 0,
        "patterns": []
    }

    if not sample_values:
        return analysis

    # Check if values are numeric
    numeric_values = []
    for v in sample_values:
        try:
            # Remove currency symbols, commas, and spaces
            cleaned = v.replace('$', '').replace('€', '').replace('£', '').replace('¥', '').replace('₹', '')
            cleaned = cleaned.replace(',', '').replace(' ', '')
            num = float(cleaned)
            numeric_values.append(num)
        except:
            pass

    analysis["is_numeric"] = len(numeric_values) >= len(sample_values) * 0.7

    if analysis["is_numeric"] and numeric_values:
        analysis["has_negatives"] = any(n < 0 for n in numeric_values)

    # Check if values are dates
    date_formats = detect_date_format(sample_values)
    analysis["is_date"] = bool(date_formats)

    # Check for categorical data (low cardinality)
    unique_values = set(sample_values)
    analysis["unique_ratio"] = len(unique_values) / len(sample_values) if sample_values else 0
    analysis["is_categorical"] = 0 < analysis["unique_ratio"] < 0.3  # Less than 30% unique values

    # Check for text data
    if not analysis["is_numeric"] and not analysis["is_date"]:
        avg_length = sum(len(v) for v in sample_values) / len(sample_values) if sample_values else 0
        analysis["avg_length"] = avg_length
        analysis["is_text"] = avg_length > 5  # Arbitrary threshold

    # Find common prefixes and suffixes
    if sample_values and len(sample_values) > 1:
        # Get common prefixes
        for i in range(1, min(5, min(len(v) for v in sample_values) + 1)):
            prefix = sample_values[0][:i]
            if all(v.startswith(prefix) for v in sample_values):
                analysis["common_prefixes"].append(prefix)

        # Get common suffixes
        for i in range(1, min(5, min(len(v) for v in sample_values) + 1)):
            suffix = sample_values[0][-i:]
            if all(v.endswith(suffix) for v in sample_values):
                analysis["common_suffixes"].append(suffix)

    return analysis

def determine_column_mapping(column, column_lower, sample_values, value_analysis, dataset_context, financial_categories, transaction_categories):
    """Determine the mapping for a column based on its name, values, and dataset context"""
    mapping = {
        "source_field": column,
        "target_field": column,  # Default to keeping original name
        "semantic_type": "unknown",
        "is_income": None,
        "confidence": 0.5,  # Default confidence
        "reasoning": [],
        "sample_values": sample_values[:5] if sample_values else []  # Include up to 5 sample values
    }

    # Check for amount fields
    amount_indicators = ["amount", "sum", "total", "price", "cost", "value", "payment", "fee", "charge", "balance"]
    if any(indicator in column_lower for indicator in amount_indicators) or \
       (value_analysis["is_numeric"] and column in dataset_context["amount_columns"]):
        mapping["target_field"] = "amount"
        mapping["semantic_type"] = "amount"
        mapping["confidence"] = 0.9
        mapping["reasoning"].append(f"Column name '{column}' suggests monetary amount")

        # Try to determine if it's income or expense
        is_income = None

        # Check column name for income/expense indicators
        for category, keywords in financial_categories.items():
            if any(keyword in column_lower for keyword in keywords):
                is_income = (category == 'income')
                mapping["reasoning"].append(f"Column name indicates {category}")
                break

        # If we couldn't determine from the column name, check sample values
        if is_income is None and value_analysis["is_numeric"] and sample_values:
            # Check if values are mostly positive or negative
            try:
                numeric_values = []
                for v in sample_values:
                    cleaned = v.replace('$', '').replace('€', '').replace('£', '').replace('¥', '').replace('₹', '')
                    cleaned = cleaned.replace(',', '').replace(' ', '')
                    numeric_values.append(float(cleaned))

                if numeric_values:
                    positive_count = sum(1 for v in numeric_values if v > 0)
                    negative_count = sum(1 for v in numeric_values if v < 0)

                    # Consider the document type for interpretation
                    if dataset_context["likely_source_type"] in ["bank_statement", "credit_card_statement"]:
                        # For bank/credit statements, positive often means deposit/credit (income)
                        if positive_count > negative_count:
                            is_income = True
                            mapping["reasoning"].append("Mostly positive values in a bank/credit statement suggest income")
                        elif negative_count > positive_count:
                            is_income = False
                            mapping["reasoning"].append("Mostly negative values in a bank/credit statement suggest expenses")
                    else:
                        # For other documents, positive values are usually the norm for both income and expenses
                        # We'll need additional context
                        pass
            except:
                pass

        mapping["is_income"] = is_income

    # Check for date fields
    date_indicators = ["date", "time", "day", "month", "year", "when"]
    if any(indicator in column_lower for indicator in date_indicators) or \
       value_analysis["is_date"] or column in dataset_context["date_columns"]:
        mapping["target_field"] = "transaction_date"
        mapping["semantic_type"] = "date"
        mapping["confidence"] = 0.9
        mapping["reasoning"].append(f"Column contains date values")

    # Check for category fields
    category_indicators = ["category", "type", "class", "classification", "group"]
    if any(indicator in column_lower for indicator in category_indicators) or \
       (value_analysis["is_categorical"] and not value_analysis["is_numeric"] and not value_analysis["is_date"]):
        mapping["target_field"] = "category"
        mapping["semantic_type"] = "category"
        mapping["confidence"] = 0.8
        mapping["reasoning"].append(f"Column appears to contain categorical values")

        # Check if categories match known financial categories
        if sample_values:
            matches = 0
            for value in sample_values:
                value_lower = value.lower()
                if any(category in value_lower for category in transaction_categories):
                    matches += 1

            if matches / len(sample_values) > 0.3:  # If more than 30% match known categories
                mapping["confidence"] = 0.9
                mapping["reasoning"].append("Contains known financial transaction categories")

    # Check for description fields
    description_indicators = ["description", "desc", "note", "details", "memo", "comment", "narrative", "reference"]
    if any(indicator in column_lower for indicator in description_indicators) or \
       (value_analysis["is_text"] and value_analysis["avg_length"] > 10):
        mapping["target_field"] = "description"
        mapping["semantic_type"] = "description"
        mapping["confidence"] = 0.8
        mapping["reasoning"].append(f"Column contains descriptive text")

    # Check for account/payee fields
    account_indicators = ["account", "payee", "vendor", "merchant", "recipient", "sender", "customer", "client"]
    if any(indicator in column_lower for indicator in account_indicators):
        mapping["target_field"] = "account_or_payee"
        mapping["semantic_type"] = "account_or_payee"
        mapping["confidence"] = 0.8
        mapping["reasoning"].append(f"Column appears to contain account or payee information")

    # If we still don't know, try to infer from sample values
    if mapping["semantic_type"] == "unknown" and sample_values:
        # Check if it might be a date
        if value_analysis["is_date"]:
            mapping["target_field"] = "transaction_date"
            mapping["semantic_type"] = "date"
            mapping["confidence"] = 0.7
            mapping["reasoning"].append("Values match date patterns")

        # Check if it might be an amount
        elif value_analysis["is_numeric"]:
            mapping["target_field"] = "amount"
            mapping["semantic_type"] = "amount"
            mapping["confidence"] = 0.7
            mapping["reasoning"].append("Values are numeric and could represent amounts")

    return mapping

def post_process_mappings(field_mappings, dataset_context):
    """Post-process mappings to ensure consistency and resolve conflicts"""
    # Count semantic types
    semantic_counts = {}
    for mapping in field_mappings:
        semantic_type = mapping["semantic_type"]
        if semantic_type not in semantic_counts:
            semantic_counts[semantic_type] = 0
        semantic_counts[semantic_type] += 1

    # If we have multiple date or amount fields, adjust confidence based on context
    if semantic_counts.get("date", 0) > 1:
        date_mappings = [m for m in field_mappings if m["semantic_type"] == "date"]
        # Sort by confidence
        date_mappings.sort(key=lambda m: m["confidence"], reverse=True)

        # Keep the highest confidence one as transaction_date
        for i, mapping in enumerate(date_mappings):
            if i == 0:  # Highest confidence
                mapping["target_field"] = "transaction_date"
                mapping["confidence"] = max(mapping["confidence"], 0.9)  # Boost confidence
                mapping["reasoning"].append("Selected as primary transaction date")
            else:  # Secondary date fields
                # Check if it might be a posting date, due date, etc.
                field_lower = mapping["source_field"].lower()
                if "post" in field_lower or "settle" in field_lower:
                    mapping["target_field"] = "posting_date"
                    mapping["reasoning"].append("Appears to be a posting/settlement date")
                elif "due" in field_lower:
                    mapping["target_field"] = "due_date"
                    mapping["reasoning"].append("Appears to be a due date")
                else:
                    mapping["target_field"] = f"secondary_date_{i}"
                    mapping["confidence"] = max(mapping["confidence"] - 0.1, 0.5)  # Reduce confidence
                    mapping["reasoning"].append("Secondary date field with unclear purpose")

    # If we have multiple amount fields, try to determine which is primary
    if semantic_counts.get("amount", 0) > 1:
        amount_mappings = [m for m in field_mappings if m["semantic_type"] == "amount"]

        # Check for specific amount types
        for mapping in amount_mappings:
            field_lower = mapping["source_field"].lower()

            if any(word in field_lower for word in ["total", "net", "final"]):
                mapping["target_field"] = "total_amount"
                mapping["confidence"] = max(mapping["confidence"], 0.9)
                mapping["reasoning"].append("Appears to be a total amount")
            elif any(word in field_lower for word in ["tax", "vat", "gst"]):
                mapping["target_field"] = "tax_amount"
                mapping["confidence"] = max(mapping["confidence"], 0.9)
                mapping["reasoning"].append("Appears to be a tax amount")
            elif any(word in field_lower for word in ["fee", "charge", "commission"]):
                mapping["target_field"] = "fee_amount"
                mapping["confidence"] = max(mapping["confidence"], 0.9)
                mapping["reasoning"].append("Appears to be a fee or charge amount")
            elif any(word in field_lower for word in ["discount", "rebate", "credit"]):
                mapping["target_field"] = "discount_amount"
                mapping["confidence"] = max(mapping["confidence"], 0.9)
                mapping["reasoning"].append("Appears to be a discount amount")
            elif any(word in field_lower for word in ["subtotal", "sub"]):
                mapping["target_field"] = "subtotal_amount"
                mapping["confidence"] = max(mapping["confidence"], 0.9)
                mapping["reasoning"].append("Appears to be a subtotal amount")

    # Remove the reasoning field before returning (it was just for internal use)
    for mapping in field_mappings:
        if "reasoning" in mapping:
            del mapping["reasoning"]

    return field_mappings

def normalize_row(row, field_mappings, source_id):
    """Normalize a single row based on field mappings with enhanced financial data handling"""
    try:
        # Initialize normalized row with default values
        normalized = {
            "id": str(uuid.uuid4()),
            "source_id": source_id,
            "transaction_date": None,
            "amount": None,
            "category": None,
            "description": None,
            "account_or_payee": None,
            "is_income": None,
            "original_data": row,
            "confidence": 0.0,  # Track overall confidence in normalization
            "additional_fields": {}  # Store any additional fields
        }

        # First pass: extract all mapped fields
        extracted_values = {}
        for mapping in field_mappings:
            source_field = mapping["source_field"]
            target_field = mapping["target_field"]
            semantic_type = mapping["semantic_type"]
            confidence = mapping.get("confidence", 0.5)

            if source_field not in row:
                continue

            value = row[source_field]

            # Store the extracted value and its metadata
            extracted_values[target_field] = {
                "value": value,
                "semantic_type": semantic_type,
                "confidence": confidence,
                "mapping": mapping
            }

        # Second pass: process and normalize the extracted values
        # Process date fields
        date_fields = {k: v for k, v in extracted_values.items()
                      if v["semantic_type"] == "date" and k.endswith("_date")}

        if date_fields:
            # Sort by confidence to get the most reliable date field
            sorted_date_fields = sorted(date_fields.items(), key=lambda x: x[1]["confidence"], reverse=True)
            primary_date_field = sorted_date_fields[0][0]
            primary_date_value = sorted_date_fields[0][1]["value"]

            # Normalize the date format
            normalized_date = normalize_date(primary_date_value)
            if normalized_date:
                normalized["transaction_date"] = normalized_date
                normalized["confidence"] += sorted_date_fields[0][1]["confidence"]

            # Store additional date fields
            for field_name, field_data in sorted_date_fields[1:]:
                normalized_date = normalize_date(field_data["value"])
                if normalized_date:
                    normalized["additional_fields"][field_name] = normalized_date

        # Process amount fields
        amount_fields = {k: v for k, v in extracted_values.items()
                        if v["semantic_type"] == "amount" and (k == "amount" or k.endswith("_amount"))}

        if amount_fields:
            # Sort by confidence to get the most reliable amount field
            sorted_amount_fields = sorted(amount_fields.items(), key=lambda x: x[1]["confidence"], reverse=True)
            primary_amount_field = sorted_amount_fields[0][0]
            primary_amount_data = sorted_amount_fields[0][1]

            # Normalize the amount
            normalized_amount = normalize_amount(primary_amount_data["value"])
            if normalized_amount is not None:
                normalized["amount"] = normalized_amount
                normalized["confidence"] += primary_amount_data["confidence"]

                # Determine if it's income or expense
                mapping = primary_amount_data["mapping"]
                if mapping.get("is_income") is not None:
                    normalized["is_income"] = mapping["is_income"]
                else:
                    # Positive is income, negative is expense by default
                    normalized["is_income"] = normalized_amount >= 0

            # Store additional amount fields
            for field_name, field_data in sorted_amount_fields[1:]:
                normalized_amount = normalize_amount(field_data["value"])
                if normalized_amount is not None:
                    normalized["additional_fields"][field_name] = normalized_amount

        # Process category field
        category_fields = {k: v for k, v in extracted_values.items()
                          if v["semantic_type"] == "category"}

        if category_fields:
            # Sort by confidence to get the most reliable category field
            sorted_category_fields = sorted(category_fields.items(), key=lambda x: x[1]["confidence"], reverse=True)
            primary_category_field = sorted_category_fields[0][0]
            primary_category_data = sorted_category_fields[0][1]

            # Normalize the category
            normalized_category = normalize_category(primary_category_data["value"])
            if normalized_category:
                normalized["category"] = normalized_category
                normalized["confidence"] += primary_category_data["confidence"]

        # Process description field
        description_fields = {k: v for k, v in extracted_values.items()
                             if v["semantic_type"] == "description"}

        if description_fields:
            # Sort by confidence to get the most reliable description field
            sorted_desc_fields = sorted(description_fields.items(), key=lambda x: x[1]["confidence"], reverse=True)
            primary_desc_field = sorted_desc_fields[0][0]
            primary_desc_data = sorted_desc_fields[0][1]

            # Normalize the description
            normalized_desc = normalize_description(primary_desc_data["value"])
            if normalized_desc:
                normalized["description"] = normalized_desc
                normalized["confidence"] += primary_desc_data["confidence"]

        # Process account/payee field
        account_fields = {k: v for k, v in extracted_values.items()
                         if v["semantic_type"] == "account_or_payee"}

        if account_fields:
            # Sort by confidence to get the most reliable account field
            sorted_account_fields = sorted(account_fields.items(), key=lambda x: x[1]["confidence"], reverse=True)
            primary_account_field = sorted_account_fields[0][0]
            primary_account_data = sorted_account_fields[0][1]

            # Normalize the account/payee
            normalized_account = str(primary_account_data["value"]).strip()
            if normalized_account:
                normalized["account_or_payee"] = normalized_account
                normalized["confidence"] += primary_account_data["confidence"]

        # If we don't have a category but have a description, try to infer category from description
        if not normalized["category"] and normalized["description"]:
            inferred_category = infer_category_from_description(normalized["description"])
            if inferred_category:
                normalized["category"] = inferred_category
                normalized["additional_fields"]["category_source"] = "inferred_from_description"
                normalized["confidence"] += 0.5  # Lower confidence for inferred categories

        # Normalize confidence to a 0-1 scale
        field_count = sum(1 for f in ["transaction_date", "amount", "category", "description", "account_or_payee"]
                         if normalized[f] is not None)
        if field_count > 0:
            normalized["confidence"] = min(normalized["confidence"] / field_count, 1.0)

        # Validate that we have the required fields
        if normalized["transaction_date"] is None or normalized["amount"] is None:
            return None

        return normalized
    except Exception as e:
        logger.warning(f"Error normalizing row: {str(e)}")
        return None

def normalize_date(date_value):
    """Normalize date values to ISO format (YYYY-MM-DD)"""
    if not date_value:
        return None

    # If already a datetime object, format it
    if isinstance(date_value, (datetime.date, datetime.datetime)):
        return date_value.strftime("%Y-%m-%d")

    # Convert to string if not already
    date_str = str(date_value).strip()

    # Try various date formats
    date_formats = [
        "%Y-%m-%d",       # ISO format: 2023-01-31
        "%m/%d/%Y",       # US format: 01/31/2023
        "%d/%m/%Y",       # UK format: 31/01/2023
        "%m-%d-%Y",       # US with hyphens: 01-31-2023
        "%d-%m-%Y",       # UK with hyphens: 31-01-2023
        "%d.%m.%Y",       # European format: 31.01.2023
        "%m/%d/%y",       # US short year: 01/31/23
        "%d/%m/%y",       # UK short year: 31/01/23
        "%b %d, %Y",      # Jan 31, 2023
        "%d %b %Y",       # 31 Jan 2023
        "%B %d, %Y",      # January 31, 2023
        "%d %B %Y"        # 31 January 2023
    ]

    for fmt in date_formats:
        try:
            parsed_date = datetime.datetime.strptime(date_str, fmt)
            return parsed_date.strftime("%Y-%m-%d")
        except ValueError:
            continue

    # If all formats fail, try a more flexible approach with dateutil
    try:
        from dateutil import parser
        parsed_date = parser.parse(date_str)
        return parsed_date.strftime("%Y-%m-%d")
    except:
        # If dateutil is not available or fails, return the original string
        return date_str

def normalize_amount(amount_value):
    """Normalize monetary amounts to float values"""
    if amount_value is None:
        return None

    # If already a number, return it
    if isinstance(amount_value, (int, float)):
        return float(amount_value)

    # Convert to string and clean it
    amount_str = str(amount_value).strip()

    # Remove currency symbols and other non-numeric characters
    currency_symbols = ['$', '€', '£', '¥', '₹', '₽', '₩', '₴', '₦', '₱', '฿', '₲', '₡', '₫']
    for symbol in currency_symbols:
        amount_str = amount_str.replace(symbol, '')

    # Handle parentheses for negative numbers: (100) -> -100
    if amount_str.startswith('(') and amount_str.endswith(')'):
        amount_str = '-' + amount_str[1:-1]

    # Remove thousands separators and other formatting
    amount_str = amount_str.replace(',', '').replace(' ', '')

    # Handle different decimal separators
    if '.' in amount_str:
        # Assume period is decimal separator
        pass
    elif ',' in amount_str:
        # Comma might be decimal separator in some locales
        amount_str = amount_str.replace(',', '.')

    try:
        return float(amount_str)
    except ValueError:
        return None

def normalize_category(category_value):
    """Normalize transaction categories to standard values"""
    if not category_value:
        return None

    category_str = str(category_value).strip()

    # Map of common variations to standardized categories
    category_mapping = {
        # Income categories
        'salary': ['salary', 'wages', 'payroll', 'income', 'earnings'],
        'interest': ['interest', 'dividend', 'investment income'],
        'gift': ['gift', 'present', 'donation received'],
        'refund': ['refund', 'reimbursement', 'cashback'],
        'rental_income': ['rent', 'rental', 'lease income'],

        # Expense categories
        'housing': ['rent', 'mortgage', 'lease payment', 'housing'],
        'utilities': ['utility', 'electric', 'water', 'gas', 'internet', 'phone', 'cable'],
        'groceries': ['grocery', 'supermarket', 'food store'],
        'dining': ['restaurant', 'cafe', 'dining', 'fast food', 'takeout'],
        'transportation': ['gas', 'fuel', 'auto', 'car', 'vehicle', 'transport', 'uber', 'lyft', 'taxi'],
        'travel': ['travel', 'hotel', 'flight', 'airfare', 'vacation'],
        'shopping': ['shopping', 'retail', 'store', 'amazon', 'online purchase'],
        'entertainment': ['entertainment', 'movie', 'music', 'concert', 'subscription'],
        'health': ['medical', 'healthcare', 'doctor', 'pharmacy', 'prescription', 'health'],
        'education': ['education', 'tuition', 'school', 'college', 'university', 'course', 'book'],
        'personal_care': ['personal care', 'haircut', 'salon', 'spa', 'beauty'],
        'insurance': ['insurance', 'premium'],
        'taxes': ['tax', 'irs', 'government fee'],
        'debt_payment': ['loan payment', 'credit card payment', 'debt'],
        'charity': ['donation', 'charity', 'gift given'],
        'business': ['business', 'office', 'professional', 'work expense']
    }

    # Convert to lowercase for matching
    category_lower = category_str.lower()

    # Try to match with standard categories
    for standard_category, variations in category_mapping.items():
        if any(variation in category_lower for variation in variations):
            return standard_category

    # If no match found, return the original but capitalized nicely
    return category_str.title()

def normalize_description(description_value):
    """Normalize transaction descriptions"""
    if not description_value:
        return None

    description_str = str(description_value).strip()

    # Remove common prefixes like "POS PURCHASE" or "DEBIT CARD PURCHASE"
    common_prefixes = [
        "POS PURCHASE", "DEBIT CARD PURCHASE", "CREDIT CARD PURCHASE",
        "ACH PAYMENT", "ACH DEPOSIT", "ATM WITHDRAWAL", "ATM DEPOSIT",
        "ONLINE PAYMENT", "ONLINE TRANSFER", "MOBILE PAYMENT", "MOBILE DEPOSIT",
        "CHECK PAYMENT", "CHECK DEPOSIT", "WIRE TRANSFER", "DIRECT DEPOSIT"
    ]

    for prefix in common_prefixes:
        if description_str.upper().startswith(prefix):
            description_str = description_str[len(prefix):].strip()

    # Remove transaction IDs and reference numbers
    # This is a simplified approach - in a real implementation, you'd use more sophisticated regex
    ref_patterns = [
        r'\b[A-Z0-9]{6,}\b',  # Alphanumeric reference codes
        r'\bREF\s*[A-Z0-9]+\b',  # References starting with REF
        r'\bID:\s*[A-Z0-9]+\b'   # IDs
    ]

    for pattern in ref_patterns:
        description_str = re.sub(pattern, '', description_str)

    # Clean up extra whitespace
    description_str = re.sub(r'\s+', ' ', description_str).strip()

    return description_str

def infer_category_from_description(description):
    """Infer transaction category from description text"""
    if not description:
        return None

    description_lower = description.lower()

    # Common keywords that indicate specific categories
    category_keywords = {
        'groceries': ['grocery', 'supermarket', 'food', 'market', 'walmart', 'target', 'kroger', 'safeway', 'aldi', 'trader joe'],
        'dining': ['restaurant', 'cafe', 'coffee', 'starbucks', 'mcdonald', 'burger', 'pizza', 'taco', 'sushi', 'dining'],
        'transportation': ['gas', 'fuel', 'shell', 'exxon', 'chevron', 'uber', 'lyft', 'taxi', 'transit', 'parking', 'auto', 'car'],
        'utilities': ['electric', 'water', 'gas', 'utility', 'phone', 'mobile', 'internet', 'cable', 'netflix', 'spotify'],
        'housing': ['rent', 'mortgage', 'property', 'apartment', 'home', 'lease', 'housing'],
        'shopping': ['amazon', 'ebay', 'etsy', 'shop', 'store', 'retail', 'clothing', 'electronics'],
        'entertainment': ['movie', 'theater', 'cinema', 'concert', 'ticket', 'event', 'game', 'subscription'],
        'health': ['doctor', 'medical', 'pharmacy', 'hospital', 'clinic', 'dental', 'health', 'fitness', 'gym'],
        'travel': ['hotel', 'airbnb', 'airline', 'flight', 'travel', 'vacation', 'booking', 'expedia', 'trip'],
        'education': ['school', 'college', 'university', 'tuition', 'course', 'class', 'education', 'book', 'student'],
        'income': ['salary', 'payroll', 'deposit', 'income', 'revenue', 'payment received', 'direct deposit'],
        'transfer': ['transfer', 'zelle', 'venmo', 'paypal', 'cash app', 'wire', 'ach']
    }

    # Check for matches
    for category, keywords in category_keywords.items():
        if any(keyword in description_lower for keyword in keywords):
            return category

    return None

def generate_pnl_report(normalized_rows, name):
    """Generate a P&L report from normalized data"""
    try:
        # Group transactions by month
        monthly_data = {}
        category_data = {}

        # Find date range
        dates = [datetime.fromisoformat(row["transaction_date"].replace('Z', '+00:00')) for row in normalized_rows if row["transaction_date"]]
        period_start = min(dates).isoformat() if dates else None
        period_end = max(dates).isoformat() if dates else None

        # Process each row
        for row in normalized_rows:
            try:
                # Get the month
                date = datetime.fromisoformat(row["transaction_date"].replace('Z', '+00:00'))
                month_key = date.strftime("%Y-%m")

                # Initialize month data if needed
                if month_key not in monthly_data:
                    monthly_data[month_key] = {
                        "income": 0,
                        "expenses": 0,
                        "net": 0,
                        "transactions": []
                    }

                # Add transaction to the month
                amount = row["amount"]
                is_income = row["is_income"]

                if is_income:
                    monthly_data[month_key]["income"] += amount
                else:
                    monthly_data[month_key]["expenses"] += abs(amount)

                monthly_data[month_key]["net"] = monthly_data[month_key]["income"] - monthly_data[month_key]["expenses"]
                monthly_data[month_key]["transactions"].append(row["id"])

                # Process by category
                category = row.get("category", "Uncategorized")

                if category not in category_data:
                    category_data[category] = {
                        "income": 0,
                        "expenses": 0,
                        "net": 0,
                        "transactions": []
                    }

                if is_income:
                    category_data[category]["income"] += amount
                else:
                    category_data[category]["expenses"] += abs(amount)

                category_data[category]["net"] = category_data[category]["income"] - category_data[category]["expenses"]
                category_data[category]["transactions"].append(row["id"])
            except Exception as e:
                logger.warning(f"Error processing row for P&L: {str(e)}")
                continue

        # Create the report
        report_id = str(uuid.uuid4())
        report = {
            "id": report_id,
            "name": name,
            "source_ids": list(set(row["source_id"] for row in normalized_rows)),
            "period_start": period_start,
            "period_end": period_end,
            "monthly_breakdown": monthly_data,
            "category_breakdown": category_data,
            "created_at": datetime.now(timezone.utc).isoformat()
        }

        return report
    except Exception as e:
        logger.error(f"Error generating P&L report: {str(e)}")
        raise

# Semantic Mapping API Endpoints

class AnalyzeSchemasRequest(BaseModel):
    source_ids: List[str]

class TransformColumnRequest(BaseModel):
    source_id: str
    column_name: str
    transformation: Dict[str, Any]

class PreviewCombinedDataRequest(BaseModel):
    source_ids: List[str]
    field_mappings: List[Dict[str, Any]]
    limit: int = 5
    selected_fields_only: bool = True

class CreateFieldMappingRequest(BaseModel):
    source_ids: List[str]
    field_mappings: List[Dict[str, Any]]

@app.post("/api/analyze-schemas")
async def analyze_schemas(request: AnalyzeSchemasRequest):
    """Analyze schemas from multiple data sources and suggest field mappings"""
    try:
        source_ids = request.source_ids

        # Validate source IDs
        sources = []
        for i, source_id in enumerate(source_ids):
            source = None
            for s in DATA_SOURCES:
                if s["id"] == source_id:
                    source = s
                    break

            if not source:
                raise HTTPException(status_code=404, detail=f"Data source with ID {source_id} not found")

            sources.append(source)

        # Load data for each source
        dataframes = []
        source_schemas = []

        for i, source in enumerate(sources):
            # Load the data
            with open(source["json_path"], "r") as f:
                data = json.load(f)

            # Convert to DataFrame
            df = pd.DataFrame(data)
            dataframes.append(df)

            # Analyze schema
            schema_info = analyze_dataset_schema(df)

            # Add source information
            schema_info["source_id"] = source["id"]
            schema_info["source_name"] = source["name"]
            schema_info["source_index"] = i

            source_schemas.append(schema_info)

        # Suggest field mappings
        field_mappings = suggest_field_mappings(source_schemas)

        return {
            "source_schemas": source_schemas,
            "field_mappings": field_mappings
        }
    except Exception as e:
        logger.error(f"Error analyzing schemas: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error analyzing schemas: {str(e)}")

@app.post("/api/transform-column")
async def transform_column_endpoint(request: TransformColumnRequest):
    """Apply a transformation to a column"""
    try:
        source_id = request.source_id
        column_name = request.column_name
        transformation = request.transformation

        # Find the data source
        source = None
        for s in DATA_SOURCES:
            if s["id"] == source_id:
                source = s
                break

        if not source:
            raise HTTPException(status_code=404, detail=f"Data source with ID {source_id} not found")

        # Load the data
        with open(source["json_path"], "r") as f:
            data = json.load(f)

        # Convert to DataFrame
        df = pd.DataFrame(data)

        # Check if column exists
        if column_name not in df.columns:
            raise HTTPException(status_code=400, detail=f"Column {column_name} not found in data source")

        # Apply transformation
        result_df = transform_column(df, column_name, transformation)

        # Get new columns
        new_columns = [col for col in result_df.columns if col not in df.columns]

        # Generate preview
        preview = result_df.head(5).replace({np.nan: None}).to_dict("records")

        return {
            "preview": preview,
            "new_columns": new_columns
        }
    except Exception as e:
        logger.error(f"Error transforming column: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error transforming column: {str(e)}")

@app.post("/api/preview-combined-data")
async def preview_combined_data_endpoint(request: PreviewCombinedDataRequest):
    """Generate a preview of combined data based on field mappings"""
    try:
        source_ids = request.source_ids
        field_mappings = request.field_mappings
        limit = request.limit
        selected_fields_only = request.selected_fields_only

        # Validate source IDs
        sources = []
        for source_id in source_ids:
            source = None
            for s in DATA_SOURCES:
                if s["id"] == source_id:
                    source = s
                    break

            if not source:
                raise HTTPException(status_code=404, detail=f"Data source with ID {source_id} not found")

            sources.append(source)

        # Load data for each source
        dataframes = []

        for source in sources:
            # Load the data
            with open(source["json_path"], "r") as f:
                data = json.load(f)

            # Convert to DataFrame
            df = pd.DataFrame(data)
            dataframes.append(df)

        # Generate preview
        preview_result = preview_combined_data(dataframes, field_mappings, limit, selected_fields_only)

        return preview_result
    except Exception as e:
        logger.error(f"Error generating preview: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating preview: {str(e)}")

@app.post("/api/create-field-mapping")
async def create_field_mapping(request: CreateFieldMappingRequest):
    """Create a field mapping between data sources"""
    try:
        source_ids = request.source_ids
        field_mappings = request.field_mappings

        # Validate source IDs
        for source_id in source_ids:
            source = None
            for s in DATA_SOURCES:
                if s["id"] == source_id:
                    source = s
                    break

            if not source:
                raise HTTPException(status_code=404, detail=f"Data source with ID {source_id} not found")

        # Generate a unique ID for this mapping
        mapping_id = str(uuid.uuid4())

        # Store the mapping
        FIELD_MAPPINGS[mapping_id] = {
            "id": mapping_id,
            "source_ids": source_ids,
            "field_mappings": field_mappings,
            "created_at": datetime.now(timezone.utc).isoformat()
        }

        return {
            "mapping_id": mapping_id
        }
    except Exception as e:
        logger.error(f"Error creating field mapping: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating field mapping: {str(e)}")

@app.get("/api/field-mappings/{mapping_id}")
async def get_field_mapping(mapping_id: str):
    """Get a field mapping by ID"""
    if mapping_id not in FIELD_MAPPINGS:
        raise HTTPException(status_code=404, detail=f"Field mapping with ID {mapping_id} not found")

    return FIELD_MAPPINGS[mapping_id]

if __name__ == "__main__":
    logger.info("Starting Excel Data Processor API...")
    uvicorn.run(app, host="0.0.0.0", port=8002)
