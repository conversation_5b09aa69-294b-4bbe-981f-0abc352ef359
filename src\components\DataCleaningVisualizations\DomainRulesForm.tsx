import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Trash2 } from 'lucide-react';

interface DomainRule {
  id: string;
  column: string;
  condition: string;
  value: string;
}

interface DomainRulesFormProps {
  columns: string[];
  rules: DomainRule[];
  onChange: (rules: DomainRule[]) => void;
  onApply: () => void;
  isLoading?: boolean;
}

const DomainRulesForm: React.FC<DomainRulesFormProps> = ({
  columns,
  rules,
  onChange,
  onApply,
  isLoading = false
}) => {
  const addRule = () => {
    const newRule: DomainRule = {
      id: `rule-${Date.now()}`,
      column: columns[0] || '',
      condition: 'greater_than',
      value: ''
    };
    onChange([...rules, newRule]);
  };

  const updateRule = (id: string, field: keyof DomainRule, value: string) => {
    const updatedRules = rules.map(rule => 
      rule.id === id ? { ...rule, [field]: value } : rule
    );
    onChange(updatedRules);
  };

  const removeRule = (id: string) => {
    const updatedRules = rules.filter(rule => rule.id !== id);
    onChange(updatedRules);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Domain-Specific Rules</CardTitle>
        <CardDescription>
          Define custom rules to identify anomalies based on domain knowledge
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {rules.map(rule => (
            <div key={rule.id} className="flex items-end gap-2">
              <div className="flex-1">
                <Label htmlFor={`column-${rule.id}`}>Column</Label>
                <Select 
                  value={rule.column} 
                  onValueChange={(value) => updateRule(rule.id, 'column', value)}
                >
                  <SelectTrigger id={`column-${rule.id}`}>
                    <SelectValue placeholder="Select column" />
                  </SelectTrigger>
                  <SelectContent>
                    {columns.map(column => (
                      <SelectItem key={column} value={column}>{column}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1">
                <Label htmlFor={`condition-${rule.id}`}>Condition</Label>
                <Select 
                  value={rule.condition} 
                  onValueChange={(value) => updateRule(rule.id, 'condition', value)}
                >
                  <SelectTrigger id={`condition-${rule.id}`}>
                    <SelectValue placeholder="Select condition" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="greater_than">Greater than</SelectItem>
                    <SelectItem value="less_than">Less than</SelectItem>
                    <SelectItem value="equal_to">Equal to</SelectItem>
                    <SelectItem value="not_equal_to">Not equal to</SelectItem>
                    <SelectItem value="contains">Contains</SelectItem>
                    <SelectItem value="not_contains">Does not contain</SelectItem>
                    <SelectItem value="is_null">Is null</SelectItem>
                    <SelectItem value="is_not_null">Is not null</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1">
                <Label htmlFor={`value-${rule.id}`}>Value</Label>
                <Input
                  id={`value-${rule.id}`}
                  value={rule.value}
                  onChange={(e) => updateRule(rule.id, 'value', e.target.value)}
                  disabled={rule.condition === 'is_null' || rule.condition === 'is_not_null'}
                />
              </div>
              
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => removeRule(rule.id)}
                className="mb-0.5"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))}
          
          <div className="flex justify-between">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={addRule}
              disabled={isLoading}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Rule
            </Button>
            
            <Button 
              onClick={onApply}
              disabled={isLoading || rules.length === 0}
              className="bg-teal hover:bg-teal/90"
            >
              Apply Rules
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DomainRulesForm;
