import os
import logging
from dotenv import load_dotenv
from app.db.session import SessionLocal
from app.models.data_source import DataSource
from app.models.anomaly_detection import AnomalyDetectionJob

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

def check_database():
    """Check the database for data sources and jobs."""
    db = SessionLocal()
    try:
        # Check data sources
        data_sources = db.query(DataSource).all()
        logger.info(f"Found {len(data_sources)} data sources:")
        for ds in data_sources:
            logger.info(f"  - ID: {ds.id}, Name: {ds.name}, Type: {ds.source_type}, Records: {ds.record_count}")
            logger.info(f"    Location: {ds.location}")
            logger.info(f"    Schema: {ds.schema}")
            logger.info(f"    Created at: {ds.created_at}")
        
        # Check anomaly detection jobs
        jobs = db.query(AnomalyDetectionJob).all()
        logger.info(f"Found {len(jobs)} anomaly detection jobs:")
        for job in jobs:
            logger.info(f"  - ID: {job.id}, Status: {job.status}, Progress: {job.progress}")
            logger.info(f"    Data Source ID: {job.data_source_id}, Pipeline ID: {job.pipeline_id}")
            logger.info(f"    Created at: {job.created_at}, Updated at: {job.updated_at}")
            if job.completed_at:
                logger.info(f"    Completed at: {job.completed_at}")
            if job.result:
                logger.info(f"    Result: {job.result}")
    finally:
        db.close()

if __name__ == "__main__":
    check_database()
