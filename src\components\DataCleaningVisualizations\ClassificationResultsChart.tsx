import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  <PERSON><PERSON><PERSON>, Pie, Cell, Tooltip, Legend, ResponsiveContainer,
  BarChart, Bar, XAxis, YAxis, CartesianGrid,
  RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar
} from 'recharts';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertCircle, Search, MessageSquare, Tag } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface ClassificationResultsProps {
  totalRows: number;
  processedRows: number;
  classificationType: string;
  classifications: any[];
  distribution: Record<string, number>;
  confidence?: Record<string, number>;
}

const ClassificationResultsChart: React.FC<ClassificationResultsProps> = ({
  totalRows,
  processedRows,
  classificationType,
  classifications,
  distribution,
  confidence = {}
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItem, setSelectedItem] = useState<any | null>(null);

  // Transform data for charts
  const distributionData = Object.entries(distribution)
    .map(([label, value]) => ({ 
      name: label, 
      value,
      confidence: confidence[label] || 0
    }))
    .sort((a, b) => b.value - a.value);

  // Filter classifications based on search term
  const filteredClassifications = classifications.filter(item => {
    if (!searchTerm) return true;
    
    // Search in all string fields
    return Object.entries(item).some(([key, value]) => {
      if (typeof value === 'string') {
        return value.toLowerCase().includes(searchTerm.toLowerCase());
      }
      return false;
    });
  });

  // Colors
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A28BFF', 
                 '#FF6B6B', '#4ECDC4', '#FFA69E', '#95D5B2', '#C1A7FF'];

  // Get icon based on classification type
  const getClassificationIcon = () => {
    switch (classificationType) {
      case 'category':
        return <Tag className="h-5 w-5" />;
      case 'entity':
        return <Search className="h-5 w-5" />;
      case 'sentiment':
        return <MessageSquare className="h-5 w-5" />;
      default:
        return <AlertCircle className="h-5 w-5" />;
    }
  };

  // Get title based on classification type
  const getClassificationTitle = () => {
    switch (classificationType) {
      case 'category':
        return 'Category Classification';
      case 'entity':
        return 'Entity Extraction';
      case 'sentiment':
        return 'Sentiment Analysis';
      case 'custom':
        return 'Custom Classification';
      default:
        return 'Classification Results';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center gap-2">
          <div className="bg-blue-100 p-2 rounded-full">
            {getClassificationIcon()}
          </div>
          <div>
            <CardTitle>{getClassificationTitle()}</CardTitle>
            <CardDescription>
              {classificationType === 'category' && 'Text categorized into predefined classes'}
              {classificationType === 'entity' && 'Entities extracted from text'}
              {classificationType === 'sentiment' && 'Sentiment analysis of text content'}
              {classificationType === 'custom' && 'Custom classification of text content'}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="distribution">
          <TabsList className="mb-4">
            <TabsTrigger value="distribution">Distribution</TabsTrigger>
            <TabsTrigger value="confidence">Confidence</TabsTrigger>
            <TabsTrigger value="examples">Examples</TabsTrigger>
          </TabsList>
          
          <TabsContent value="distribution" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Total Records</p>
                <p className="text-2xl font-bold">{totalRows.toLocaleString()}</p>
              </div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Processed Records</p>
                <p className="text-2xl font-bold">{processedRows.toLocaleString()}</p>
              </div>
              <div className="bg-slate-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">Coverage</p>
                <p className="text-2xl font-bold">
                  {totalRows > 0 ? ((processedRows / totalRows) * 100).toFixed(1) : 0}%
                </p>
              </div>
            </div>
            
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium mb-2">Distribution</h3>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={distributionData}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                      >
                        {distributionData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip 
                        formatter={(value: number) => [`${value.toFixed(1)}%`, 'Percentage']}
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
              
              <div>
                <h3 className="text-lg font-medium mb-2">Top Categories</h3>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={distributionData.slice(0, 5)}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" domain={[0, 100]} tickFormatter={(value) => `${value}%`} />
                      <YAxis dataKey="name" type="category" width={120} />
                      <Tooltip formatter={(value: number) => [`${value.toFixed(1)}%`, 'Percentage']} />
                      <Bar dataKey="value" fill="#8884d8" radius={[0, 4, 4, 0]}>
                        {distributionData.slice(0, 5).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="confidence">
            {Object.keys(confidence).length > 0 ? (
              <div className="space-y-6">
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart cx="50%" cy="50%" outerRadius="80%" data={distributionData}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="name" />
                      <PolarRadiusAxis angle={30} domain={[0, 1]} tickFormatter={(value) => `${(value * 100).toFixed(0)}%`} />
                      <Radar
                        name="Confidence"
                        dataKey="confidence"
                        stroke="#8884d8"
                        fill="#8884d8"
                        fillOpacity={0.6}
                      />
                      <Tooltip formatter={(value: number) => [`${(value * 100).toFixed(1)}%`, 'Confidence']} />
                      <Legend />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
                
                <div className="space-y-4">
                  {distributionData.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium">{item.name}</h4>
                        <div className="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                          <div 
                            className="bg-blue-600 h-2.5 rounded-full" 
                            style={{ width: `${item.confidence * 100}%` }}
                          ></div>
                        </div>
                      </div>
                      <Badge variant="outline" className="ml-4 text-sm whitespace-nowrap">
                        {(item.confidence * 100).toFixed(1)}% confidence
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <AlertCircle className="h-12 w-12 text-amber-500 mb-4" />
                <h3 className="text-lg font-medium">No Confidence Data Available</h3>
                <p className="text-gray-500 mt-2">
                  Confidence scores are not available for this classification.
                </p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="examples">
            {classifications.length > 0 ? (
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Input
                    placeholder="Search examples..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="max-w-sm"
                  />
                  <Button 
                    variant="outline"
                    onClick={() => setSearchTerm('')}
                    disabled={!searchTerm}
                  >
                    Clear
                  </Button>
                </div>
                
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="bg-slate-100">
                        <th className="p-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Text
                        </th>
                        {classificationType === 'category' && (
                          <>
                            <th className="p-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Category
                            </th>
                            <th className="p-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Confidence
                            </th>
                          </>
                        )}
                        {classificationType === 'sentiment' && (
                          <>
                            <th className="p-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Sentiment
                            </th>
                            <th className="p-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                              Confidence
                            </th>
                          </>
                        )}
                        <th className="p-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredClassifications.slice(0, 10).map((item, index) => (
                        <tr key={index} className={index % 2 === 0 ? 'bg-white' : 'bg-slate-50'}>
                          <td className="p-2 text-sm">
                            {item.text ? (
                              item.text.length > 100 ? `${item.text.substring(0, 100)}...` : item.text
                            ) : 'N/A'}
                          </td>
                          {classificationType === 'category' && (
                            <>
                              <td className="p-2 text-sm">
                                <Badge>{item.category}</Badge>
                              </td>
                              <td className="p-2 text-sm">
                                {item.confidence ? `${(item.confidence * 100).toFixed(1)}%` : 'N/A'}
                              </td>
                            </>
                          )}
                          {classificationType === 'sentiment' && (
                            <>
                              <td className="p-2 text-sm">
                                <Badge 
                                  className={
                                    item.sentiment === 'positive' ? 'bg-green-100 text-green-800' :
                                    item.sentiment === 'negative' ? 'bg-red-100 text-red-800' :
                                    'bg-gray-100 text-gray-800'
                                  }
                                >
                                  {item.sentiment}
                                </Badge>
                              </td>
                              <td className="p-2 text-sm">
                                {item.confidence ? `${(item.confidence * 100).toFixed(1)}%` : 'N/A'}
                              </td>
                            </>
                          )}
                          <td className="p-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => setSelectedItem(item)}
                            >
                              Details
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                {filteredClassifications.length > 10 && (
                  <p className="text-sm text-gray-500 text-center">
                    Showing 10 of {filteredClassifications.length} results
                  </p>
                )}
                
                {selectedItem && (
                  <div className="mt-4 p-4 border rounded-lg bg-slate-50">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-lg font-medium">Classification Details</h3>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => setSelectedItem(null)}
                      >
                        Close
                      </Button>
                    </div>
                    <div className="space-y-4">
                      <div>
                        <p className="text-sm text-gray-500">Text</p>
                        <p className="font-medium">{selectedItem.text}</p>
                      </div>
                      
                      {classificationType === 'category' && (
                        <>
                          <div>
                            <p className="text-sm text-gray-500">Category</p>
                            <p className="font-medium">{selectedItem.category}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Confidence</p>
                            <p className="font-medium">
                              {selectedItem.confidence ? `${(selectedItem.confidence * 100).toFixed(1)}%` : 'N/A'}
                            </p>
                          </div>
                          {selectedItem.reasoning && (
                            <div>
                              <p className="text-sm text-gray-500">Reasoning</p>
                              <p className="font-medium">{selectedItem.reasoning}</p>
                            </div>
                          )}
                        </>
                      )}
                      
                      {classificationType === 'entity' && (
                        <div>
                          <p className="text-sm text-gray-500">Extracted Entities</p>
                          <div className="space-y-2 mt-2">
                            {Object.entries(selectedItem.entities || {}).map(([entityType, entities]) => (
                              <div key={entityType}>
                                <p className="text-sm font-medium">{entityType}</p>
                                <div className="flex flex-wrap gap-2 mt-1">
                                  {Array.isArray(entities) ? (
                                    entities.map((entity: string, i: number) => (
                                      <Badge key={i} variant="outline">{entity}</Badge>
                                    ))
                                  ) : (
                                    <p className="text-sm">No entities found</p>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      
                      {classificationType === 'sentiment' && (
                        <>
                          <div>
                            <p className="text-sm text-gray-500">Sentiment</p>
                            <p className="font-medium">{selectedItem.sentiment}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Confidence</p>
                            <p className="font-medium">
                              {selectedItem.confidence ? `${(selectedItem.confidence * 100).toFixed(1)}%` : 'N/A'}
                            </p>
                          </div>
                          {selectedItem.reasoning && (
                            <div>
                              <p className="text-sm text-gray-500">Reasoning</p>
                              <p className="font-medium">{selectedItem.reasoning}</p>
                            </div>
                          )}
                        </>
                      )}
                      
                      {classificationType === 'custom' && (
                        <div>
                          <p className="text-sm text-gray-500">Custom Classification Result</p>
                          <pre className="bg-gray-100 p-2 rounded-md text-sm mt-1 overflow-x-auto">
                            {JSON.stringify(selectedItem.result, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <AlertCircle className="h-12 w-12 text-amber-500 mb-4" />
                <h3 className="text-lg font-medium">No Examples Available</h3>
                <p className="text-gray-500 mt-2">
                  Classification examples are not available for preview.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ClassificationResultsChart;
