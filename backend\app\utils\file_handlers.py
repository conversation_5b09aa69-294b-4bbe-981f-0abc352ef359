import csv
import io
import json
import logging
import os
from typing import Any, Dict, List, Optional, Union

import pandas as pd
from fastapi import UploadFile

logger = logging.getLogger(__name__)


async def process_csv_file(file: UploadFile) -> pd.DataFrame:
    """
    Process a CSV file and return a pandas DataFrame.
    
    Args:
        file: Uploaded CSV file
        
    Returns:
        DataFrame containing the CSV data
    """
    contents = await file.read()
    file.file.seek(0)  # Reset file pointer for potential reuse
    
    try:
        # Try to detect encoding and delimiter
        df = pd.read_csv(io.BytesIO(contents), encoding='utf-8')
    except UnicodeDecodeError:
        # Try with a different encoding if UTF-8 fails
        df = pd.read_csv(io.BytesIO(contents), encoding='latin1')
    except Exception as e:
        logger.error(f"Error processing CSV file: {e}")
        raise ValueError(f"Invalid CSV file: {str(e)}")
    
    return df


async def process_excel_file(file: UploadFile) -> Dict[str, pd.DataFrame]:
    """
    Process an Excel file and return a dictionary of pandas DataFrames.
    
    Args:
        file: Uploaded Excel file
        
    Returns:
        Dictionary mapping sheet names to DataFrames
    """
    contents = await file.read()
    file.file.seek(0)  # Reset file pointer for potential reuse
    
    try:
        # Read all sheets
        excel_file = pd.ExcelFile(io.BytesIO(contents))
        dfs = {}
        
        for sheet_name in excel_file.sheet_names:
            dfs[sheet_name] = pd.read_excel(excel_file, sheet_name=sheet_name)
        
        return dfs
    except Exception as e:
        logger.error(f"Error processing Excel file: {e}")
        raise ValueError(f"Invalid Excel file: {str(e)}")


async def process_json_file(file: UploadFile) -> Union[Dict, List]:
    """
    Process a JSON file and return the parsed data.
    
    Args:
        file: Uploaded JSON file
        
    Returns:
        Parsed JSON data as a dictionary or list
    """
    contents = await file.read()
    file.file.seek(0)  # Reset file pointer for potential reuse
    
    try:
        data = json.loads(contents)
        return data
    except Exception as e:
        logger.error(f"Error processing JSON file: {e}")
        raise ValueError(f"Invalid JSON file: {str(e)}")


def detect_file_type(filename: str) -> str:
    """
    Detect the file type based on the file extension.
    
    Args:
        filename: Name of the file
        
    Returns:
        File type as a string ("csv", "excel", "json", or "unknown")
    """
    ext = os.path.splitext(filename)[1].lower()
    
    if ext == '.csv':
        return "csv"
    elif ext in ['.xls', '.xlsx', '.xlsm']:
        return "excel"
    elif ext == '.json':
        return "json"
    else:
        return "unknown"


def dataframe_to_dict_list(df: pd.DataFrame) -> List[Dict[str, Any]]:
    """
    Convert a pandas DataFrame to a list of dictionaries.
    
    Args:
        df: DataFrame to convert
        
    Returns:
        List of dictionaries, one per row
    """
    return df.replace({pd.NA: None}).to_dict(orient='records')
