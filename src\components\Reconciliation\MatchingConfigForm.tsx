import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { 
  matchingConfigSchema, 
  MatchingConfigFormValues, 
  defaultMatchingConfig,
  formValuesToApiRequest
} from './validationSchema';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form } from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Loader2 } from 'lucide-react';
import matchingService from '@/services/matchingService';

// Import form sections (we'll create these next)
import DatasetSelection from './FormSections/DatasetSelection';
import MethodSelection from './FormSections/MethodSelection';
import FieldMappings from './FormSections/FieldMappings';
import ThresholdSettings from './FormSections/ThresholdSettings';
import AdvancedSettings from './FormSections/AdvancedSettings';

interface MatchingConfigFormProps {
  onSubmit: (data: ReturnType<typeof formValuesToApiRequest>) => void;
  isLoading?: boolean;
  error?: string | null;
}

const MatchingConfigForm: React.FC<MatchingConfigFormProps> = ({
  onSubmit,
  isLoading = false,
  error = null
}) => {
  const [activeTab, setActiveTab] = useState('datasets');
  const [datasets, setDatasets] = useState<{ id: string; name: string; fields: string[] }[]>([]);
  const [methods, setMethods] = useState<{ id: string; name: string; description: string }[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [dataError, setDataError] = useState<string | null>(null);

  // Initialize form with react-hook-form and zod validation
  const form = useForm<MatchingConfigFormValues>({
    resolver: zodResolver(matchingConfigSchema),
    defaultValues: defaultMatchingConfig,
    mode: 'onChange'
  });

  // Watch form values to enable/disable certain fields based on selected methods
  const watchMethods = form.watch('methods');
  const watchSourceDataset = form.watch('sourceDatasetId');
  const watchTargetDataset = form.watch('targetDatasetId');

  // Fetch datasets and matching methods on component mount
  useEffect(() => {
    const fetchData = async () => {
      setIsLoadingData(true);
      setDataError(null);
      
      try {
        const [datasetsData, methodsData] = await Promise.all([
          matchingService.getDatasets(),
          matchingService.getMatchingMethods()
        ]);
        
        setDatasets(datasetsData);
        setMethods(methodsData);
      } catch (error) {
        console.error('Error fetching form data:', error);
        setDataError('Failed to load datasets and matching methods. Please try again.');
      } finally {
        setIsLoadingData(false);
      }
    };
    
    fetchData();
  }, []);

  // Handle form submission
  const handleSubmit = (values: MatchingConfigFormValues) => {
    const apiRequest = formValuesToApiRequest(values);
    onSubmit(apiRequest);
  };

  // Get source and target dataset fields
  const getSourceFields = () => {
    const dataset = datasets.find(d => d.id === watchSourceDataset);
    return dataset?.fields || [];
  };

  const getTargetFields = () => {
    const dataset = datasets.find(d => d.id === watchTargetDataset);
    return dataset?.fields || [];
  };

  // Check if a method is selected
  const isMethodSelected = (methodId: string) => {
    return watchMethods.includes(methodId);
  };

  // Navigate to next tab
  const goToNextTab = () => {
    if (activeTab === 'datasets') setActiveTab('methods');
    else if (activeTab === 'methods') setActiveTab('mappings');
    else if (activeTab === 'mappings') setActiveTab('thresholds');
    else if (activeTab === 'thresholds') setActiveTab('advanced');
  };

  // Navigate to previous tab
  const goToPrevTab = () => {
    if (activeTab === 'advanced') setActiveTab('thresholds');
    else if (activeTab === 'thresholds') setActiveTab('mappings');
    else if (activeTab === 'mappings') setActiveTab('methods');
    else if (activeTab === 'methods') setActiveTab('datasets');
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Matching Configuration</CardTitle>
        <CardDescription>
          Configure how records should be matched between datasets
        </CardDescription>
      </CardHeader>
      <CardContent>
        {(isLoadingData || dataError) ? (
          <div className="flex flex-col items-center justify-center py-8">
            {isLoadingData && (
              <>
                <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
                <p className="text-muted-foreground">Loading configuration data...</p>
              </>
            )}
            
            {dataError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{dataError}</AlertDescription>
              </Alert>
            )}
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)}>
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid grid-cols-5 mb-8">
                  <TabsTrigger value="datasets">Datasets</TabsTrigger>
                  <TabsTrigger value="methods">Methods</TabsTrigger>
                  <TabsTrigger value="mappings">Field Mappings</TabsTrigger>
                  <TabsTrigger value="thresholds">Thresholds</TabsTrigger>
                  <TabsTrigger value="advanced">Advanced</TabsTrigger>
                </TabsList>
                
                <TabsContent value="datasets">
                  <DatasetSelection 
                    form={form} 
                    datasets={datasets} 
                  />
                </TabsContent>
                
                <TabsContent value="methods">
                  <MethodSelection 
                    form={form} 
                    methods={methods} 
                  />
                </TabsContent>
                
                <TabsContent value="mappings">
                  <FieldMappings 
                    form={form} 
                    sourceFields={getSourceFields()} 
                    targetFields={getTargetFields()} 
                  />
                </TabsContent>
                
                <TabsContent value="thresholds">
                  <ThresholdSettings 
                    form={form} 
                    methods={watchMethods} 
                  />
                </TabsContent>
                
                <TabsContent value="advanced">
                  <AdvancedSettings 
                    form={form} 
                    methods={watchMethods}
                    sourceFields={getSourceFields()}
                    targetFields={getTargetFields()}
                    isHybridSelected={isMethodSelected('hybrid')}
                    isWeightedFieldSelected={isMethodSelected('weighted_field')}
                    isPhoneticNameSelected={isMethodSelected('phonetic_name')}
                  />
                </TabsContent>
              </Tabs>
              
              {error && (
                <Alert variant="destructive" className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              
              <div className="flex justify-between mt-8">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={goToPrevTab}
                  disabled={activeTab === 'datasets'}
                >
                  Previous
                </Button>
                
                {activeTab === 'advanced' ? (
                  <Button 
                    type="submit" 
                    disabled={isLoading || !form.formState.isValid}
                  >
                    {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Run Matching
                  </Button>
                ) : (
                  <Button 
                    type="button" 
                    onClick={goToNextTab}
                    disabled={
                      (activeTab === 'datasets' && (!watchSourceDataset || !watchTargetDataset)) ||
                      (activeTab === 'methods' && watchMethods.length === 0) ||
                      (activeTab === 'mappings' && Object.keys(form.getValues('fieldMappings')).length === 0)
                    }
                  >
                    Next
                  </Button>
                )}
              </div>
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
};

export default MatchingConfigForm;
