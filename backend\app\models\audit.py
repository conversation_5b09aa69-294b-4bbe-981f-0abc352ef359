from datetime import datetime
from sqlalchemy import Column, DateTime, Enum, ForeignKey, Integer, JSON, String, Text
from sqlalchemy.orm import relationship

from app.db.base_class import Base


class AuditLog(Base):
    id = Column(Integer, primary_key=True, index=True)
    action = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    category = Column(Enum(
        "data", 
        "system", 
        "edit", 
        "report", 
        "export", 
        name="audit_category"
    ), nullable=False)
    details = Column(JSON, nullable=True)  # Additional details about the action
    ip_address = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("user.id"), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="audit_logs")


class RecordHistory(Base):
    id = Column(Integer, primary_key=True, index=True)
    record_type = Column(String, nullable=False)  # Type of record (e.g., "invoice", "payment")
    record_id = Column(String, nullable=False)  # ID of the record being modified
    field = Column(String, nullable=False)  # Field that was changed
    old_value = Column(Text, nullable=True)  # Previous value
    new_value = Column(Text, nullable=True)  # New value
    reason = Column(Text, nullable=True)  # Reason for the change
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Foreign keys
    audit_log_id = Column(Integer, ForeignKey("auditlog.id"), nullable=True)
