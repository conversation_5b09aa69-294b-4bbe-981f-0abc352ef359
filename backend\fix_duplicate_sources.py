#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix duplicate data sources pointing to the same file.
"""

import sqlite3
import os

def fix_duplicate_sources():
    """Remove duplicate data sources that point to the same file."""
    
    # Connect to the SQLite database
    db_path = "app.db" if os.path.exists("app.db") else "dev.db"
    
    if not os.path.exists(db_path):
        print("No database file found")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Get all data sources
        cursor.execute("SELECT id, name, file_path FROM datasource ORDER BY id")
        sources = cursor.fetchall()
        
        print("Current data sources:")
        for source_id, name, file_path in sources:
            print(f"  ID: {source_id}, Name: {name}, File: {file_path}")
        
        # Group by file path to find duplicates (normalize path separators)
        file_groups = {}
        for source_id, name, file_path in sources:
            # Normalize path separators
            normalized_path = file_path.replace('\\', '/') if file_path else file_path
            if normalized_path not in file_groups:
                file_groups[normalized_path] = []
            file_groups[normalized_path].append((source_id, name, file_path))
        
        # Find duplicates
        duplicates_found = False
        for normalized_path, group in file_groups.items():
            if len(group) > 1:
                duplicates_found = True
                print(f"\n🔍 Found {len(group)} sources pointing to the same file: {normalized_path}")
                
                # Keep the first one (lowest ID), remove the rest
                keep_source = group[0]
                remove_sources = group[1:]
                
                print(f"  ✅ Keeping: ID {keep_source[0]} - {keep_source[1]}")
                
                for source_id, name, _ in remove_sources:
                    print(f"  ❌ Removing: ID {source_id} - {name}")
                    
                    # Remove the duplicate source
                    cursor.execute("DELETE FROM datasource WHERE id = ?", (source_id,))
        
        if duplicates_found:
            # Commit changes
            conn.commit()
            print("\n✅ Duplicate sources removed successfully!")
        else:
            print("\n✅ No duplicate sources found.")
        
        # Show final data sources
        cursor.execute("SELECT id, name, file_path FROM datasource ORDER BY id")
        sources = cursor.fetchall()
        
        print("\nFinal data sources:")
        for source_id, name, file_path in sources:
            print(f"  ID: {source_id}, Name: {name}, File: {file_path}")
            # Check if file exists
            if file_path and file_path.startswith("uploads/"):
                full_path = os.path.join("storage", file_path)
                exists = "✅" if os.path.exists(full_path) else "❌"
                print(f"    {exists} {full_path}")
        
    except Exception as e:
        print(f"Error: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    fix_duplicate_sources()
