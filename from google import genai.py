import google.generativeai as genai

# Replace with your actual Gemini API key
API_KEY = "AIzaSyBPgxm0YlgNIMdz0kyWIfRqO4Nlla6Cmpo"  #Always keep your API key safe

# Configure the generative model
genai.configure(api_key=API_KEY)

# For gemini pro model
model = genai.GenerativeModel('gemini-2.5-pro-preview-05-06')

# For gemini vision model
#model = genai.GenerativeModel('gemini-pro-vision')

response = model.generate_content("Explain how AI works in a few words")

print(response.text)
