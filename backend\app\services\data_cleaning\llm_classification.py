import logging
import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
import numpy as np
from tenacity import retry, stop_after_attempt, wait_exponential

from app.models.data_source import DataSource
from app.utils.file_handlers import dataframe_to_dict_list
from app.utils.gcp_storage import storage_client
from app.utils.llm_client import llm_client

logger = logging.getLogger(__name__)


def classify_data(
    data_source: DataSource,
    config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Classify data using LLM.
    
    Args:
        data_source: Data source model
        config: Configuration for classification
        
    Returns:
        Dictionary with classification results
    """
    try:
        # Load data from source
        df = load_dataframe_from_source(data_source)
        
        # Get classification type
        classification_type = config.get("classification_type", "category")
        
        # Get columns to classify
        text_column = config.get("text_column")
        if not text_column or text_column not in df.columns:
            raise ValueError(f"Text column '{text_column}' not found in data source")
        
        # Get categories or entities to extract
        categories = config.get("categories", [])
        entities = config.get("entities", [])
        
        # Get sample size (limit to avoid excessive API calls)
        sample_size = min(config.get("sample_size", 100), len(df))
        
        # Sample data
        if sample_size < len(df):
            sample_df = df.sample(sample_size, random_state=42)
        else:
            sample_df = df
        
        # Initialize results
        results = {
            "total_rows": len(df),
            "processed_rows": sample_size,
            "classification_type": classification_type,
            "classifications": [],
            "distribution": {},
            "confidence": {}
        }
        
        # Apply classification based on type
        if classification_type == "category":
            if not categories:
                raise ValueError("Categories must be provided for category classification")
            
            classifications, distribution, confidence = classify_categories(
                sample_df, text_column, categories, config
            )
            results["classifications"] = classifications
            results["distribution"] = distribution
            results["confidence"] = confidence
        
        elif classification_type == "entity":
            if not entities:
                raise ValueError("Entities must be provided for entity extraction")
            
            classifications, distribution = extract_entities(
                sample_df, text_column, entities, config
            )
            results["classifications"] = classifications
            results["distribution"] = distribution
        
        elif classification_type == "sentiment":
            classifications, distribution, confidence = analyze_sentiment(
                sample_df, text_column, config
            )
            results["classifications"] = classifications
            results["distribution"] = distribution
            results["confidence"] = confidence
        
        elif classification_type == "custom":
            prompt_template = config.get("prompt_template")
            if not prompt_template:
                raise ValueError("Prompt template must be provided for custom classification")
            
            classifications = custom_classification(
                sample_df, text_column, prompt_template, config
            )
            results["classifications"] = classifications
            
            # Calculate distribution for custom classification
            if classifications:
                distribution = {}
                for item in classifications:
                    label = item.get("label", "unknown")
                    if label not in distribution:
                        distribution[label] = 0
                    distribution[label] += 1
                
                # Convert counts to percentages
                for label in distribution:
                    distribution[label] = (distribution[label] / len(classifications)) * 100
                
                results["distribution"] = distribution
        
        else:
            raise ValueError(f"Unknown classification type: {classification_type}")
        
        # Save classification data if specified
        output_path = None
        if config.get("save_output", False):
            output_path = f"classifications/{data_source.id}/{datetime.now().strftime('%Y%m%d%H%M%S')}.json"
            save_classification_data(results, output_path)
            results["output_path"] = output_path
        
        return results
    except Exception as e:
        logger.error(f"Error classifying data: {e}")
        raise ValueError(f"Error classifying data: {str(e)}")


def load_dataframe_from_source(data_source: DataSource) -> pd.DataFrame:
    """
    Load data from a data source into a pandas DataFrame.
    
    Args:
        data_source: Data source model
        
    Returns:
        DataFrame containing the data
    """
    if data_source.source_type == "file":
        # Download file from GCS
        local_path = f"/tmp/{data_source.file_path.split('/')[-1]}"
        storage_client.download_file(data_source.file_path, local_path)
        
        # Read file based on type
        if data_source.file_type == "csv":
            return pd.read_csv(local_path)
        elif data_source.file_type == "excel":
            return pd.read_excel(local_path)
        elif data_source.file_type == "json":
            return pd.read_json(local_path)
        else:
            raise ValueError(f"Unsupported file type: {data_source.file_type}")
    elif data_source.source_type == "database":
        # Implement database connection logic
        raise NotImplementedError("Database source loading not implemented yet")
    elif data_source.source_type == "cloud_service":
        # Implement cloud service connection logic
        raise NotImplementedError("Cloud service source loading not implemented yet")
    else:
        raise ValueError(f"Unsupported source type: {data_source.source_type}")


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10))
def classify_categories(
    df: pd.DataFrame,
    text_column: str,
    categories: List[str],
    config: Dict[str, Any]
) -> Tuple[List[Dict[str, Any]], Dict[str, float], Dict[str, float]]:
    """
    Classify text into predefined categories using LLM.
    
    Args:
        df: DataFrame to analyze
        text_column: Column containing text to classify
        categories: List of categories to classify into
        config: Configuration for classification
        
    Returns:
        Tuple containing:
        - List of classification results
        - Distribution of categories
        - Average confidence by category
    """
    # Get configuration
    batch_size = min(config.get("batch_size", 10), len(df))
    
    # Initialize results
    classifications = []
    category_counts = {category: 0 for category in categories}
    category_confidence = {category: [] for category in categories}
    
    # Process in batches to avoid excessive API calls
    for i in range(0, len(df), batch_size):
        batch_df = df.iloc[i:i+batch_size]
        
        # Process each row in the batch
        for _, row in batch_df.iterrows():
            text = row[text_column]
            
            # Skip empty text
            if pd.isna(text) or text == "":
                continue
            
            # Create prompt for classification
            prompt = f"""
            Classify the following text into one of these categories: {', '.join(categories)}.
            
            Text: {text}
            
            Return your answer as a JSON object with these fields:
            - "category": The most appropriate category from the list
            - "confidence": A number between 0 and 1 indicating your confidence in this classification
            - "reasoning": A brief explanation of why this category was chosen
            
            JSON response:
            """
            
            try:
                # Get response from LLM
                response = llm_client.llm.invoke(prompt)
                
                # Parse JSON response
                try:
                    # Extract JSON from response (handle cases where LLM adds extra text)
                    json_str = extract_json_from_text(response)
                    result = json.loads(json_str)
                    
                    # Validate result
                    if "category" not in result or result["category"] not in categories:
                        logger.warning(f"Invalid category in LLM response: {result}")
                        continue
                    
                    # Add to results
                    classification = {
                        "index": row.name,
                        "text": text,
                        "category": result["category"],
                        "confidence": result.get("confidence", 0.0),
                        "reasoning": result.get("reasoning", "")
                    }
                    classifications.append(classification)
                    
                    # Update counts and confidence
                    category = result["category"]
                    category_counts[category] += 1
                    category_confidence[category].append(result.get("confidence", 0.0))
                
                except Exception as e:
                    logger.warning(f"Error parsing LLM response: {e}")
                    logger.warning(f"Response: {response}")
                    continue
            
            except Exception as e:
                logger.warning(f"Error calling LLM: {e}")
                continue
    
    # Calculate distribution
    total = sum(category_counts.values())
    distribution = {
        category: (count / total) * 100 if total > 0 else 0
        for category, count in category_counts.items()
    }
    
    # Calculate average confidence
    confidence = {
        category: sum(scores) / len(scores) if scores else 0
        for category, scores in category_confidence.items()
    }
    
    return classifications, distribution, confidence


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10))
def extract_entities(
    df: pd.DataFrame,
    text_column: str,
    entities: List[str],
    config: Dict[str, Any]
) -> Tuple[List[Dict[str, Any]], Dict[str, float]]:
    """
    Extract entities from text using LLM.
    
    Args:
        df: DataFrame to analyze
        text_column: Column containing text to analyze
        entities: List of entity types to extract
        config: Configuration for entity extraction
        
    Returns:
        Tuple containing:
        - List of entity extraction results
        - Distribution of entity types
    """
    # Get configuration
    batch_size = min(config.get("batch_size", 5), len(df))
    
    # Initialize results
    extractions = []
    entity_counts = {entity: 0 for entity in entities}
    
    # Process in batches to avoid excessive API calls
    for i in range(0, len(df), batch_size):
        batch_df = df.iloc[i:i+batch_size]
        
        # Process each row in the batch
        for _, row in batch_df.iterrows():
            text = row[text_column]
            
            # Skip empty text
            if pd.isna(text) or text == "":
                continue
            
            # Create prompt for entity extraction
            prompt = f"""
            Extract the following entity types from the text: {', '.join(entities)}.
            
            Text: {text}
            
            Return your answer as a JSON object where:
            - Each key is an entity type from the list
            - Each value is a list of extracted entities of that type
            
            JSON response:
            """
            
            try:
                # Get response from LLM
                response = llm_client.llm.invoke(prompt)
                
                # Parse JSON response
                try:
                    # Extract JSON from response (handle cases where LLM adds extra text)
                    json_str = extract_json_from_text(response)
                    result = json.loads(json_str)
                    
                    # Validate result
                    valid_result = {}
                    for entity_type in entities:
                        if entity_type in result and isinstance(result[entity_type], list):
                            valid_result[entity_type] = result[entity_type]
                            
                            # Update counts
                            entity_counts[entity_type] += len(result[entity_type])
                        else:
                            valid_result[entity_type] = []
                    
                    # Add to results
                    extraction = {
                        "index": row.name,
                        "text": text,
                        "entities": valid_result
                    }
                    extractions.append(extraction)
                
                except Exception as e:
                    logger.warning(f"Error parsing LLM response: {e}")
                    logger.warning(f"Response: {response}")
                    continue
            
            except Exception as e:
                logger.warning(f"Error calling LLM: {e}")
                continue
    
    # Calculate distribution
    total = sum(entity_counts.values())
    distribution = {
        entity: (count / total) * 100 if total > 0 else 0
        for entity, count in entity_counts.items()
    }
    
    return extractions, distribution


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10))
def analyze_sentiment(
    df: pd.DataFrame,
    text_column: str,
    config: Dict[str, Any]
) -> Tuple[List[Dict[str, Any]], Dict[str, float], Dict[str, float]]:
    """
    Analyze sentiment of text using LLM.
    
    Args:
        df: DataFrame to analyze
        text_column: Column containing text to analyze
        config: Configuration for sentiment analysis
        
    Returns:
        Tuple containing:
        - List of sentiment analysis results
        - Distribution of sentiment
        - Average confidence by sentiment
    """
    # Get configuration
    batch_size = min(config.get("batch_size", 10), len(df))
    sentiments = config.get("sentiments", ["positive", "neutral", "negative"])
    
    # Initialize results
    analyses = []
    sentiment_counts = {sentiment: 0 for sentiment in sentiments}
    sentiment_confidence = {sentiment: [] for sentiment in sentiments}
    
    # Process in batches to avoid excessive API calls
    for i in range(0, len(df), batch_size):
        batch_df = df.iloc[i:i+batch_size]
        
        # Process each row in the batch
        for _, row in batch_df.iterrows():
            text = row[text_column]
            
            # Skip empty text
            if pd.isna(text) or text == "":
                continue
            
            # Create prompt for sentiment analysis
            prompt = f"""
            Analyze the sentiment of the following text as one of: {', '.join(sentiments)}.
            
            Text: {text}
            
            Return your answer as a JSON object with these fields:
            - "sentiment": The sentiment from the list
            - "confidence": A number between 0 and 1 indicating your confidence in this analysis
            - "reasoning": A brief explanation of why this sentiment was chosen
            
            JSON response:
            """
            
            try:
                # Get response from LLM
                response = llm_client.llm.invoke(prompt)
                
                # Parse JSON response
                try:
                    # Extract JSON from response (handle cases where LLM adds extra text)
                    json_str = extract_json_from_text(response)
                    result = json.loads(json_str)
                    
                    # Validate result
                    if "sentiment" not in result or result["sentiment"] not in sentiments:
                        logger.warning(f"Invalid sentiment in LLM response: {result}")
                        continue
                    
                    # Add to results
                    analysis = {
                        "index": row.name,
                        "text": text,
                        "sentiment": result["sentiment"],
                        "confidence": result.get("confidence", 0.0),
                        "reasoning": result.get("reasoning", "")
                    }
                    analyses.append(analysis)
                    
                    # Update counts and confidence
                    sentiment = result["sentiment"]
                    sentiment_counts[sentiment] += 1
                    sentiment_confidence[sentiment].append(result.get("confidence", 0.0))
                
                except Exception as e:
                    logger.warning(f"Error parsing LLM response: {e}")
                    logger.warning(f"Response: {response}")
                    continue
            
            except Exception as e:
                logger.warning(f"Error calling LLM: {e}")
                continue
    
    # Calculate distribution
    total = sum(sentiment_counts.values())
    distribution = {
        sentiment: (count / total) * 100 if total > 0 else 0
        for sentiment, count in sentiment_counts.items()
    }
    
    # Calculate average confidence
    confidence = {
        sentiment: sum(scores) / len(scores) if scores else 0
        for sentiment, scores in sentiment_confidence.items()
    }
    
    return analyses, distribution, confidence


@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10))
def custom_classification(
    df: pd.DataFrame,
    text_column: str,
    prompt_template: str,
    config: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    Apply custom classification using a prompt template.
    
    Args:
        df: DataFrame to analyze
        text_column: Column containing text to classify
        prompt_template: Template for the prompt
        config: Configuration for classification
        
    Returns:
        List of classification results
    """
    # Get configuration
    batch_size = min(config.get("batch_size", 5), len(df))
    
    # Initialize results
    classifications = []
    
    # Process in batches to avoid excessive API calls
    for i in range(0, len(df), batch_size):
        batch_df = df.iloc[i:i+batch_size]
        
        # Process each row in the batch
        for _, row in batch_df.iterrows():
            text = row[text_column]
            
            # Skip empty text
            if pd.isna(text) or text == "":
                continue
            
            # Create prompt using template
            prompt = prompt_template.replace("{text}", text)
            
            try:
                # Get response from LLM
                response = llm_client.llm.invoke(prompt)
                
                # Parse JSON response
                try:
                    # Extract JSON from response (handle cases where LLM adds extra text)
                    json_str = extract_json_from_text(response)
                    result = json.loads(json_str)
                    
                    # Add to results
                    classification = {
                        "index": row.name,
                        "text": text,
                        "result": result
                    }
                    
                    # Extract label if available
                    if "label" in result:
                        classification["label"] = result["label"]
                    elif "category" in result:
                        classification["label"] = result["category"]
                    elif "classification" in result:
                        classification["label"] = result["classification"]
                    
                    classifications.append(classification)
                
                except Exception as e:
                    logger.warning(f"Error parsing LLM response: {e}")
                    logger.warning(f"Response: {response}")
                    
                    # Add raw response as result
                    classification = {
                        "index": row.name,
                        "text": text,
                        "raw_response": response
                    }
                    classifications.append(classification)
            
            except Exception as e:
                logger.warning(f"Error calling LLM: {e}")
                continue
    
    return classifications


def extract_json_from_text(text: str) -> str:
    """
    Extract JSON from text that might contain additional content.
    
    Args:
        text: Text that might contain JSON
        
    Returns:
        Extracted JSON string
    """
    # Try to find JSON object in the text
    start_idx = text.find('{')
    end_idx = text.rfind('}')
    
    if start_idx != -1 and end_idx != -1 and start_idx < end_idx:
        return text[start_idx:end_idx+1]
    
    # If no JSON object found, return the original text
    return text


def save_classification_data(data: Dict[str, Any], output_path: str) -> str:
    """
    Save classification data to GCS.
    
    Args:
        data: Classification data to save
        output_path: Path in GCS to save to
        
    Returns:
        GCS path where data was saved
    """
    # Save to a temporary file
    local_path = f"/tmp/{output_path.split('/')[-1]}"
    with open(local_path, 'w') as f:
        json.dump(data, f)
    
    # Upload to GCS
    with open(local_path, 'rb') as f:
        storage_client.upload_file(f, output_path)
    
    return output_path
