import io
import logging
from typing import Any, Dict, List, Optional, <PERSON>ple

import pandas as pd
from fastapi import UploadFile

from app.utils.file_handlers import dataframe_to_dict_list
from app.utils.gcp_storage import storage_client

logger = logging.getLogger(__name__)


async def process_csv(file: UploadFile) -> Tuple[List[Dict[str, Any]], Dict[str, Any]]:
    """
    Process a CSV file and return the data and schema.
    
    Args:
        file: Uploaded CSV file
        
    Returns:
        Tuple containing:
        - List of dictionaries representing the data
        - Dictionary representing the schema
    """
    try:
        # Read the file
        contents = await file.read()
        file.file.seek(0)  # Reset file pointer
        
        # Parse CSV
        df = pd.read_csv(io.BytesIO(contents))
        
        # Infer schema
        schema = {}
        for column in df.columns:
            dtype = str(df[column].dtype)
            if dtype.startswith('int'):
                schema[column] = 'integer'
            elif dtype.startswith('float'):
                schema[column] = 'number'
            elif dtype.startswith('datetime'):
                schema[column] = 'datetime'
            elif dtype.startswith('bool'):
                schema[column] = 'boolean'
            else:
                schema[column] = 'string'
        
        # Convert to list of dictionaries
        data = dataframe_to_dict_list(df)
        
        return data, schema
    except Exception as e:
        logger.error(f"Error processing CSV file: {e}")
        raise ValueError(f"Error processing CSV file: {str(e)}")


def detect_delimiter(sample: str) -> str:
    """
    Detect the delimiter used in a CSV file.
    
    Args:
        sample: Sample of the CSV content
        
    Returns:
        Detected delimiter
    """
    # Count occurrences of common delimiters
    delimiters = [',', ';', '\t', '|']
    counts = {d: sample.count(d) for d in delimiters}
    
    # Return the delimiter with the highest count
    return max(counts.items(), key=lambda x: x[1])[0]


def detect_header(df: pd.DataFrame) -> bool:
    """
    Detect if the first row is a header.
    
    Args:
        df: DataFrame to check
        
    Returns:
        True if the first row is likely a header, False otherwise
    """
    # Check if the first row has different types than the rest
    first_row = df.iloc[0]
    rest = df.iloc[1:]
    
    # If the first row contains strings and the rest are mostly numeric, it's likely a header
    if all(isinstance(val, str) for val in first_row) and \
       rest.select_dtypes(include=['number']).shape[1] > 0:
        return True
    
    return False
