import logging

from app.core.config import settings

# Check if Gemini is available
try:
    from app.utils.gemini_client import get_gemini_client
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("Gemini client not available. Make sure google-generativeai package is installed.")

logger = logging.getLogger(__name__)

def get_llm_client():
    """
    Get the appropriate LLM client based on settings.

    Returns:
        An LLM client instance
    """
    # Check if Gemini should be used
    use_gemini = getattr(settings, "USE_GEMINI", False)
    gemini_api_key = getattr(settings, "GEMINI_API_KEY", "")

    logger.info(f"USE_GEMINI setting: {use_gemini}")
    logger.info(f"GEMINI_API_KEY available: {'Yes' if gemini_api_key else 'No'}")
    logger.info(f"GEMINI_AVAILABLE: {GEMINI_AVAILABLE}")

    if use_gemini and GEMINI_AVAILABLE and gemini_api_key:
        try:
            # Use the lazy-loading function to get the client
            gemini_client = get_gemini_client()
            logger.info("Using Google Gemini LLM client")
            return gemini_client
        except Exception as e:
            logger.error(f"Error initializing Gemini client: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            logger.warning("Falling back to mock LLM client")
    else:
        if use_gemini:
            if not GEMINI_AVAILABLE:
                logger.warning("Gemini is enabled in settings but the package is not available. Falling back to mock LLM client.")
            elif not gemini_api_key:
                logger.warning("Gemini is enabled in settings but no API key is provided. Falling back to mock LLM client.")
            else:
                logger.warning("Gemini is enabled in settings but cannot be used for an unknown reason. Falling back to mock LLM client.")
        else:
            logger.info("Gemini is disabled in settings. Using mock LLM client.")

    # Use mock client as fallback
    from app.utils.llm_client import get_mock_client
    return get_mock_client()