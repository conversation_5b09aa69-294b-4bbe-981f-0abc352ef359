from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class ReconciliationRule(BaseModel):
    name: str
    rule_type: str = Field(..., description="Type of rule: 'exact_match', 'fuzzy_match', or 'semantic_match'")
    source_fields: List[str]
    target_fields: List[str]
    threshold: Optional[float] = 0.8
    config: Optional[Dict[str, Any]] = None


class ReconciliationRequest(BaseModel):
    pipeline_id: int
    source_a_id: int
    source_b_id: int
    rules: List[ReconciliationRule]


class ReconciliationResponse(BaseModel):
    id: int
    pipeline_id: int
    status: str
    total_records: int
    matched_records: int
    anomaly_records: int
    unmatched_records: int
    match_rate: float
    anomaly_details: Optional[Dict[str, Any]] = None
    result_data: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
