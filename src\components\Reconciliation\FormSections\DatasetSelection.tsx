import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { MatchingConfigFormValues } from '../validationSchema';
import { 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { InfoIcon } from 'lucide-react';

interface DatasetSelectionProps {
  form: UseFormReturn<MatchingConfigFormValues>;
  datasets: { id: string; name: string; fields: string[] }[];
}

const DatasetSelection: React.FC<DatasetSelectionProps> = ({ form, datasets }) => {
  const sourceDatasetId = form.watch('sourceDatasetId');
  const targetDatasetId = form.watch('targetDatasetId');
  
  // Check if the same dataset is selected for both source and target
  const isSameDataset = sourceDatasetId && targetDatasetId && sourceDatasetId === targetDatasetId;

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <FormField
          control={form.control}
          name="sourceDatasetId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Source Dataset</FormLabel>
              <Select 
                onValueChange={field.onChange} 
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select source dataset" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {datasets.map((dataset) => (
                    <SelectItem key={dataset.id} value={dataset.id}>
                      {dataset.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <FormField
          control={form.control}
          name="targetDatasetId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Target Dataset</FormLabel>
              <Select 
                onValueChange={field.onChange} 
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select target dataset" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {datasets.map((dataset) => (
                    <SelectItem key={dataset.id} value={dataset.id}>
                      {dataset.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      
      {isSameDataset && (
        <Alert variant="warning" className="mt-4">
          <InfoIcon className="h-4 w-4" />
          <AlertDescription>
            You've selected the same dataset for both source and target. 
            This will match records within the same dataset.
          </AlertDescription>
        </Alert>
      )}
      
      {sourceDatasetId && targetDatasetId && (
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-slate-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium mb-2">Source Dataset Fields</h3>
            <div className="max-h-40 overflow-y-auto">
              <ul className="list-disc list-inside text-sm">
                {datasets.find(d => d.id === sourceDatasetId)?.fields.map((field, index) => (
                  <li key={index} className="text-gray-600">{field}</li>
                ))}
              </ul>
            </div>
          </div>
          
          <div className="bg-slate-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium mb-2">Target Dataset Fields</h3>
            <div className="max-h-40 overflow-y-auto">
              <ul className="list-disc list-inside text-sm">
                {datasets.find(d => d.id === targetDatasetId)?.fields.map((field, index) => (
                  <li key={index} className="text-gray-600">{field}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DatasetSelection;
