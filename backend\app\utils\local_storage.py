import logging
import os
import shutil
from typing import Binary<PERSON>, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)

class LocalStorageClient:
    """Client for interacting with local file storage."""

    def __init__(self):
        # Set the base directory for storage
        self.base_dir = os.environ.get("LOCAL_STORAGE_DIR", "storage")
        
        # Create the base directory if it doesn't exist
        os.makedirs(self.base_dir, exist_ok=True)
        logger.info(f"Using local storage directory: {self.base_dir}")

    def upload_file(self, file: BinaryIO, destination_path: str) -> str:
        """
        Upload a file to local storage.

        Args:
            file: File-like object to upload
            destination_path: Path in the storage to upload to

        Returns:
            Path of the uploaded file
        """
        # Create the full path
        full_path = os.path.join(self.base_dir, destination_path)
        
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(full_path), exist_ok=True)
        
        # Write the file
        with open(full_path, 'wb') as f:
            # Reset file pointer to beginning
            file.seek(0)
            # Copy the file
            shutil.copyfileobj(file, f)
        
        logger.info(f"File uploaded to {full_path}")
        return full_path

    def download_file(self, source_path: str, destination_file_name: str) -> None:
        """
        Copy a file from storage to a destination.

        Args:
            source_path: Path in the storage to copy from
            destination_file_name: Local path to copy to
        """
        # Create the full source path
        full_source_path = os.path.join(self.base_dir, source_path)
        
        # Check if the source file exists
        if not os.path.exists(full_source_path):
            logger.error(f"Source file {full_source_path} does not exist")
            return
        
        # Create the directory if it doesn't exist
        os.makedirs(os.path.dirname(destination_file_name), exist_ok=True)
        
        # Copy the file
        shutil.copy2(full_source_path, destination_file_name)
        logger.info(f"File copied to {destination_file_name}")

    def delete_file(self, path: str) -> None:
        """
        Delete a file from storage.

        Args:
            path: Path in the storage to delete
        """
        # Create the full path
        full_path = os.path.join(self.base_dir, path)
        
        # Check if the file exists
        if not os.path.exists(full_path):
            logger.error(f"File {full_path} does not exist")
            return
        
        # Delete the file
        os.remove(full_path)
        logger.info(f"File {full_path} deleted")

    def list_files(self, prefix: Optional[str] = None) -> List[str]:
        """
        List files in the storage with an optional prefix.

        Args:
            prefix: Optional prefix to filter files

        Returns:
            List of file paths
        """
        # Create the full path for the prefix
        prefix_path = os.path.join(self.base_dir, prefix) if prefix else self.base_dir
        
        # Check if the prefix path exists
        if not os.path.exists(prefix_path):
            return []
        
        # Get all files in the prefix path
        files = []
        for root, _, filenames in os.walk(prefix_path):
            for filename in filenames:
                # Get the full path
                full_path = os.path.join(root, filename)
                # Convert to relative path
                rel_path = os.path.relpath(full_path, self.base_dir)
                files.append(rel_path)
        
        return files

    def file_exists(self, path: str) -> bool:
        """
        Check if a file exists in the storage.

        Args:
            path: Path in the storage to check

        Returns:
            True if the file exists, False otherwise
        """
        # Create the full path
        full_path = os.path.join(self.base_dir, path)
        
        # Check if the file exists
        return os.path.exists(full_path)

# Create a singleton instance
storage_client = LocalStorageClient()
