from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class DataSourceBase(BaseModel):
    name: str
    description: Optional[str] = None
    source_type: str = Field(..., description="Type of data source: 'file', 'database', or 'cloud_service'")


class DataSourceCreate(DataSourceBase):
    connection_details: Optional[Dict[str, Any]] = None
    file_type: Optional[str] = None
    sheet_name: Optional[str] = None


class DataSourceUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    connection_details: Optional[Dict[str, Any]] = None


class DataSourceInResponse(DataSourceBase):
    id: int
    file_path: Optional[str] = None
    file_type: Optional[str] = None
    # sheet_name field is not in the database schema yet
    connection_details: Optional[Dict[str, Any]] = None
    data_schema: Optional[Dict[str, Any]] = Field(None, alias="schema")
    created_at: datetime
    updated_at: datetime
    owner_id: int

    class Config:
        from_attributes = True
        populate_by_name = True

class DataSourceResponse(BaseModel):
    data_source: Optional[DataSourceInResponse] = None
    requires_sheet_selection: Optional[bool] = None
    sheet_names: Optional[List[str]] = None
    previews: Optional[Dict[str, Any]] = None
    default_sheet: Optional[str] = None

    class Config:
        from_attributes = True
