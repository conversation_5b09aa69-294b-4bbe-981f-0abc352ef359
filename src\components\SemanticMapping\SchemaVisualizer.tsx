import React, { useState, useEffect } from 'react';
import { ColumnInfo, FieldMapping, SchemaInfo } from '../../services/semanticMappingService';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../ui/tooltip';
import { InfoIcon, ArrowRight, Plus, Filter, Eye, EyeOff } from 'lucide-react';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';

// Define semantic type colors
const SEMANTIC_TYPE_COLORS = {
  amount: '#4CAF50',     // Green
  date: '#2196F3',       // Blue
  description: '#9C27B0', // Purple
  category: '#FF9800',   // Orange
  account: '#795548',    // <PERSON>
  payee: '#607D8B',      // Blue Grey
  income: '#8BC34A',     // Light Green
  expense: '#F44336',    // Red
  unknown: '#9E9E9E'     // Grey
};

// Column card component
const ColumnCard = ({
  column,
  schema,
  onSelect,
  isSelected = false,
  isSuggested = false,
  similarityScore = 0,
  isFiltered = false
}: {
  column: ColumnInfo;
  schema: SchemaInfo;
  onSelect: (schemaIndex: number, column: ColumnInfo) => void;
  isSelected?: boolean;
  isSuggested?: boolean;
  similarityScore?: number;
  isFiltered?: boolean;
}) => {
  const semanticType = column.semantic_type || 'unknown';
  const color = semanticType ?
    SEMANTIC_TYPE_COLORS[semanticType as keyof typeof SEMANTIC_TYPE_COLORS] || SEMANTIC_TYPE_COLORS.unknown
    : SEMANTIC_TYPE_COLORS.unknown;

  // Determine card styling based on selection and suggestion state
  let cardClassName = "w-full shadow-sm border-t-2 cursor-pointer hover:shadow-md transition-all";

  if (isSelected) {
    cardClassName += " ring-2 ring-blue-500 shadow-md";
  } else if (isSuggested) {
    cardClassName += " ring-2 ring-yellow-300 shadow-md";
  }

  if (isFiltered) {
    cardClassName += " opacity-40";
  }

  return (
    <Card
      className={cardClassName}
      style={{ borderTopColor: color }}
      onClick={() => onSelect(schema.source_index, column)}
    >
      <CardContent className="p-3">
        <div className="flex items-center justify-between mb-2">
          <div className="font-medium truncate">{column.name}</div>
          <div className="flex items-center space-x-1">
            {isSuggested && (
              <Badge variant="outline" className="text-xs bg-yellow-50 border-yellow-300 text-yellow-700">
                Suggested Match {similarityScore > 0 ? `(${Math.round(similarityScore * 100)}%)` : ''}
              </Badge>
            )}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <InfoIcon className="h-4 w-4 text-gray-400" />
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-1 text-xs">
                    <p><strong>Source:</strong> {schema.source_name}</p>
                    <p><strong>Type:</strong> {column.dtype}</p>
                    <p><strong>Null Count:</strong> {column.null_count}</p>
                    <p><strong>Unique Count:</strong> {column.unique_count}</p>
                    {isSelected && <p className="text-blue-500 font-bold">Currently Selected</p>}
                    {isSuggested && <p className="text-yellow-500 font-bold">Suggested Match</p>}
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <Badge
            variant="outline"
            className="text-xs"
            style={{ backgroundColor: `${color}20`, borderColor: color, color }}
          >
            {semanticType}
          </Badge>
          <div className="text-xs text-gray-500">
            {column.confidence ? `${Math.round(column.confidence * 100)}% confidence` : ''}
          </div>
        </div>

        {column.sample_values && column.sample_values.length > 0 && (
          <div className="mt-2">
            <div className="text-xs text-gray-500 mb-1">Sample values:</div>
            <div className="text-xs bg-gray-50 p-1 rounded max-h-16 overflow-y-auto">
              {column.sample_values.map((value, i) => (
                <div key={i} className="truncate">{value}</div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Mapping item component
const MappingItem = ({
  mapping,
  schemas,
  onDelete
}: {
  mapping: FieldMapping;
  schemas: SchemaInfo[];
  onDelete: () => void;
}) => {
  const semanticType = mapping.semantic_type || 'unknown';
  const color = semanticType ?
    SEMANTIC_TYPE_COLORS[semanticType as keyof typeof SEMANTIC_TYPE_COLORS] || SEMANTIC_TYPE_COLORS.unknown
    : SEMANTIC_TYPE_COLORS.unknown;

  return (
    <Card className="w-full shadow-sm border-l-2" style={{ borderLeftColor: color }}>
      <CardContent className="p-3">
        <div className="flex items-center justify-between mb-2">
          <Badge
            variant="outline"
            className="text-xs"
            style={{ backgroundColor: `${color}20`, borderColor: color, color }}
          >
            {semanticType}
          </Badge>
          <div className="text-xs text-gray-500">
            {mapping.confidence ? `${Math.round(mapping.confidence * 100)}% confidence` : ''}
          </div>
        </div>

        <div className="space-y-2">
          {mapping.columns.map((column, index) => {
            const schema = schemas.find(s => s.source_index === column.source_index);
            if (!schema) return null;

            return (
              <div key={index} className="flex items-center">
                {index > 0 && (
                  <div className="flex-shrink-0 mx-2">
                    <ArrowRight className="h-4 w-4 text-gray-400" />
                  </div>
                )}
                <div className="flex-grow bg-gray-50 p-1 rounded text-sm">
                  <div className="font-medium">{column.column_name}</div>
                  <div className="text-xs text-gray-500">{schema.source_name}</div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="mt-2 flex justify-end">
          <Button variant="ghost" size="sm" onClick={onDelete}>
            Remove
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

interface SchemaVisualizerProps {
  schemas: SchemaInfo[];
  mappings: FieldMapping[];
  onMappingsChange: (mappings: FieldMapping[]) => void;
}

const SchemaVisualizer: React.FC<SchemaVisualizerProps> = ({
  schemas,
  mappings,
  onMappingsChange
}) => {
  const [selectedSchema, setSelectedSchema] = useState<number | null>(null);
  const [selectedColumn, setSelectedColumn] = useState<ColumnInfo | null>(null);
  const [targetSchema, setTargetSchema] = useState<number | null>(null);
  const [targetColumn, setTargetColumn] = useState<string>('');
  const [semanticType, setSemanticType] = useState<string>('');

  // New state for filtering and suggestions
  const [showOnlyMappedFields, setShowOnlyMappedFields] = useState(false);
  const [suggestedMatches, setSuggestedMatches] = useState<{
    sourceIndex: number;
    columnName: string;
    targetIndex: number;
    targetColumn: string;
    score: number;
  }[]>([]);

  // Calculate which columns are used in mappings
  const usedColumns = React.useMemo(() => {
    const result: Record<number, Set<string>> = {};

    // Initialize sets for each schema
    schemas.forEach(schema => {
      result[schema.source_index] = new Set<string>();
    });

    // Add columns used in mappings
    mappings.forEach(mapping => {
      mapping.columns.forEach(col => {
        if (result[col.source_index]) {
          result[col.source_index].add(col.column_name);
        }
      });
    });

    return result;
  }, [schemas, mappings]);

  // Generate suggested matches when a column is selected
  useEffect(() => {
    if (selectedSchema !== null && selectedColumn) {
      const suggestions: typeof suggestedMatches = [];

      // For each other schema
      schemas.forEach(schema => {
        if (schema.source_index === selectedSchema) return;

        // For each column in that schema
        schema.columns.forEach(column => {
          let score = 0;

          // Calculate similarity score based on semantic type
          if (column.semantic_type && selectedColumn.semantic_type &&
              column.semantic_type === selectedColumn.semantic_type) {
            score += 0.5;
          }

          // Calculate similarity based on column name
          const nameSimScore = calculateNameSimilarity(selectedColumn.name, column.name);
          score += nameSimScore * 0.3;

          // Calculate similarity based on sample values
          if (selectedColumn.sample_values && column.sample_values) {
            const valueSimilarity = calculateValueSimilarity(
              selectedColumn.sample_values, column.sample_values);
            score += valueSimilarity * 0.2;
          }

          // Add to suggestions if score is high enough
          if (score >= 0.4) {
            suggestions.push({
              sourceIndex: selectedSchema,
              columnName: selectedColumn.name,
              targetIndex: schema.source_index,
              targetColumn: column.name,
              score
            });
          }
        });
      });

      // Sort by score and take top 3
      suggestions.sort((a, b) => b.score - a.score);
      setSuggestedMatches(suggestions.slice(0, 3));
    } else {
      setSuggestedMatches([]);
    }
  }, [selectedSchema, selectedColumn, schemas]);

  // Calculate name similarity
  const calculateNameSimilarity = (name1: string, name2: string): number => {
    const n1 = name1.toLowerCase();
    const n2 = name2.toLowerCase();

    // Exact match
    if (n1 === n2) return 1.0;

    // One is substring of the other
    if (n1.includes(n2) || n2.includes(n1)) return 0.8;

    // Check for common words
    const words1 = n1.split(/[^a-z0-9]+/);
    const words2 = n2.split(/[^a-z0-9]+/);

    let commonWords = 0;
    for (const w1 of words1) {
      if (w1.length < 3) continue; // Skip short words
      if (words2.includes(w1)) commonWords++;
    }

    if (commonWords > 0) {
      return 0.5 + (0.5 * commonWords / Math.max(words1.length, words2.length));
    }

    return 0;
  };

  // Calculate value similarity
  const calculateValueSimilarity = (values1: any[], values2: any[]): number => {
    if (!values1.length || !values2.length) return 0;

    // Convert to strings
    const strValues1 = values1.map(v => String(v));
    const strValues2 = values2.map(v => String(v));

    // Check for exact matches
    let exactMatches = 0;
    for (const v1 of strValues1) {
      if (strValues2.includes(v1)) exactMatches++;
    }

    if (exactMatches > 0) {
      return exactMatches / Math.min(strValues1.length, strValues2.length);
    }

    return 0;
  };

  // Handle column selection
  const handleColumnSelect = (schemaIndex: number, column: ColumnInfo) => {
    if (selectedSchema === null) {
      // First selection
      setSelectedSchema(schemaIndex);
      setSelectedColumn(column);
      setSemanticType(column.semantic_type || '');
    } else if (schemaIndex !== selectedSchema) {
      // Second selection (from a different schema)
      // Create a new mapping
      const newMapping: FieldMapping = {
        id: `mapping-${Date.now()}`,
        semantic_type: semanticType || column.semantic_type || selectedColumn?.semantic_type || 'unknown',
        columns: [
          {
            source_index: selectedSchema,
            column_name: selectedColumn!.name,
            confidence: selectedColumn!.confidence || 0.5
          },
          {
            source_index: schemaIndex,
            column_name: column.name,
            confidence: column.confidence || 0.5
          }
        ],
        confidence: ((selectedColumn?.confidence || 0.5) + (column.confidence || 0.5)) / 2
      };

      // Update mappings
      onMappingsChange([...mappings, newMapping]);

      // Reset selection
      setSelectedSchema(null);
      setSelectedColumn(null);
      setTargetSchema(null);
      setTargetColumn('');
      setSemanticType('');
    }
  };

  // Handle mapping creation from dropdown
  const handleCreateMapping = () => {
    if (selectedSchema === null || !selectedColumn || targetSchema === null || !targetColumn) {
      return;
    }

    // Find target column
    const schema = schemas.find(s => s.source_index === targetSchema);
    if (!schema) return;

    const column = schema.columns.find(c => c.name === targetColumn);
    if (!column) return;

    // Create a new mapping
    const newMapping: FieldMapping = {
      id: `mapping-${Date.now()}`,
      semantic_type: semanticType || column.semantic_type || selectedColumn.semantic_type || 'unknown',
      columns: [
        {
          source_index: selectedSchema,
          column_name: selectedColumn.name,
          confidence: selectedColumn.confidence || 0.5
        },
        {
          source_index: targetSchema,
          column_name: column.name,
          confidence: column.confidence || 0.5
        }
      ],
      confidence: ((selectedColumn.confidence || 0.5) + (column.confidence || 0.5)) / 2
    };

    // Update mappings
    onMappingsChange([...mappings, newMapping]);

    // Reset selection
    setSelectedSchema(null);
    setSelectedColumn(null);
    setTargetSchema(null);
    setTargetColumn('');
    setSemanticType('');
  };

  // Handle mapping deletion
  const handleDeleteMapping = (index: number) => {
    const newMappings = [...mappings];
    newMappings.splice(index, 1);
    onMappingsChange(newMappings);
  };

  // Cancel selection
  const handleCancelSelection = () => {
    setSelectedSchema(null);
    setSelectedColumn(null);
    setTargetSchema(null);
    setTargetColumn('');
    setSemanticType('');
  };

  // Create a mapping from a suggestion
  const handleCreateFromSuggestion = (suggestion: typeof suggestedMatches[0]) => {
    const sourceSchema = schemas.find(s => s.source_index === suggestion.sourceIndex);
    const targetSchema = schemas.find(s => s.source_index === suggestion.targetIndex);

    if (!sourceSchema || !targetSchema) return;

    const sourceColumn = sourceSchema.columns.find(c => c.name === suggestion.columnName);
    const targetColumn = targetSchema.columns.find(c => c.name === suggestion.targetColumn);

    if (!sourceColumn || !targetColumn) return;

    // Create a new mapping
    const newMapping: FieldMapping = {
      id: `mapping-${Date.now()}`,
      semantic_type: sourceColumn.semantic_type || targetColumn.semantic_type || 'unknown',
      columns: [
        {
          source_index: suggestion.sourceIndex,
          column_name: suggestion.columnName,
          confidence: sourceColumn.confidence || 0.5
        },
        {
          source_index: suggestion.targetIndex,
          column_name: suggestion.targetColumn,
          confidence: targetColumn.confidence || 0.5
        }
      ],
      confidence: suggestion.score
    };

    // Update mappings
    onMappingsChange([...mappings, newMapping]);

    // Reset selection and suggestions
    setSelectedSchema(null);
    setSelectedColumn(null);
    setSuggestedMatches([]);
  };

  return (
    <div className="space-y-4">
      {/* Controls */}
      <div className="flex items-center justify-between bg-gray-50 p-3 rounded-md">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="show-mapped-fields"
              checked={showOnlyMappedFields}
              onCheckedChange={setShowOnlyMappedFields}
            />
            <Label htmlFor="show-mapped-fields" className="cursor-pointer">
              {showOnlyMappedFields ? (
                <span className="flex items-center">
                  <Filter className="h-4 w-4 mr-1" />
                  Show only mapped fields
                </span>
              ) : (
                <span className="flex items-center">
                  <Eye className="h-4 w-4 mr-1" />
                  Show all fields
                </span>
              )}
            </Label>
          </div>
        </div>

        <div className="text-sm text-gray-500">
          {mappings.length} field mappings created
        </div>
      </div>

      {/* Suggested matches */}
      {suggestedMatches.length > 0 && (
        <Card className="bg-yellow-50 border-yellow-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Suggested Matches</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-2">
              {suggestedMatches.map((suggestion, index) => {
                const sourceSchema = schemas.find(s => s.source_index === suggestion.sourceIndex);
                const targetSchema = schemas.find(s => s.source_index === suggestion.targetIndex);

                if (!sourceSchema || !targetSchema) return null;

                return (
                  <div key={index} className="flex items-center justify-between bg-white p-2 rounded border border-yellow-200">
                    <div className="flex items-center space-x-2">
                      <div className="text-sm">
                        <span className="font-medium">{suggestion.columnName}</span>
                        <span className="text-gray-500 text-xs"> ({sourceSchema.source_name})</span>
                      </div>
                      <ArrowRight className="h-4 w-4 text-yellow-500" />
                      <div className="text-sm">
                        <span className="font-medium">{suggestion.targetColumn}</span>
                        <span className="text-gray-500 text-xs"> ({targetSchema.source_name})</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="bg-yellow-100 border-yellow-300 text-yellow-800">
                        {Math.round(suggestion.score * 100)}% match
                      </Badge>
                      <Button size="sm" variant="outline" onClick={() => handleCreateFromSuggestion(suggestion)}>
                        Accept
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Selection panel */}
      {selectedSchema !== null && selectedColumn && (
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-2">
              <div className="font-medium">Creating Field Mapping</div>
              <Button variant="ghost" size="sm" onClick={handleCancelSelection}>
                Cancel
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="text-sm font-medium mb-1">Source Column</div>
                <div className="bg-white p-2 rounded border border-gray-200">
                  <div className="font-medium">{selectedColumn.name}</div>
                  <div className="text-xs text-gray-500">
                    {schemas.find(s => s.source_index === selectedSchema)?.source_name}
                  </div>
                </div>
              </div>

              <div>
                <div className="text-sm font-medium mb-1">Target Column</div>
                <div className="flex flex-col space-y-2">
                  <Select value={targetSchema?.toString() || ''} onValueChange={(value) => setTargetSchema(parseInt(value))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select schema" />
                    </SelectTrigger>
                    <SelectContent>
                      {schemas
                        .filter(schema => schema.source_index !== selectedSchema)
                        .map(schema => (
                          <SelectItem key={schema.source_index} value={schema.source_index.toString()}>
                            {schema.source_name}
                          </SelectItem>
                        ))
                      }
                    </SelectContent>
                  </Select>

                  <Select
                    value={targetColumn}
                    onValueChange={setTargetColumn}
                    disabled={targetSchema === null}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select column" />
                    </SelectTrigger>
                    <SelectContent>
                      {targetSchema !== null && schemas
                        .find(schema => schema.source_index === targetSchema)?.columns
                        .map(column => (
                          <SelectItem key={column.name} value={column.name}>
                            {column.name}
                          </SelectItem>
                        ))
                      }
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <div className="text-sm font-medium mb-1">Semantic Type</div>
                <Select value={semanticType} onValueChange={setSemanticType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.keys(SEMANTIC_TYPE_COLORS).map(type => (
                      <SelectItem key={type} value={type}>
                        {type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button
                  className="w-full mt-2"
                  onClick={handleCreateMapping}
                  disabled={targetSchema === null || !targetColumn}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Mapping
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Schema columns */}
        <div className="md:col-span-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {schemas.map((schema) => (
              <div key={schema.source_index}>
                <h3 className="font-medium mb-2">{schema.source_name}</h3>
                <div className="space-y-2 max-h-[500px] overflow-y-auto pr-2">
                  {schema.columns.map((column) => {
                    // Check if this column is used in any mapping
                    const isUsedInMapping = usedColumns[schema.source_index]?.has(column.name);

                    // Check if this column is a suggested match
                    const suggestion = suggestedMatches.find(
                      s => s.targetIndex === schema.source_index && s.targetColumn === column.name
                    );

                    // Skip if we're only showing mapped fields and this isn't mapped
                    if (showOnlyMappedFields && !isUsedInMapping && !selectedSchema) {
                      return null;
                    }

                    return (
                      <ColumnCard
                        key={column.name}
                        column={column}
                        schema={schema}
                        onSelect={handleColumnSelect}
                        isSelected={selectedSchema === schema.source_index && selectedColumn?.name === column.name}
                        isSuggested={!!suggestion}
                        similarityScore={suggestion?.score || 0}
                        isFiltered={showOnlyMappedFields && !isUsedInMapping && selectedSchema !== null}
                      />
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Mappings */}
        <div>
          <h3 className="font-medium mb-2">Field Mappings</h3>
          <div className="space-y-2 max-h-[500px] overflow-y-auto pr-2">
            {mappings.length === 0 ? (
              <div className="text-center text-gray-500 p-4 border border-dashed rounded">
                No mappings created yet. Click on columns to create mappings.
              </div>
            ) : (
              mappings.map((mapping, index) => (
                <MappingItem
                  key={mapping.id}
                  mapping={mapping}
                  schemas={schemas}
                  onDelete={() => handleDeleteMapping(index)}
                />
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SchemaVisualizer;
