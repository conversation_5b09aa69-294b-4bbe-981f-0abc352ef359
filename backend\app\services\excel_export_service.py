"""
Excel Export Service for generating comprehensive financial reports with reconciliation.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, date
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.utils import get_column_letter
from io import BytesIO

logger = logging.getLogger(__name__)

class ExcelExportService:
    """Service for creating Excel reports with multiple tabs and formulas."""
    
    def __init__(self):
        self.workbook = None
        self.category_mapping = {
            # Revenue mapping
            'Membership Fee Income': 'Sales Income',
            'Mentoring Fee Income': 'Other Operating Income',
            'Income - Fees': 'Sales Income',
            'Income - Revenue': 'Sales Income', 
            'Income - Other': 'Other Operating Income',
            
            # Expense mapping
            'Software Expense': 'Software & IT Services',
            'DataOracle Software Expense': 'Software & IT Services',
            'Marketing Expense': 'Marketing & Advertising',
            'Workshop and Events Expense': 'Travel & Entertainment',
            'Expense - Events': 'Travel & Entertainment',
            'Expense - Transport': 'Travel & Entertainment',
            'Bank Fees': 'Other Operating Expense',
            'Expense - Bank Fees': 'Other Operating Expense',
            'Expense - Office': 'Other Operating Expense',
            'Expense - Other': 'Other Operating Expense',
            'Uncategorized': 'Other Operating Expense'
        }
    
    def create_financial_report(
        self, 
        transactions: List[Dict[str, Any]], 
        report_name: str = "Financial Report",
        start_date: Optional[date] = None,
        end_date: Optional[date] = None
    ) -> BytesIO:
        """
        Create a comprehensive Excel financial report with multiple tabs.
        
        Args:
            transactions: List of categorized transactions
            report_name: Name for the report
            start_date: Optional start date filter
            end_date: Optional end date filter
            
        Returns:
            BytesIO: Excel file as bytes
        """
        logger.info(f"Creating Excel report with {len(transactions)} transactions")
        
        # Create workbook
        self.workbook = Workbook()
        
        # Remove default sheet
        self.workbook.remove(self.workbook.active)
        
        # Create all tabs
        self._create_transaction_data_tab(transactions)
        self._create_category_summary_tab()
        self._create_pl_report_tab(report_name, start_date, end_date)
        self._create_reconciliation_tab()
        
        # Save to BytesIO
        excel_buffer = BytesIO()
        self.workbook.save(excel_buffer)
        excel_buffer.seek(0)
        
        logger.info("Excel report created successfully")
        return excel_buffer
    
    def _create_transaction_data_tab(self, transactions: List[Dict[str, Any]]):
        """Create the Transaction Data tab with all transaction details."""
        ws = self.workbook.create_sheet("Transaction Data")
        
        # Headers
        headers = ['Date', 'Description', 'Amount', 'Category', 'P&L Line Item']
        ws.append(headers)
        
        # Style headers
        for col in range(1, len(headers) + 1):
            cell = ws.cell(row=1, column=col)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # Add transaction data
        for transaction in transactions:
            # Extract data with fallbacks
            date_val = self._extract_date(transaction)
            description = self._extract_description(transaction)
            amount = float(transaction.get('amount', 0))
            category = transaction.get('category', 'Uncategorized')
            pl_line_item = self.category_mapping.get(category, 'Other Operating Expense')
            
            ws.append([date_val, description, amount, category, pl_line_item])
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # Add borders
        self._add_table_borders(ws, len(transactions) + 1, len(headers))
        
        logger.info(f"Created Transaction Data tab with {len(transactions)} rows")
    
    def _create_category_summary_tab(self):
        """Create the Category Summary tab with aggregated data."""
        ws = self.workbook.create_sheet("Category Summary")
        
        # Headers
        headers = ['Category', 'Count', 'Total Amount', 'P&L Line Item']
        ws.append(headers)
        
        # Style headers
        for col in range(1, len(headers) + 1):
            cell = ws.cell(row=1, column=col)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="70AD47", end_color="70AD47", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # Get unique categories from transaction data
        unique_categories = set()
        for category in self.category_mapping.keys():
            unique_categories.add(category)
        unique_categories.add('Uncategorized')  # Ensure uncategorized is included
        
        row = 2
        for category in sorted(unique_categories):
            pl_line_item = self.category_mapping.get(category, 'Other Operating Expense')
            
            # Add formulas that reference Transaction Data tab
            ws.cell(row=row, column=1, value=category)
            ws.cell(row=row, column=2, value=f'=COUNTIF(\'Transaction Data\'!D:D,"{category}")')
            ws.cell(row=row, column=3, value=f'=SUMIF(\'Transaction Data\'!D:D,"{category}",\'Transaction Data\'!C:C)')
            ws.cell(row=row, column=4, value=pl_line_item)
            
            row += 1
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 30)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # Add borders
        self._add_table_borders(ws, len(unique_categories) + 1, len(headers))
        
        logger.info(f"Created Category Summary tab with {len(unique_categories)} categories")
    
    def _extract_date(self, transaction: Dict[str, Any]) -> str:
        """Extract date from transaction with multiple fallbacks."""
        date_fields = ['date', 'Date', 'transaction_date', 'Booking Date', 'posting_date']
        for field in date_fields:
            if field in transaction and transaction[field]:
                return str(transaction[field])
        return datetime.now().strftime('%Y-%m-%d')
    
    def _extract_description(self, transaction: Dict[str, Any]) -> str:
        """Extract description from transaction with multiple fallbacks."""
        desc_fields = ['description', 'Description', 'Booking details', 'details', 'memo']
        for field in desc_fields:
            if field in transaction and transaction[field]:
                return str(transaction[field])
        return 'No description'
    
    def _add_table_borders(self, ws, rows: int, cols: int):
        """Add borders to table."""
        thin_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        for row in range(1, rows + 1):
            for col in range(1, cols + 1):
                ws.cell(row=row, column=col).border = thin_border

    def _create_pl_report_tab(self, report_name: str, start_date: Optional[date], end_date: Optional[date]):
        """Create the P&L Report tab with formatted financial statement."""
        ws = self.workbook.create_sheet("P&L Report", 0)  # Make it the first tab

        # Title
        ws.merge_cells('A1:D1')
        title_cell = ws['A1']
        title_cell.value = f"{report_name.upper()}"
        title_cell.font = Font(size=16, bold=True)
        title_cell.alignment = Alignment(horizontal="center")

        # Date range
        if start_date and end_date:
            ws.merge_cells('A2:D2')
            date_cell = ws['A2']
            date_cell.value = f"For Period: {start_date} to {end_date}"
            date_cell.font = Font(size=12)
            date_cell.alignment = Alignment(horizontal="center")
            current_row = 4
        else:
            current_row = 3

        # REVENUE SECTION
        ws.cell(row=current_row, column=1, value="REVENUE").font = Font(bold=True, size=12)
        current_row += 1

        # Revenue line items with formulas
        revenue_items = [
            ('Sales Income', '=SUMIF(\'Category Summary\'!D:D,"Sales Income",\'Category Summary\'!C:C)'),
            ('Other Operating Income', '=SUMIF(\'Category Summary\'!D:D,"Other Operating Income",\'Category Summary\'!C:C)')
        ]

        for item_name, formula in revenue_items:
            ws.cell(row=current_row, column=2, value=item_name)
            ws.cell(row=current_row, column=3, value=formula)
            current_row += 1

        # Total Revenue
        total_revenue_row = current_row
        ws.cell(row=current_row, column=2, value="TOTAL REVENUE").font = Font(bold=True)
        ws.cell(row=current_row, column=3, value=f"=SUM(C{current_row-len(revenue_items)}:C{current_row-1})").font = Font(bold=True)
        current_row += 2

        # OPERATING EXPENSES SECTION
        ws.cell(row=current_row, column=1, value="OPERATING EXPENSES").font = Font(bold=True, size=12)
        current_row += 1

        # Expense line items with formulas
        expense_items = [
            ('Software & IT Services', '=SUMIF(\'Category Summary\'!D:D,"Software & IT Services",\'Category Summary\'!C:C)'),
            ('Marketing & Advertising', '=SUMIF(\'Category Summary\'!D:D,"Marketing & Advertising",\'Category Summary\'!C:C)'),
            ('Travel & Entertainment', '=SUMIF(\'Category Summary\'!D:D,"Travel & Entertainment",\'Category Summary\'!C:C)'),
            ('Other Operating Expense', '=SUMIF(\'Category Summary\'!D:D,"Other Operating Expense",\'Category Summary\'!C:C)')
        ]

        expense_start_row = current_row
        for item_name, formula in expense_items:
            ws.cell(row=current_row, column=2, value=item_name)
            ws.cell(row=current_row, column=3, value=formula)
            current_row += 1

        # Total Operating Expenses
        total_expense_row = current_row
        ws.cell(row=current_row, column=2, value="TOTAL OPERATING EXPENSES").font = Font(bold=True)
        ws.cell(row=current_row, column=3, value=f"=SUM(C{expense_start_row}:C{current_row-1})").font = Font(bold=True)
        current_row += 2

        # NET PROFIT/LOSS
        ws.cell(row=current_row, column=2, value="NET PROFIT (LOSS)").font = Font(bold=True, size=12)
        net_profit_formula = f"=C{total_revenue_row}+C{total_expense_row}"  # Expenses are negative
        ws.cell(row=current_row, column=3, value=net_profit_formula).font = Font(bold=True, size=12)

        # Format amounts as currency
        for row in range(1, current_row + 1):
            cell = ws.cell(row=row, column=3)
            if cell.value and (isinstance(cell.value, str) and cell.value.startswith('=')):
                cell.number_format = '"$"#,##0.00_);("$"#,##0.00)'

        # Auto-adjust column widths
        ws.column_dimensions['A'].width = 5
        ws.column_dimensions['B'].width = 30
        ws.column_dimensions['C'].width = 20
        ws.column_dimensions['D'].width = 5

        logger.info("Created P&L Report tab")

    def _create_reconciliation_tab(self):
        """Create the Reconciliation tab with validation formulas."""
        ws = self.workbook.create_sheet("Reconciliation")

        # Title
        ws.merge_cells('A1:E1')
        title_cell = ws['A1']
        title_cell.value = "RECONCILIATION SUMMARY"
        title_cell.font = Font(size=14, bold=True)
        title_cell.alignment = Alignment(horizontal="center")

        current_row = 3

        # Summary reconciliation
        ws.cell(row=current_row, column=1, value="SUMMARY RECONCILIATION").font = Font(bold=True, size=12)
        current_row += 2

        # Total transactions count
        ws.cell(row=current_row, column=1, value="Total Transactions in Source Data:")
        ws.cell(row=current_row, column=2, value='=COUNTA(\'Transaction Data\'!A:A)-1')
        current_row += 1

        # Total amount in source data
        ws.cell(row=current_row, column=1, value="Total Amount in Source Data:")
        ws.cell(row=current_row, column=2, value='=SUM(\'Transaction Data\'!C:C)')
        ws.cell(row=current_row, column=2).number_format = '"$"#,##0.00_);("$"#,##0.00)'
        current_row += 1

        # Total in P&L Report
        ws.cell(row=current_row, column=1, value="Net Profit/Loss in P&L Report:")
        ws.cell(row=current_row, column=2, value='=\'P&L Report\'!C' + str(self._find_net_profit_row()))
        ws.cell(row=current_row, column=2).number_format = '"$"#,##0.00_);("$"#,##0.00)'
        current_row += 1

        # Difference (should be 0 for net profit reconciliation)
        ws.cell(row=current_row, column=1, value="Difference (Should be 0):")
        ws.cell(row=current_row, column=2, value=f'=B{current_row-1}-B{current_row-2}')
        ws.cell(row=current_row, column=2).number_format = '"$"#,##0.00_);("$"#,##0.00)'
        current_row += 3

        # Category reconciliation
        ws.cell(row=current_row, column=1, value="CATEGORY RECONCILIATION").font = Font(bold=True, size=12)
        current_row += 2

        # Headers for category reconciliation
        headers = ['Category', 'Source Data', 'Category Summary', 'Difference']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=current_row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
        current_row += 1

        # Category reconciliation rows
        categories = ['Bank Fees', 'Software Expense', 'Marketing Expense', 'Workshop and Events Expense',
                     'Membership Fee Income', 'Mentoring Fee Income', 'Uncategorized']

        for category in categories:
            ws.cell(row=current_row, column=1, value=category)
            ws.cell(row=current_row, column=2, value=f'=SUMIF(\'Transaction Data\'!D:D,"{category}",\'Transaction Data\'!C:C)')
            ws.cell(row=current_row, column=3, value=f'=SUMIF(\'Category Summary\'!A:A,"{category}",\'Category Summary\'!C:C)')
            ws.cell(row=current_row, column=4, value=f'=B{current_row}-C{current_row}')

            # Format as currency
            for col in [2, 3, 4]:
                ws.cell(row=current_row, column=col).number_format = '"$"#,##0.00_);("$"#,##0.00)'

            current_row += 1

        current_row += 2

        # Validation section
        ws.cell(row=current_row, column=1, value="VALIDATION CHECKS").font = Font(bold=True, size=12)
        current_row += 2

        # All categories accounted for
        ws.cell(row=current_row, column=1, value="✓ All categories balanced:")
        ws.cell(row=current_row, column=2, value=f'=IF(ABS(SUM(D{current_row-len(categories)-1}:D{current_row-2}))<0.01,"✓ PASS","❌ FAIL")')
        current_row += 1

        # Transaction count validation
        ws.cell(row=current_row, column=1, value="✓ Transaction count matches:")
        ws.cell(row=current_row, column=2, value='=IF(SUM(\'Category Summary\'!B:B)=COUNTA(\'Transaction Data\'!A:A)-1,"✓ PASS","❌ FAIL")')

        # Auto-adjust column widths
        for col in ['A', 'B', 'C', 'D', 'E']:
            ws.column_dimensions[col].width = 25

        # Add borders to reconciliation table
        self._add_table_borders(ws, current_row, 4)

        logger.info("Created Reconciliation tab")

    def _find_net_profit_row(self) -> int:
        """Find the row number where Net Profit is located in P&L Report."""
        # This is a helper method to find the net profit row
        # For now, we'll estimate based on our structure
        return 20  # Approximate row where net profit will be
