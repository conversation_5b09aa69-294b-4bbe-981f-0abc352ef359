
import React from 'react';

interface ProcessStepProps {
  step: string;
  title: string;
  description: string;
}

const ProcessStep: React.FC<ProcessStepProps> = ({ step, title, description }) => {
  return (
    <div className="flex">
      <div className="mr-4">
        <div className="flex items-center justify-center h-10 w-10 rounded-full bg-teal text-navy font-bold">
          {step}
        </div>
      </div>
      <div>
        <h3 className="text-lg font-semibold text-navy mb-1">{title}</h3>
        <p className="text-gray-600">{description}</p>
      </div>
    </div>
  );
};

export default ProcessStep;
