import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import pandas as pd
import numpy as np
from scipy import stats
from sklearn.ensemble import IsolationForest
from sklearn.neighbors import LocalOutlierFactor
from sklearn.preprocessing import StandardScaler

from app.models.data_source import DataSource
from app.utils.file_handlers import dataframe_to_dict_list
from app.utils.storage_factory import storage_client

logger = logging.getLogger(__name__)


def detect_anomalies(
    data_source: DataSource,
    config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Detect anomalies in data from a data source.

    Args:
        data_source: Data source model
        config: Configuration for anomaly detection

    Returns:
        Dictionary with anomaly detection results
    """
    try:
        # Load data from source
        df = load_dataframe_from_source(data_source)

        # Get anomaly detection methods from config
        methods = config.get("methods", ["statistical"])

        # Get columns to analyze
        columns = config.get("columns", df.select_dtypes(include=[np.number]).columns.tolist())

        # Initialize results
        results = {
            "total_rows": len(df),
            "anomalies_by_method": {},
            "anomalies_by_column": {},
            "anomaly_indices": set(),
            "anomaly_records": []
        }

        # Apply each anomaly detection method
        for method in methods:
            if method == "statistical":
                anomalies, anomaly_details = detect_statistical_anomalies(df, columns, config)
                results["anomalies_by_method"]["statistical"] = len(anomalies)
                update_results(results, anomalies, anomaly_details)

            elif method == "isolation_forest":
                anomalies, anomaly_details = detect_isolation_forest_anomalies(df, columns, config)
                results["anomalies_by_method"]["isolation_forest"] = len(anomalies)
                update_results(results, anomalies, anomaly_details)

            elif method == "local_outlier_factor":
                anomalies, anomaly_details = detect_lof_anomalies(df, columns, config)
                results["anomalies_by_method"]["local_outlier_factor"] = len(anomalies)
                update_results(results, anomalies, anomaly_details)

            elif method == "domain_rules":
                anomalies, anomaly_details = apply_domain_rules(df, config.get("rules", []))
                results["anomalies_by_method"]["domain_rules"] = len(anomalies)
                update_results(results, anomalies, anomaly_details)

            else:
                logger.warning(f"Unknown anomaly detection method: {method}")

        # Convert anomaly indices to list and sort
        results["anomaly_indices"] = sorted(list(results["anomaly_indices"]))

        # Get anomaly records
        anomaly_records = df.iloc[results["anomaly_indices"]].copy() if results["anomaly_indices"] else pd.DataFrame()

        # Add anomaly records to results (limit to 100 for performance)
        results["anomaly_records"] = dataframe_to_dict_list(anomaly_records.head(100))

        # Calculate anomaly percentage
        results["anomaly_percentage"] = (len(results["anomaly_indices"]) / len(df)) * 100 if len(df) > 0 else 0

        # Save anomaly data if specified
        output_path = None
        if config.get("save_output", False) and not anomaly_records.empty:
            output_path = f"anomalies/{data_source.id}/{datetime.now().strftime('%Y%m%d%H%M%S')}.csv"
            save_anomaly_data(anomaly_records, output_path)
            results["output_path"] = output_path

        return results
    except Exception as e:
        logger.error(f"Error detecting anomalies: {e}")
        raise ValueError(f"Error detecting anomalies: {str(e)}")


def load_dataframe_from_source(data_source: DataSource) -> pd.DataFrame:
    """
    Load data from a data source into a pandas DataFrame.

    Args:
        data_source: Data source model

    Returns:
        DataFrame containing the data
    """
    if data_source.source_type == "file":
        # Download file from GCS
        local_path = f"/tmp/{data_source.file_path.split('/')[-1]}"
        storage_client.download_file(data_source.file_path, local_path)

        # Read file based on type
        if data_source.file_type == "csv":
            return pd.read_csv(local_path)
        elif data_source.file_type == "excel":
            return pd.read_excel(local_path)
        elif data_source.file_type == "json":
            return pd.read_json(local_path)
        else:
            raise ValueError(f"Unsupported file type: {data_source.file_type}")
    elif data_source.source_type == "database":
        # Implement database connection logic
        raise NotImplementedError("Database source loading not implemented yet")
    elif data_source.source_type == "cloud_service":
        # Implement cloud service connection logic
        raise NotImplementedError("Cloud service source loading not implemented yet")
    else:
        raise ValueError(f"Unsupported source type: {data_source.source_type}")


def update_results(
    results: Dict[str, Any],
    anomalies: List[int],
    anomaly_details: Dict[str, List[int]]
) -> None:
    """
    Update results dictionary with anomalies.

    Args:
        results: Results dictionary to update
        anomalies: List of anomaly indices
        anomaly_details: Dictionary mapping columns to lists of anomaly indices
    """
    # Update anomaly indices
    results["anomaly_indices"].update(anomalies)

    # Update anomalies by column
    for column, indices in anomaly_details.items():
        if column not in results["anomalies_by_column"]:
            results["anomalies_by_column"][column] = 0
        results["anomalies_by_column"][column] += len(indices)


def detect_statistical_anomalies(
    df: pd.DataFrame,
    columns: List[str],
    config: Dict[str, Any]
) -> Tuple[List[int], Dict[str, List[int]]]:
    """
    Detect anomalies using statistical methods (Z-score, IQR).

    Args:
        df: DataFrame to analyze
        columns: Columns to check for anomalies
        config: Configuration for anomaly detection

    Returns:
        Tuple containing:
        - List of anomaly indices
        - Dictionary mapping columns to lists of anomaly indices
    """
    # Get configuration
    method = config.get("statistical_method", "z_score")
    threshold = config.get("threshold", 3.0)

    # Initialize results
    all_anomalies = []
    anomaly_details = {}

    # Check each column
    for column in columns:
        # Skip if column is not in DataFrame or not numeric
        if column not in df.columns or not pd.api.types.is_numeric_dtype(df[column]):
            continue

        # Get column data without NaN values
        column_data = df[column].dropna()

        # Skip if not enough data
        if len(column_data) < 10:
            continue

        # Detect anomalies based on method
        if method == "z_score":
            # Calculate Z-scores
            z_scores = np.abs(stats.zscore(column_data))

            # Find anomalies
            anomaly_mask = z_scores > threshold
            anomaly_indices = column_data.index[anomaly_mask].tolist()

        elif method == "iqr":
            # Calculate IQR
            q1 = column_data.quantile(0.25)
            q3 = column_data.quantile(0.75)
            iqr = q3 - q1

            # Define bounds
            lower_bound = q1 - (threshold * iqr)
            upper_bound = q3 + (threshold * iqr)

            # Find anomalies
            anomaly_mask = (column_data < lower_bound) | (column_data > upper_bound)
            anomaly_indices = column_data.index[anomaly_mask].tolist()

        else:
            logger.warning(f"Unknown statistical method: {method}")
            continue

        # Add to results
        all_anomalies.extend(anomaly_indices)
        anomaly_details[column] = anomaly_indices

    return all_anomalies, anomaly_details


def detect_isolation_forest_anomalies(
    df: pd.DataFrame,
    columns: List[str],
    config: Dict[str, Any]
) -> Tuple[List[int], Dict[str, List[int]]]:
    """
    Detect anomalies using Isolation Forest algorithm.

    Args:
        df: DataFrame to analyze
        columns: Columns to check for anomalies
        config: Configuration for anomaly detection

    Returns:
        Tuple containing:
        - List of anomaly indices
        - Dictionary mapping columns to lists of anomaly indices
    """
    # Get configuration
    contamination = config.get("contamination", 0.05)
    random_state = config.get("random_state", 42)

    # Filter columns to only include numeric ones
    numeric_columns = [col for col in columns if col in df.columns and pd.api.types.is_numeric_dtype(df[col])]

    # Skip if no numeric columns
    if not numeric_columns:
        return [], {}

    # Get data without NaN values
    data = df[numeric_columns].dropna()

    # Skip if not enough data
    if len(data) < 10:
        return [], {}

    # Scale data
    scaler = StandardScaler()
    scaled_data = scaler.fit_transform(data)

    # Apply Isolation Forest
    model = IsolationForest(
        contamination=contamination,
        random_state=random_state,
        n_estimators=100
    )

    # Fit model and predict
    predictions = model.fit_predict(scaled_data)

    # Anomalies are labeled as -1
    anomaly_mask = predictions == -1
    anomaly_indices = data.index[anomaly_mask].tolist()

    # Create anomaly details (attribute to all columns)
    anomaly_details = {col: anomaly_indices for col in numeric_columns}

    return anomaly_indices, anomaly_details


def detect_lof_anomalies(
    df: pd.DataFrame,
    columns: List[str],
    config: Dict[str, Any]
) -> Tuple[List[int], Dict[str, List[int]]]:
    """
    Detect anomalies using Local Outlier Factor algorithm.

    Args:
        df: DataFrame to analyze
        columns: Columns to check for anomalies
        config: Configuration for anomaly detection

    Returns:
        Tuple containing:
        - List of anomaly indices
        - Dictionary mapping columns to lists of anomaly indices
    """
    # Get configuration
    contamination = config.get("contamination", 0.05)
    n_neighbors = config.get("n_neighbors", 20)

    # Filter columns to only include numeric ones
    numeric_columns = [col for col in columns if col in df.columns and pd.api.types.is_numeric_dtype(df[col])]

    # Skip if no numeric columns
    if not numeric_columns:
        return [], {}

    # Get data without NaN values
    data = df[numeric_columns].dropna()

    # Skip if not enough data
    if len(data) < max(n_neighbors + 1, 10):
        return [], {}

    # Scale data
    scaler = StandardScaler()
    scaled_data = scaler.fit_transform(data)

    # Apply Local Outlier Factor
    model = LocalOutlierFactor(
        n_neighbors=min(n_neighbors, len(data) - 1),
        contamination=contamination
    )

    # Fit model and predict
    predictions = model.fit_predict(scaled_data)

    # Anomalies are labeled as -1
    anomaly_mask = predictions == -1
    anomaly_indices = data.index[anomaly_mask].tolist()

    # Create anomaly details (attribute to all columns)
    anomaly_details = {col: anomaly_indices for col in numeric_columns}

    return anomaly_indices, anomaly_details


def apply_domain_rules(
    df: pd.DataFrame,
    rules: List[Dict[str, Any]]
) -> Tuple[List[int], Dict[str, List[int]]]:
    """
    Apply domain-specific rules to detect anomalies.

    Args:
        df: DataFrame to analyze
        rules: List of rules to apply

    Returns:
        Tuple containing:
        - List of anomaly indices
        - Dictionary mapping columns to lists of anomaly indices
    """
    # Initialize results
    all_anomalies = []
    anomaly_details = {}

    # Apply each rule
    for rule in rules:
        # Get rule details
        column = rule.get("column")
        condition = rule.get("condition")
        value = rule.get("value")

        # Skip if column is not in DataFrame
        if column not in df.columns:
            continue

        # Apply condition
        if condition == "greater_than":
            anomaly_mask = df[column] > value
        elif condition == "less_than":
            anomaly_mask = df[column] < value
        elif condition == "equal_to":
            anomaly_mask = df[column] == value
        elif condition == "not_equal_to":
            anomaly_mask = df[column] != value
        elif condition == "contains":
            if pd.api.types.is_string_dtype(df[column]):
                anomaly_mask = df[column].str.contains(str(value), na=False)
            else:
                continue
        elif condition == "not_contains":
            if pd.api.types.is_string_dtype(df[column]):
                anomaly_mask = ~df[column].str.contains(str(value), na=False)
            else:
                continue
        elif condition == "is_null":
            anomaly_mask = df[column].isna()
        elif condition == "is_not_null":
            anomaly_mask = ~df[column].isna()
        else:
            logger.warning(f"Unknown condition: {condition}")
            continue

        # Get anomaly indices
        anomaly_indices = df.index[anomaly_mask].tolist()

        # Add to results
        all_anomalies.extend(anomaly_indices)

        if column not in anomaly_details:
            anomaly_details[column] = []

        anomaly_details[column].extend(anomaly_indices)

    return all_anomalies, anomaly_details


def save_anomaly_data(df: pd.DataFrame, output_path: str) -> str:
    """
    Save anomaly data to GCS.

    Args:
        df: DataFrame to save
        output_path: Path in GCS to save to

    Returns:
        GCS path where data was saved
    """
    # Save to a temporary file
    local_path = f"/tmp/{output_path.split('/')[-1]}"
    df.to_csv(local_path, index=False)

    # Upload to GCS
    with open(local_path, 'rb') as f:
        storage_client.upload_file(f, output_path)

    return output_path
