import React, { useState } from 'react';
import { useN<PERSON><PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { 
  ArrowLeft, 
  Search, 
  Filter, 
  Download, 
  Clock, 
  User,
  Calendar,
  ChevronDown,
  ChevronUp,
  FileText,
  AlertTriangle,
  CheckCircle,
  Edit
} from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { format } from 'date-fns';

// Mock data for audit trail
const auditLogs = [
  { 
    id: 1, 
    action: 'Data Upload', 
    description: 'Uploaded invoice data file (invoices_q2.csv)', 
    user: '<PERSON>', 
    timestamp: '2023-09-15 14:32:45', 
    category: 'data',
    details: 'File size: 2.4MB, 1,240 records processed'
  },
  { 
    id: 2, 
    action: 'Data Upload', 
    description: 'Uploaded payment data file (payments_q2.csv)', 
    user: 'John Doe', 
    timestamp: '2023-09-15 14:35:12', 
    category: 'data',
    details: 'File size: 2.1MB, 1,180 records processed'
  },
  { 
    id: 3, 
    action: 'Reconciliation', 
    description: 'Started automatic matching process', 
    user: 'System', 
    timestamp: '2023-09-15 14:36:05', 
    category: 'system',
    details: 'Matching algorithm: fuzzy match with 70% confidence threshold'
  },
  { 
    id: 4, 
    action: 'Reconciliation', 
    description: 'Completed matching process', 
    user: 'System', 
    timestamp: '2023-09-15 14:38:22', 
    category: 'system',
    details: '1,170 matches found, 28 anomalies flagged, 42 unmatched records'
  },
  { 
    id: 5, 
    action: 'Record Edit', 
    description: 'Modified Transaction #INV-003', 
    user: 'Jane Smith', 
    timestamp: '2023-09-15 15:10:45', 
    category: 'edit',
    details: 'Changed amount from $2,000.00 to $2,100.75'
  },
  { 
    id: 6, 
    action: 'Record Edit', 
    description: 'Modified Transaction #INV-006', 
    user: 'Jane Smith', 
    timestamp: '2023-09-15 15:12:30', 
    category: 'edit',
    details: 'Changed date from 2023-09-25 to 2023-09-22'
  },
  { 
    id: 7, 
    action: 'Reconciliation', 
    description: 'Finalized reconciliation', 
    user: 'Jane Smith', 
    timestamp: '2023-09-15 15:20:18', 
    category: 'system',
    details: 'Final status: 1,172 matches, 26 anomalies, 42 unmatched'
  },
  { 
    id: 8, 
    action: 'Report', 
    description: 'Generated Monthly Reconciliation Report', 
    user: 'Jane Smith', 
    timestamp: '2023-09-15 15:25:40', 
    category: 'report',
    details: 'Report format: Excel, Template: Monthly Reconciliation'
  },
  { 
    id: 9, 
    action: 'Export', 
    description: 'Exported reconciled data', 
    user: 'Jane Smith', 
    timestamp: '2023-09-15 15:30:12', 
    category: 'export',
    details: 'Format: CSV, File: reconciled_data_q2.csv'
  },
  { 
    id: 10, 
    action: 'System', 
    description: 'Backup created', 
    user: 'System', 
    timestamp: '2023-09-15 16:00:00', 
    category: 'system',
    details: 'Automatic daily backup of reconciliation data'
  },
];

// Mock data for record history
const recordHistory = [
  { 
    id: 1, 
    field: 'Amount', 
    oldValue: '$2,000.00', 
    newValue: '$2,100.75', 
    user: 'Jane Smith', 
    timestamp: '2023-09-15 15:10:45',
    reason: 'Corrected based on invoice PDF'
  },
  { 
    id: 2, 
    field: 'Status', 
    oldValue: 'Anomaly', 
    newValue: 'Matched', 
    user: 'Jane Smith', 
    timestamp: '2023-09-15 15:10:45',
    reason: 'Resolved amount discrepancy'
  },
  { 
    id: 3, 
    field: 'Confidence', 
    oldValue: '65%', 
    newValue: '95%', 
    user: 'System', 
    timestamp: '2023-09-15 15:10:46',
    reason: 'Recalculated after manual edit'
  },
];

const AuditTrail = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  });
  const [userFilter, setUserFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [expandedLog, setExpandedLog] = useState<number | null>(null);
  const [showRecordHistory, setShowRecordHistory] = useState(false);

  // Filter logs based on search and filters
  const filteredLogs = auditLogs.filter(log => {
    // Search filter
    const matchesSearch = searchTerm === '' || 
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.user.toLowerCase().includes(searchTerm.toLowerCase());
    
    // User filter
    const matchesUser = userFilter === 'all' || log.user === userFilter;
    
    // Category filter
    const matchesCategory = categoryFilter === 'all' || log.category === categoryFilter;
    
    // Date filter
    const logDate = new Date(log.timestamp);
    const matchesDate = 
      (!dateRange.from || logDate >= dateRange.from) && 
      (!dateRange.to || logDate <= dateRange.to);
    
    return matchesSearch && matchesUser && matchesCategory && matchesDate;
  });

  const toggleLogExpansion = (logId: number) => {
    if (expandedLog === logId) {
      setExpandedLog(null);
    } else {
      setExpandedLog(logId);
    }
  };

  const getActionBadge = (action: string) => {
    switch (action) {
      case 'Data Upload':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Upload</Badge>;
      case 'Reconciliation':
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">Reconciliation</Badge>;
      case 'Record Edit':
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-100">Edit</Badge>;
      case 'Report':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Report</Badge>;
      case 'Export':
        return <Badge className="bg-teal-100 text-teal-800 hover:bg-teal-100">Export</Badge>;
      case 'System':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-100">System</Badge>;
      default:
        return <Badge>{action}</Badge>;
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'Data Upload':
        return <FileText size={16} className="text-blue-600" />;
      case 'Reconciliation':
        return <CheckCircle size={16} className="text-purple-600" />;
      case 'Record Edit':
        return <Edit size={16} className="text-amber-600" />;
      case 'Report':
        return <BarChart size={16} className="text-green-600" />;
      case 'Export':
        return <Download size={16} className="text-teal-600" />;
      case 'System':
        return <AlertTriangle size={16} className="text-gray-600" />;
      default:
        return <Clock size={16} className="text-gray-600" />;
    }
  };

  return (
    <div className="pt-20 pb-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center mb-8">
          <Button 
            variant="ghost" 
            size="sm" 
            className="mr-4"
            onClick={() => navigate('/data-pipeline')}
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Pipeline
          </Button>
          <h1 className="text-3xl font-bold text-navy">Audit Trail</h1>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                <Input 
                  placeholder="Search logs..." 
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {dateRange.from ? (
                        dateRange.to ? (
                          <>
                            {format(dateRange.from, "LLL dd, y")} -{" "}
                            {format(dateRange.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(dateRange.from, "LLL dd, y")
                        )
                      ) : (
                        <span>Pick a date range</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange.from}
                      selected={dateRange}
                      onSelect={setDateRange}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <Select value={userFilter} onValueChange={setUserFilter}>
                <SelectTrigger>
                  <div className="flex items-center">
                    <User size={16} className="mr-2 text-gray-500" />
                    <SelectValue placeholder="Filter by user" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Users</SelectItem>
                  <SelectItem value="John Doe">John Doe</SelectItem>
                  <SelectItem value="Jane Smith">Jane Smith</SelectItem>
                  <SelectItem value="System">System</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger>
                  <div className="flex items-center">
                    <Filter size={16} className="mr-2 text-gray-500" />
                    <SelectValue placeholder="Filter by category" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="data">Data Upload</SelectItem>
                  <SelectItem value="system">System</SelectItem>
                  <SelectItem value="edit">Edits</SelectItem>
                  <SelectItem value="report">Reports</SelectItem>
                  <SelectItem value="export">Exports</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Audit Logs */}
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Activity Timeline</CardTitle>
            <CardDescription>
              {filteredLogs.length} activities found
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="divide-y divide-gray-200">
              {filteredLogs.map((log) => (
                <div key={log.id} className="p-4 hover:bg-gray-50">
                  <div 
                    className="flex items-start cursor-pointer"
                    onClick={() => toggleLogExpansion(log.id)}
                  >
                    <div className="mr-4 mt-1">
                      {getActionIcon(log.action)}
                    </div>
                    <div className="flex-grow">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <h3 className="text-base font-medium">{log.description}</h3>
                          {getActionBadge(log.action)}
                        </div>
                        <div>
                          {expandedLog === log.id ? (
                            <ChevronUp size={16} className="text-gray-500" />
                          ) : (
                            <ChevronDown size={16} className="text-gray-500" />
                          )}
                        </div>
                      </div>
                      <div className="flex items-center text-sm text-gray-500 mt-1">
                        <User size={14} className="mr-1" />
                        <span>{log.user}</span>
                        <span className="mx-2">•</span>
                        <Clock size={14} className="mr-1" />
                        <span>{log.timestamp}</span>
                      </div>
                    </div>
                  </div>
                  
                  {expandedLog === log.id && (
                    <div className="mt-4 ml-8 pl-4 border-l-2 border-gray-200">
                      <div className="text-sm text-gray-600 mb-2">
                        <strong>Details:</strong> {log.details}
                      </div>
                      
                      {log.action === 'Record Edit' && (
                        <div className="mt-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              setShowRecordHistory(!showRecordHistory);
                            }}
                          >
                            {showRecordHistory ? 'Hide Record History' : 'View Record History'}
                          </Button>
                          
                          {showRecordHistory && (
                            <div className="mt-4 border rounded-md overflow-hidden">
                              <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                  <tr>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Field</th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Old Value</th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">New Value</th>
                                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                                  </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                  {recordHistory.map((record) => (
                                    <tr key={record.id}>
                                      <td className="px-4 py-2 text-sm">{record.field}</td>
                                      <td className="px-4 py-2 text-sm text-red-600">{record.oldValue}</td>
                                      <td className="px-4 py-2 text-sm text-green-600">{record.newValue}</td>
                                      <td className="px-4 py-2 text-sm">{record.reason}</td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Export Logs */}
        <div className="flex justify-between">
          <Button variant="outline" onClick={() => navigate('/data-pipeline')}>
            <ArrowLeft size={16} className="mr-2" />
            Back to Pipeline
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download size={16} />
            Export Audit Log
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AuditTrail;
