
import React from 'react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

interface CallToActionProps {
  title?: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
}

const CallToAction: React.FC<CallToActionProps> = ({
  title = "Ready to transform your financial data operations?",
  description = "Schedule a demo to see how our AI-driven solution can save time, reduce errors, and improve forecasting for your institution.",
  buttonText = "Request a Demo",
  buttonLink = "/contact"
}) => {
  return (
    <section className="bg-gradient-to-r from-navy to-navy/90 text-white">
      <div className="max-w-7xl mx-auto py-16 px-4 sm:py-24 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold tracking-tight sm:text-4xl">
            {title}
          </h2>
          <p className="mt-4 text-lg leading-6 max-w-2xl mx-auto">
            {description}
          </p>
          <div className="mt-10">
            <Link to={buttonLink}>
              <Button size="lg" className="bg-teal hover:bg-teal/90 text-navy font-semibold">
                {buttonText}
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CallToAction;
