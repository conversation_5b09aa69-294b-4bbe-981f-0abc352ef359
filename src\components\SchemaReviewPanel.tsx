import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Check, AlertTriangle, Info, Edit, Save, HelpCircle, Eye, Calendar, DollarSign, Tag, FileText, User } from 'lucide-react';

interface FieldMapping {
  source_field: string;
  target_field: string;
  semantic_type: string;
  is_income: boolean | null;
  confidence: number;
  sample_values?: string[];
  reasoning?: string[];
}

interface SchemaReviewPanelProps {
  fieldMappings: FieldMapping[];
  onUpdateMapping: (updatedMapping: FieldMapping[]) => void;
  onConfirm: () => void;
  isLoading?: boolean;
}

const SchemaReviewPanel: React.FC<SchemaReviewPanelProps> = ({
  fieldMappings,
  onUpdateMapping,
  onConfirm,
  isLoading = false
}) => {
  const [editMode, setEditMode] = useState(false);
  const [editedMappings, setEditedMappings] = useState<FieldMapping[]>(fieldMappings);

  const handleEdit = () => {
    setEditMode(true);
    setEditedMappings([...fieldMappings]);
  };

  const handleSave = () => {
    setEditMode(false);
    onUpdateMapping(editedMappings);
  };

  const handleCancel = () => {
    setEditMode(false);
    setEditedMappings([...fieldMappings]);
  };

  const handleFieldTypeChange = (index: number, value: string) => {
    const updated = [...editedMappings];
    updated[index].semantic_type = value;
    updated[index].target_field = getDefaultTargetField(value);
    setEditedMappings(updated);
  };

  const handleIncomeChange = (index: number, value: string) => {
    const updated = [...editedMappings];
    if (value === 'income') {
      updated[index].is_income = true;
    } else if (value === 'expense') {
      updated[index].is_income = false;
    } else {
      updated[index].is_income = null;
    }
    setEditedMappings(updated);
  };

  const getDefaultTargetField = (semanticType: string): string => {
    switch (semanticType) {
      case 'date':
        return 'transaction_date';
      case 'amount':
        return 'amount';
      case 'category':
        return 'category';
      case 'description':
        return 'description';
      case 'account_or_payee':
        return 'account_or_payee';
      default:
        return 'unknown';
    }
  };

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 0.8) {
      return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">High</Badge>;
    } else if (confidence >= 0.6) {
      return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Medium</Badge>;
    } else {
      return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Low</Badge>;
    }
  };

  // Get the appropriate icon for a semantic type
  const getSemanticTypeIcon = (type: string) => {
    switch (type) {
      case 'date':
        return <Calendar size={16} className="text-blue-500" />;
      case 'amount':
        return <DollarSign size={16} className="text-green-500" />;
      case 'category':
        return <Tag size={16} className="text-purple-500" />;
      case 'description':
        return <FileText size={16} className="text-orange-500" />;
      case 'account_or_payee':
        return <User size={16} className="text-indigo-500" />;
      default:
        return <HelpCircle size={16} className="text-gray-500" />;
    }
  };

  // Format sample values for display
  const formatSampleValue = (value: any, type: string) => {
    if (value === null || value === undefined) return 'N/A';

    if (type === 'date') {
      // Try to format as a date if possible
      try {
        const date = new Date(value);
        if (!isNaN(date.getTime())) {
          return date.toLocaleDateString();
        }
      } catch {}
    } else if (type === 'amount') {
      // Try to format as currency if possible
      try {
        const num = parseFloat(value);
        if (!isNaN(num)) {
          return num.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
        }
      } catch {}
    }

    // Default to string representation
    return String(value);
  };

  // Get a description of what the semantic type means
  const getTypeDescription = (type: string) => {
    switch (type) {
      case 'date':
        return 'Transaction date when the financial activity occurred';
      case 'amount':
        return 'Monetary value of the transaction';
      case 'category':
        return 'Classification or type of the transaction';
      case 'description':
        return 'Details about the transaction';
      case 'account_or_payee':
        return 'The account involved or the recipient/sender of the transaction';
      default:
        return 'Unknown field type';
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span>AI Schema Detection</span>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-6 w-6">
                    <HelpCircle size={14} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p>AI has analyzed your data and detected the meaning of each column. Review and adjust these mappings to ensure accurate financial analysis.</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          {!editMode ? (
            <Button variant="outline" size="sm" onClick={handleEdit}>
              <Edit size={16} className="mr-2" />
              Edit Mappings
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                Cancel
              </Button>
              <Button variant="default" size="sm" onClick={handleSave}>
                <Save size={16} className="mr-2" />
                Save Changes
              </Button>
            </div>
          )}
        </CardTitle>
        <CardDescription>
          Review and adjust how the AI has mapped your data fields. Each field shows sample values from your data.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-start gap-2">
            <Info size={18} className="text-blue-500 mt-0.5" />
            <div>
              <h3 className="font-medium text-blue-700">Why is this important?</h3>
              <p className="text-sm text-blue-600">Accurate field mapping ensures your financial data is correctly interpreted. The AI has made its best guess, but you can adjust if needed.</p>
            </div>
          </div>
        </div>

        <Accordion type="multiple" className="mb-6">
          {(editMode ? editedMappings : fieldMappings).map((mapping, index) => (
            <AccordionItem key={index} value={`item-${index}`} className="border rounded-md mb-2 overflow-hidden">
              <AccordionTrigger className="px-4 py-3 hover:bg-gray-50">
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center gap-3">
                    {getSemanticTypeIcon(mapping.semantic_type)}
                    <span className="font-medium">{mapping.source_field}</span>
                    <Badge variant="secondary" className="ml-2">
                      {mapping.semantic_type.charAt(0).toUpperCase() + mapping.semantic_type.slice(1)}
                    </Badge>
                    {mapping.semantic_type === 'amount' && mapping.is_income !== null && (
                      mapping.is_income === true ? (
                        <Badge className="bg-green-50 text-green-700 border-green-200">Income</Badge>
                      ) : (
                        <Badge className="bg-red-50 text-red-700 border-red-200">Expense</Badge>
                      )
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    {getConfidenceBadge(mapping.confidence)}
                    <Eye size={16} className="text-gray-400" />
                  </div>
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="px-4 py-3 bg-gray-50 border-t">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="text-sm font-medium mb-2">Field Details</h4>
                      <div className="space-y-2">
                        <div>
                          <span className="text-sm text-gray-500">Source Field:</span>
                          <span className="text-sm ml-2 font-medium">{mapping.source_field}</span>
                        </div>
                        <div>
                          <span className="text-sm text-gray-500">Detected Type:</span>
                          <div className="flex items-center gap-2 mt-1">
                            {editMode ? (
                              <Select
                                value={mapping.semantic_type}
                                onValueChange={(value) => handleFieldTypeChange(index, value)}
                              >
                                <SelectTrigger className="w-full">
                                  <SelectValue placeholder="Select type" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="date">
                                    <div className="flex items-center gap-2">
                                      <Calendar size={14} />
                                      <span>Date</span>
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="amount">
                                    <div className="flex items-center gap-2">
                                      <DollarSign size={14} />
                                      <span>Amount</span>
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="category">
                                    <div className="flex items-center gap-2">
                                      <Tag size={14} />
                                      <span>Category</span>
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="description">
                                    <div className="flex items-center gap-2">
                                      <FileText size={14} />
                                      <span>Description</span>
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="account_or_payee">
                                    <div className="flex items-center gap-2">
                                      <User size={14} />
                                      <span>Account/Payee</span>
                                    </div>
                                  </SelectItem>
                                  <SelectItem value="unknown">
                                    <div className="flex items-center gap-2">
                                      <HelpCircle size={14} />
                                      <span>Unknown</span>
                                    </div>
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            ) : (
                              <div className="flex items-center gap-2">
                                {getSemanticTypeIcon(mapping.semantic_type)}
                                <span className="text-sm">{mapping.semantic_type.charAt(0).toUpperCase() + mapping.semantic_type.slice(1)}</span>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Button variant="ghost" size="icon" className="h-6 w-6">
                                        <HelpCircle size={14} />
                                      </Button>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>{getTypeDescription(mapping.semantic_type)}</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            )}
                          </div>
                        </div>
                        {mapping.semantic_type === 'amount' && (
                          <div>
                            <span className="text-sm text-gray-500">Income/Expense:</span>
                            <div className="flex items-center gap-2 mt-1">
                              {editMode ? (
                                <Select
                                  value={mapping.is_income === true ? 'income' : mapping.is_income === false ? 'expense' : 'unknown'}
                                  onValueChange={(value) => handleIncomeChange(index, value)}
                                >
                                  <SelectTrigger className="w-full">
                                    <SelectValue placeholder="Select type" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="income">Income</SelectItem>
                                    <SelectItem value="expense">Expense</SelectItem>
                                    <SelectItem value="unknown">Unknown</SelectItem>
                                  </SelectContent>
                                </Select>
                              ) : (
                                <div>
                                  {mapping.is_income === true ? (
                                    <Badge className="bg-green-50 text-green-700 border-green-200">Income</Badge>
                                  ) : mapping.is_income === false ? (
                                    <Badge className="bg-red-50 text-red-700 border-red-200">Expense</Badge>
                                  ) : (
                                    <Badge variant="outline">Unknown</Badge>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                        <div>
                          <span className="text-sm text-gray-500">Confidence:</span>
                          <div className="mt-1">
                            {getConfidenceBadge(mapping.confidence)}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium mb-2">Sample Values</h4>
                      <div className="bg-white border rounded-md p-2 max-h-40 overflow-y-auto">
                        {mapping.sample_values && mapping.sample_values.length > 0 ? (
                          <ul className="space-y-1">
                            {mapping.sample_values.map((value, i) => (
                              <li key={i} className="text-sm py-1 px-2 odd:bg-gray-50 rounded">
                                {formatSampleValue(value, mapping.semantic_type)}
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-gray-500 italic">No sample values available</p>
                        )}
                      </div>
                      {mapping.reasoning && mapping.reasoning.length > 0 && (
                        <div className="mt-3">
                          <h4 className="text-sm font-medium mb-1">AI Reasoning</h4>
                          <ul className="text-xs text-gray-600 space-y-1">
                            {mapping.reasoning.map((reason, i) => (
                              <li key={i} className="flex items-start gap-1">
                                <span className="text-blue-500 mt-0.5">•</span>
                                <span>{reason}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>

        <div className="mt-6 flex justify-end">
          <Button onClick={onConfirm} disabled={isLoading} className="bg-blue-600 hover:bg-blue-700">
            {isLoading ? (
              <>Loading...</>
            ) : (
              <>
                <Check size={16} className="mr-2" />
                Confirm and Continue
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default SchemaReviewPanel;
