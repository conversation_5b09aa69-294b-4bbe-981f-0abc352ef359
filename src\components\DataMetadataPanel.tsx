import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from './ui/card';
import { Badge } from './ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from './ui/tabs';
import {
  FileSpreadsheet,
  Calendar,
  DollarSign,
  Tag,
  FileText,
  User,
  Bar<PERSON>hart,
  AlertTriangle,
  Info
} from 'lucide-react';

interface FieldMetadata {
  name: string;
  type: string;
  semantic_type?: string;
  confidence?: number;
  sample_values?: any[];
  description?: string;
}

interface DataMetadataPanelProps {
  fields: FieldMetadata[];
  dataSource: any;
  dataSummary: any;
}

const DataMetadataPanel: React.FC<DataMetadataPanelProps> = ({
  fields,
  dataSource,
  dataSummary
}) => {
  // Group fields by semantic type
  const fieldsByType: Record<string, FieldMetadata[]> = {
    date: [],
    amount: [],
    category: [],
    description: [],
    account: [],
    other: []
  };

  fields.forEach(field => {
    const semanticType = field.semantic_type || 'unknown';
    if (semanticType.includes('date')) {
      fieldsByType.date.push(field);
    } else if (semanticType.includes('amount')) {
      fieldsByType.amount.push(field);
    } else if (semanticType.includes('category')) {
      fieldsByType.category.push(field);
    } else if (semanticType.includes('description')) {
      fieldsByType.description.push(field);
    } else if (semanticType.includes('account') || semanticType.includes('payee')) {
      fieldsByType.account.push(field);
    } else {
      fieldsByType.other.push(field);
    }
  });

  // Get field type counts
  const typeCounts = {
    numerical: fields.filter(f => f.type === 'numerical').length,
    categorical: fields.filter(f => f.type === 'categorical').length,
    date: fields.filter(f => f.type === 'date').length,
    text: fields.filter(f => f.type === 'text').length,
    unknown: fields.filter(f => f.type === 'unknown').length
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Info className="h-5 w-5 text-blue-500" />
          Data Classification & Metadata
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="summary">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="fields">Field Classification</TabsTrigger>
            <TabsTrigger value="metadata">File Metadata</TabsTrigger>
          </TabsList>

          <TabsContent value="summary" className="pt-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">Field Types</p>
                      <div className="flex flex-wrap gap-2 mt-2">
                        <Badge variant="outline" className="bg-blue-50">
                          <BarChart className="h-3 w-3 mr-1 text-blue-500" />
                          {typeCounts.numerical} Numerical
                        </Badge>
                        <Badge variant="outline" className="bg-green-50">
                          <Tag className="h-3 w-3 mr-1 text-green-500" />
                          {typeCounts.categorical} Categorical
                        </Badge>
                        <Badge variant="outline" className="bg-amber-50">
                          <Calendar className="h-3 w-3 mr-1 text-amber-500" />
                          {typeCounts.date} Date
                        </Badge>
                        <Badge variant="outline" className="bg-purple-50">
                          <FileText className="h-3 w-3 mr-1 text-purple-500" />
                          {typeCounts.text} Text
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">Semantic Fields</p>
                      <div className="flex flex-wrap gap-2 mt-2">
                        <Badge variant="outline" className="bg-amber-50">
                          <Calendar className="h-3 w-3 mr-1 text-amber-500" />
                          {fieldsByType.date.length} Date
                        </Badge>
                        <Badge variant="outline" className="bg-green-50">
                          <DollarSign className="h-3 w-3 mr-1 text-green-500" />
                          {fieldsByType.amount.length} Amount
                        </Badge>
                        <Badge variant="outline" className="bg-blue-50">
                          <Tag className="h-3 w-3 mr-1 text-blue-500" />
                          {fieldsByType.category.length} Category
                        </Badge>
                        <Badge variant="outline" className="bg-purple-50">
                          <FileText className="h-3 w-3 mr-1 text-purple-500" />
                          {fieldsByType.description.length} Description
                        </Badge>
                        <Badge variant="outline" className="bg-red-50">
                          <User className="h-3 w-3 mr-1 text-red-500" />
                          {fieldsByType.account.length} Account/Payee
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-gray-500">Data Quality</p>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {dataSummary && dataSummary.missing_values && Object.keys(dataSummary.missing_values).length > 0 ? (
                          <Badge variant="outline" className="bg-red-50">
                            <AlertTriangle className="h-3 w-3 mr-1 text-red-500" />
                            {Object.keys(dataSummary.missing_values).length} Fields with Missing Values
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-green-50">
                            <Info className="h-3 w-3 mr-1 text-green-500" />
                            No Missing Values
                          </Badge>
                        )}

                        {dataSummary && dataSummary.numerical_columns && Object.keys(dataSummary.numerical_columns).length > 0 && (
                          <Badge variant="outline" className="bg-blue-50">
                            <BarChart className="h-3 w-3 mr-1 text-blue-500" />
                            {Object.keys(dataSummary.numerical_columns).length} Analyzable Fields
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="fields" className="pt-4">
            <div className="space-y-4">
              {fieldsByType.date.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium flex items-center mb-2">
                    <Calendar className="h-4 w-4 mr-1 text-amber-500" />
                    Date Fields
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {fieldsByType.date.map(field => (
                      <Card key={field.name} className="p-2">
                        <div className="flex justify-between">
                          <span className="font-medium">{field.name}</span>
                          {field.confidence && (
                            <Badge variant={field.confidence > 0.7 ? "default" : "outline"}>
                              {Math.round(field.confidence * 100)}%
                            </Badge>
                          )}
                        </div>
                        {field.sample_values && field.sample_values.length > 0 && (
                          <div className="text-xs text-gray-500 mt-1 truncate">
                            Example: {field.sample_values[0]}
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {fieldsByType.amount.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium flex items-center mb-2">
                    <DollarSign className="h-4 w-4 mr-1 text-green-500" />
                    Amount Fields
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {fieldsByType.amount.map(field => (
                      <Card key={field.name} className="p-2">
                        <div className="flex justify-between">
                          <span className="font-medium">{field.name}</span>
                          {field.confidence && (
                            <Badge variant={field.confidence > 0.7 ? "default" : "outline"}>
                              {Math.round(field.confidence * 100)}%
                            </Badge>
                          )}
                        </div>
                        {field.sample_values && field.sample_values.length > 0 && (
                          <div className="text-xs text-gray-500 mt-1 truncate">
                            Example: {field.sample_values[0]}
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {fieldsByType.category.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium flex items-center mb-2">
                    <Tag className="h-4 w-4 mr-1 text-blue-500" />
                    Category Fields
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {fieldsByType.category.map(field => (
                      <Card key={field.name} className="p-2">
                        <div className="flex justify-between">
                          <span className="font-medium">{field.name}</span>
                          {field.confidence && (
                            <Badge variant={field.confidence > 0.7 ? "default" : "outline"}>
                              {Math.round(field.confidence * 100)}%
                            </Badge>
                          )}
                        </div>
                        {field.sample_values && field.sample_values.length > 0 && (
                          <div className="text-xs text-gray-500 mt-1 truncate">
                            Example: {field.sample_values[0]}
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {fieldsByType.description.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium flex items-center mb-2">
                    <FileText className="h-4 w-4 mr-1 text-purple-500" />
                    Description Fields
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {fieldsByType.description.map(field => (
                      <Card key={field.name} className="p-2">
                        <div className="flex justify-between">
                          <span className="font-medium">{field.name}</span>
                          {field.confidence && (
                            <Badge variant={field.confidence > 0.7 ? "default" : "outline"}>
                              {Math.round(field.confidence * 100)}%
                            </Badge>
                          )}
                        </div>
                        {field.sample_values && field.sample_values.length > 0 && (
                          <div className="text-xs text-gray-500 mt-1 truncate">
                            Example: {field.sample_values[0]}
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {fieldsByType.account.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium flex items-center mb-2">
                    <User className="h-4 w-4 mr-1 text-red-500" />
                    Account/Payee Fields
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {fieldsByType.account.map(field => (
                      <Card key={field.name} className="p-2">
                        <div className="flex justify-between">
                          <span className="font-medium">{field.name}</span>
                          {field.confidence && (
                            <Badge variant={field.confidence > 0.7 ? "default" : "outline"}>
                              {Math.round(field.confidence * 100)}%
                            </Badge>
                          )}
                        </div>
                        {field.sample_values && field.sample_values.length > 0 && (
                          <div className="text-xs text-gray-500 mt-1 truncate">
                            Example: {field.sample_values[0]}
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {fieldsByType.other.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium flex items-center mb-2">
                    <Info className="h-4 w-4 mr-1 text-gray-500" />
                    Other Fields
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                    {fieldsByType.other.map(field => (
                      <Card key={field.name} className="p-2">
                        <div className="flex justify-between">
                          <span className="font-medium">{field.name}</span>
                          <Badge variant="outline">{field.type || 'unknown'}</Badge>
                        </div>
                        {field.sample_values && field.sample_values.length > 0 && (
                          <div className="text-xs text-gray-500 mt-1 truncate">
                            Example: {field.sample_values[0]}
                          </div>
                        )}
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="metadata" className="pt-4">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-sm font-medium flex items-center mb-2">
                      <FileSpreadsheet className="h-4 w-4 mr-1 text-blue-500" />
                      File Information
                    </h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">File Name</span>
                        <span className="text-sm font-medium">{dataSource?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">File Type</span>
                        <span className="text-sm font-medium">{dataSource?.file_type?.toUpperCase()}</span>
                      </div>
                      {dataSource?.sheet_name && (
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">Sheet Name</span>
                          <span className="text-sm font-medium">{dataSource.sheet_name}</span>
                        </div>
                      )}

                      {dataSource?.header_metadata && (
                        <>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Header Row</span>
                            <span className="text-sm font-medium">
                              Row {dataSource.header_metadata.detected_header_row + 1}
                              {dataSource.header_metadata.generated_column_names && (
                                <Badge variant="outline" className="ml-2 text-amber-600 bg-amber-50">
                                  Auto-generated
                                </Badge>
                              )}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">Header Detection</span>
                            <span className="text-sm font-medium">
                              {Math.round(dataSource.header_metadata.header_score)} / 100
                            </span>
                          </div>
                        </>
                      )}
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Created</span>
                        <span className="text-sm font-medium">
                          {dataSource?.created_at ? new Date(dataSource.created_at).toLocaleString() : 'Unknown'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-sm font-medium flex items-center mb-2">
                      <BarChart className="h-4 w-4 mr-1 text-green-500" />
                      Data Statistics
                    </h3>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Total Rows</span>
                        <span className="text-sm font-medium">{dataSource?.stats?.total_rows?.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Cleaned Rows</span>
                        <span className="text-sm font-medium">{dataSource?.stats?.cleaned_rows?.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Total Columns</span>
                        <span className="text-sm font-medium">{dataSource?.stats?.columns?.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Duplicates Removed</span>
                        <span className="text-sm font-medium">{dataSource?.stats?.duplicates_removed?.toLocaleString()}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {dataSummary && dataSummary.top_correlations && dataSummary.top_correlations.length > 0 && (
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="text-sm font-medium flex items-center mb-2">
                      <BarChart className="h-4 w-4 mr-1 text-purple-500" />
                      Top Correlations
                    </h3>
                    <div className="space-y-2">
                      {dataSummary.top_correlations.map((corr: any, index: number) => (
                        <div key={index} className="flex justify-between">
                          <span className="text-sm text-gray-500">
                            {corr.column1} ↔ {corr.column2}
                          </span>
                          <Badge
                            variant={corr.correlation > 0.7 ? "default" : "outline"}
                            className={corr.correlation > 0.7 ? "bg-green-100 text-green-800" : ""}
                          >
                            {(corr.correlation * 100).toFixed(1)}%
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default DataMetadataPanel;
