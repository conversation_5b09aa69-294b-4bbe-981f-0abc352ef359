import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import {
  ArrowLeft,
  ArrowRight,
  AlertTriangle,
  Loader2,
  Settings,
  BarChart4,
  FileText,
  ListFilter
} from 'lucide-react';

import { DomainRulesForm } from '@/components/DataCleaningVisualizations';
import anomalyDetectionService, {
  AnomalyDetectionMethod,
  StatisticalMethod,
  DomainRule as ApiDomainRule,
  JobStatus
} from '@/services/anomalyDetectionService';
import JobStatusCard from '@/components/AnomalyDetection/JobStatusCard';

interface DomainRule {
  id: string;
  column: string;
  condition: string;
  value: string;
}

const AnomalyDetectionConfig: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('methods');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<JobStatus | null>(null);
  const [jobPollingInterval, setJobPollingInterval] = useState<NodeJS.Timeout | null>(null);

  // State for configuration
  const [dataSourceId, setDataSourceId] = useState<number | null>(null);
  const [pipelineId, setPipelineId] = useState<number | null>(null);
  const [columns, setColumns] = useState<string[]>([]);
  const [selectedMethods, setSelectedMethods] = useState<string[]>(['statistical', 'isolation_forest']);
  const [statisticalMethod, setStatisticalMethod] = useState<string>('z_score');
  const [threshold, setThreshold] = useState<number>(3.0);
  const [contamination, setContamination] = useState<number>(0.05);
  const [nNeighbors, setNNeighbors] = useState<number>(20);
  const [selectedColumns, setSelectedColumns] = useState<string[]>([]);
  const [domainRules, setDomainRules] = useState<DomainRule[]>([]);

  // Extract data source and pipeline IDs from URL query params
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const dsId = params.get('dataSourceId');
    const plId = params.get('pipelineId');
    const jobId = params.get('jobId');

    if (dsId) setDataSourceId(parseInt(dsId));
    if (plId) setPipelineId(parseInt(plId));

    // If we have a job ID, check its status
    if (jobId) {
      checkJobStatus(jobId);
    }
    // If we have both IDs, fetch data source schema
    else if (dsId && plId) {
      fetchDataSourceSchema(parseInt(dsId));
    }

    // Cleanup polling interval when component unmounts
    return () => {
      if (jobPollingInterval) {
        clearInterval(jobPollingInterval);
      }
    };
  }, [location.search]);

  // Fetch data source schema to get columns
  const fetchDataSourceSchema = async (dsId: number) => {
    setIsLoading(true);
    setError(null);

    try {
      // This would be a real API call in a production app
      // For now, we'll use mock data
      setTimeout(() => {
        const mockColumns = [
          'invoice_id', 'date', 'amount', 'customer', 'status',
          'payment_method', 'reference', 'description', 'tax_amount'
        ];
        setColumns(mockColumns);
        setSelectedColumns(mockColumns.filter(col => ['amount', 'date'].includes(col)));
        setIsLoading(false);
      }, 1000);
    } catch (err) {
      console.error('Error fetching data source schema:', err);
      setError('Failed to fetch data source schema. Please try again.');
      setIsLoading(false);
    }
  };

  // Check the status of an anomaly detection job
  const checkJobStatus = async (jobId: string) => {
    try {
      const status = await anomalyDetectionService.checkJobStatus(jobId);
      setJobStatus(status);

      // If the job is still running, start polling for updates
      if (status.status === 'pending' || status.status === 'processing') {
        startJobStatusPolling(jobId);
      }
      // If the job is completed, redirect to results page
      else if (status.status === 'completed') {
        toast({
          title: "Anomaly detection complete",
          description: "You will be redirected to the results page.",
        });

        // Redirect to results page with the job ID
        setTimeout(() => {
          navigate(`/data-cleaning?dataSourceId=${dataSourceId}&pipelineId=${pipelineId}&jobId=${jobId}`);
        }, 2000);
      }
      // If the job failed, show an error
      else if (status.status === 'failed') {
        setError(`Anomaly detection failed: ${status.message || 'Unknown error'}`);
        toast({
          variant: "destructive",
          title: "Anomaly detection failed",
          description: status.message || "The anomaly detection job failed. Please try again.",
        });
      }
    } catch (err) {
      console.error('Error checking job status:', err);
      setError('Failed to check job status. Please try again.');
    }
  };

  // Start polling for job status updates
  const startJobStatusPolling = (jobId: string) => {
    // Clear any existing polling interval
    if (jobPollingInterval) {
      clearInterval(jobPollingInterval);
    }

    // Set up a new polling interval
    const interval = setInterval(async () => {
      try {
        const status = await anomalyDetectionService.checkJobStatus(jobId);
        setJobStatus(status);

        // If the job is completed or failed, stop polling
        if (status.status === 'completed' || status.status === 'failed') {
          clearInterval(interval);
          setJobPollingInterval(null);

          // If the job is completed, redirect to results page
          if (status.status === 'completed') {
            toast({
              title: "Anomaly detection complete",
              description: "You will be redirected to the results page.",
            });

            // Redirect to results page with the job ID
            setTimeout(() => {
              navigate(`/data-cleaning?dataSourceId=${dataSourceId}&pipelineId=${pipelineId}&jobId=${jobId}`);
            }, 2000);
          }
          // If the job failed, show an error
          else if (status.status === 'failed') {
            setError(`Anomaly detection failed: ${status.message || 'Unknown error'}`);
            toast({
              variant: "destructive",
              title: "Anomaly detection failed",
              description: status.message || "The anomaly detection job failed. Please try again.",
            });
          }
        }
      } catch (err) {
        console.error('Error polling job status:', err);
        clearInterval(interval);
        setJobPollingInterval(null);
      }
    }, 5000); // Poll every 5 seconds

    setJobPollingInterval(interval);
  };

  // Handle canceling an anomaly detection job
  const handleCancelJob = async () => {
    if (jobStatus) {
      try {
        await anomalyDetectionService.cancelJob(jobStatus.job_id);
        toast({
          title: "Job canceled",
          description: "The anomaly detection job has been canceled.",
        });

        // Clear the job status and stop polling
        setJobStatus(null);
        if (jobPollingInterval) {
          clearInterval(jobPollingInterval);
          setJobPollingInterval(null);
        }

        // Remove job ID from URL
        const params = new URLSearchParams(location.search);
        params.delete('jobId');
        navigate(`${location.pathname}?${params.toString()}`, { replace: true });
      } catch (err) {
        console.error('Error canceling job:', err);
        toast({
          variant: "destructive",
          title: "Error canceling job",
          description: "There was a problem canceling the anomaly detection job.",
        });
      }
    }
  };

  // Run anomaly detection with current configuration
  const runAnomalyDetection = async () => {
    if (!dataSourceId || !pipelineId) {
      toast({
        variant: "destructive",
        title: "Missing information",
        description: "Data source or pipeline ID is missing.",
      });
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Convert domain rules to the format expected by the API
      const apiRules: ApiDomainRule[] = domainRules.map(rule => ({
        column: rule.column,
        condition: rule.condition as any,
        value: rule.condition === 'is_null' || rule.condition === 'is_not_null'
          ? null
          : isNaN(Number(rule.value)) ? rule.value : Number(rule.value)
      }));

      // Create configuration object
      const config = {
        methods: selectedMethods as AnomalyDetectionMethod[],
        columns: selectedColumns,
        statistical_method: statisticalMethod as StatisticalMethod,
        threshold: threshold,
        contamination: contamination,
        n_neighbors: nNeighbors,
        save_output: true,
        rules: apiRules
      };

      // Call the API to start an asynchronous job
      const job = await anomalyDetectionService.detectAnomalies(dataSourceId, pipelineId, config);
      setJobStatus(job);

      // Update URL to include job ID
      const params = new URLSearchParams(location.search);
      params.set('jobId', job.job_id);
      navigate(`${location.pathname}?${params.toString()}`, { replace: true });

      toast({
        title: "Anomaly detection started",
        description: `Job ID: ${job.job_id}. You can track the progress here.`,
      });

      // Start polling for job status updates
      startJobStatusPolling(job.job_id);
    } catch (err) {
      console.error('Error running anomaly detection:', err);
      setError('Failed to run anomaly detection. Please try again.');
      toast({
        variant: "destructive",
        title: "Error detecting anomalies",
        description: "There was a problem running anomaly detection.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="pt-20 pb-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center mb-8">
          <Button
            variant="ghost"
            size="sm"
            className="mr-4"
            onClick={() => navigate('/data-cleaning')}
          >
            <ArrowLeft size={16} className="mr-2" />
            Back to Data Cleaning
          </Button>
          <h1 className="text-3xl font-bold text-navy">Anomaly Detection Configuration</h1>
        </div>

        {isLoading && (
          <div className="flex flex-col items-center justify-center py-16">
            <Loader2 size={48} className="animate-spin text-teal mb-4" />
            <p className="text-lg text-gray-600">Loading...</p>
          </div>
        )}

        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Show job status card if there's an active job */}
        {jobStatus && (jobStatus.status === 'pending' || jobStatus.status === 'processing') && (
          <JobStatusCard
            job={jobStatus}
            onRefresh={() => checkJobStatus(jobStatus.job_id)}
            onCancel={handleCancelJob}
            onViewResults={() => {
              if (jobStatus.result_id) {
                navigate(`/data-cleaning?dataSourceId=${dataSourceId}&pipelineId=${pipelineId}&jobId=${jobStatus.job_id}`);
              }
            }}
          />
        )}

        {!isLoading && !error && !jobStatus && (
          <>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-4 mb-8">
                <TabsTrigger value="methods" className="flex items-center">
                  <Settings size={16} className="mr-2" />
                  Methods
                </TabsTrigger>
                <TabsTrigger value="parameters" className="flex items-center">
                  <BarChart4 size={16} className="mr-2" />
                  Parameters
                </TabsTrigger>
                <TabsTrigger value="columns" className="flex items-center">
                  <FileText size={16} className="mr-2" />
                  Columns
                </TabsTrigger>
                <TabsTrigger value="rules" className="flex items-center">
                  <ListFilter size={16} className="mr-2" />
                  Domain Rules
                </TabsTrigger>
              </TabsList>

              <TabsContent value="methods">
                <Card>
                  <CardHeader>
                    <CardTitle>Detection Methods</CardTitle>
                    <CardDescription>
                      Select the anomaly detection methods to use
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="statistical"
                          checked={selectedMethods.includes('statistical')}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedMethods([...selectedMethods, 'statistical']);
                            } else {
                              setSelectedMethods(selectedMethods.filter(m => m !== 'statistical'));
                            }
                          }}
                        />
                        <Label htmlFor="statistical" className="font-medium">Statistical Methods</Label>
                        <span className="text-sm text-gray-500 ml-2">
                          (Z-score, IQR)
                        </span>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="isolation_forest"
                          checked={selectedMethods.includes('isolation_forest')}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedMethods([...selectedMethods, 'isolation_forest']);
                            } else {
                              setSelectedMethods(selectedMethods.filter(m => m !== 'isolation_forest'));
                            }
                          }}
                        />
                        <Label htmlFor="isolation_forest" className="font-medium">Isolation Forest</Label>
                        <span className="text-sm text-gray-500 ml-2">
                          (Machine learning algorithm)
                        </span>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="local_outlier_factor"
                          checked={selectedMethods.includes('local_outlier_factor')}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedMethods([...selectedMethods, 'local_outlier_factor']);
                            } else {
                              setSelectedMethods(selectedMethods.filter(m => m !== 'local_outlier_factor'));
                            }
                          }}
                        />
                        <Label htmlFor="local_outlier_factor" className="font-medium">Local Outlier Factor</Label>
                        <span className="text-sm text-gray-500 ml-2">
                          (Density-based algorithm)
                        </span>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="domain_rules"
                          checked={selectedMethods.includes('domain_rules')}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedMethods([...selectedMethods, 'domain_rules']);
                            } else {
                              setSelectedMethods(selectedMethods.filter(m => m !== 'domain_rules'));
                            }
                          }}
                        />
                        <Label htmlFor="domain_rules" className="font-medium">Domain Rules</Label>
                        <span className="text-sm text-gray-500 ml-2">
                          (Custom business rules)
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="parameters">
                <Card>
                  <CardHeader>
                    <CardTitle>Algorithm Parameters</CardTitle>
                    <CardDescription>
                      Configure parameters for the selected detection methods
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {selectedMethods.includes('statistical') && (
                        <div className="space-y-4">
                          <h3 className="text-lg font-medium">Statistical Methods</h3>

                          <div className="space-y-2">
                            <Label htmlFor="statistical_method">Method</Label>
                            <div className="flex space-x-4">
                              <div className="flex items-center space-x-2">
                                <input
                                  type="radio"
                                  id="z_score"
                                  name="statistical_method"
                                  value="z_score"
                                  checked={statisticalMethod === 'z_score'}
                                  onChange={() => setStatisticalMethod('z_score')}
                                />
                                <Label htmlFor="z_score">Z-Score</Label>
                              </div>
                              <div className="flex items-center space-x-2">
                                <input
                                  type="radio"
                                  id="iqr"
                                  name="statistical_method"
                                  value="iqr"
                                  checked={statisticalMethod === 'iqr'}
                                  onChange={() => setStatisticalMethod('iqr')}
                                />
                                <Label htmlFor="iqr">IQR</Label>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <Label htmlFor="threshold">Threshold ({threshold})</Label>
                              <span className="text-sm text-gray-500">
                                {statisticalMethod === 'z_score' ? 'Standard deviations' : 'IQR multiplier'}
                              </span>
                            </div>
                            <div className="flex items-center space-x-4">
                              <Slider
                                id="threshold"
                                value={[threshold]}
                                min={1}
                                max={5}
                                step={0.1}
                                onValueChange={(value) => setThreshold(value[0])}
                                className="flex-1"
                              />
                              <Input
                                type="number"
                                value={threshold}
                                onChange={(e) => setThreshold(parseFloat(e.target.value))}
                                min={1}
                                max={5}
                                step={0.1}
                                className="w-20"
                              />
                            </div>
                          </div>
                        </div>
                      )}

                      {(selectedMethods.includes('isolation_forest') || selectedMethods.includes('local_outlier_factor')) && (
                        <div className="space-y-4">
                          <h3 className="text-lg font-medium">Machine Learning Parameters</h3>

                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <Label htmlFor="contamination">Contamination ({(contamination * 100).toFixed(1)}%)</Label>
                              <span className="text-sm text-gray-500">
                                Expected percentage of anomalies
                              </span>
                            </div>
                            <div className="flex items-center space-x-4">
                              <Slider
                                id="contamination"
                                value={[contamination]}
                                min={0.01}
                                max={0.2}
                                step={0.01}
                                onValueChange={(value) => setContamination(value[0])}
                                className="flex-1"
                              />
                              <Input
                                type="number"
                                value={contamination}
                                onChange={(e) => setContamination(parseFloat(e.target.value))}
                                min={0.01}
                                max={0.2}
                                step={0.01}
                                className="w-20"
                              />
                            </div>
                          </div>

                          {selectedMethods.includes('local_outlier_factor') && (
                            <div className="space-y-2">
                              <div className="flex justify-between">
                                <Label htmlFor="n_neighbors">Neighbors ({nNeighbors})</Label>
                                <span className="text-sm text-gray-500">
                                  Number of neighbors for LOF
                                </span>
                              </div>
                              <div className="flex items-center space-x-4">
                                <Slider
                                  id="n_neighbors"
                                  value={[nNeighbors]}
                                  min={5}
                                  max={50}
                                  step={1}
                                  onValueChange={(value) => setNNeighbors(value[0])}
                                  className="flex-1"
                                />
                                <Input
                                  type="number"
                                  value={nNeighbors}
                                  onChange={(e) => setNNeighbors(parseInt(e.target.value))}
                                  min={5}
                                  max={50}
                                  step={1}
                                  className="w-20"
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="columns">
                <Card>
                  <CardHeader>
                    <CardTitle>Column Selection</CardTitle>
                    <CardDescription>
                      Select columns to analyze for anomalies
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {columns.map(column => (
                        <div key={column} className="flex items-center space-x-2">
                          <Checkbox
                            id={`column-${column}`}
                            checked={selectedColumns.includes(column)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedColumns([...selectedColumns, column]);
                              } else {
                                setSelectedColumns(selectedColumns.filter(c => c !== column));
                              }
                            }}
                          />
                          <Label htmlFor={`column-${column}`}>{column}</Label>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="rules">
                <DomainRulesForm
                  columns={columns}
                  rules={domainRules}
                  onChange={setDomainRules}
                  onApply={() => setActiveTab('methods')}
                  isLoading={isLoading}
                />
              </TabsContent>
            </Tabs>

            <div className="flex justify-between mt-8">
              <Button
                variant="outline"
                onClick={() => navigate('/data-cleaning')}
                disabled={isLoading}
              >
                <ArrowLeft size={16} className="mr-2" />
                Cancel
              </Button>

              <Button
                className="bg-teal hover:bg-teal/90"
                onClick={runAnomalyDetection}
                disabled={isLoading || selectedMethods.length === 0 || selectedColumns.length === 0}
              >
                {isLoading ? (
                  <>
                    <Loader2 size={16} className="mr-2 animate-spin" />
                    Processing...
                  </>
                ) : (
                  <>
                    Run Anomaly Detection
                    <ArrowRight size={16} className="ml-2" />
                  </>
                )}
              </Button>
            </div>
          </>
        )}

        {/* Show loading state */}
        {isLoading && (
          <div className="flex flex-col items-center justify-center py-16">
            <Loader2 size={48} className="animate-spin text-teal mb-4" />
            <p className="text-lg text-gray-600">Starting anomaly detection job...</p>
            <p className="text-sm text-gray-500 mt-2">This may take a few moments</p>
          </div>
        )}

        {/* Show error state */}
        {error && (
          <Alert variant="destructive" className="my-8">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
};

export default AnomalyDetectionConfig;
