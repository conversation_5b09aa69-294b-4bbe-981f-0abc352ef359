import logging
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd
from sqlalchemy.orm import Session

from app.models.data_source import DataSource
from app.schemas.reconciliation import ReconciliationRule
from app.services.reconciliation.semantic_matcher import semantic_match
from app.utils.gcp_storage import storage_client
from app.utils.file_handlers import process_csv_file

logger = logging.getLogger(__name__)


def match_data_sources(
    source_a_id: int,
    source_b_id: int,
    rules: List[ReconciliationRule],
    db: Session
) -> Dict[str, Any]:
    """
    Match data from two sources based on reconciliation rules.
    
    Args:
        source_a_id: ID of the first data source
        source_b_id: ID of the second data source
        rules: List of reconciliation rules
        db: Database session
        
    Returns:
        Dictionary with reconciliation results
    """
    try:
        # Get data sources
        source_a = db.query(DataSource).filter(DataSource.id == source_a_id).first()
        source_b = db.query(DataSource).filter(DataSource.id == source_b_id).first()
        
        if not source_a or not source_b:
            raise ValueError("Data sources not found")
        
        # Load data from sources
        data_a = load_data_from_source(source_a)
        data_b = load_data_from_source(source_b)
        
        # Apply matching rules
        matches = []
        anomalies = []
        unmatched = []
        
        # Track matched records to avoid duplicates
        matched_a_indices = set()
        matched_b_indices = set()
        
        # Apply each rule
        for rule in rules:
            if rule.rule_type == "exact_match":
                rule_matches, rule_anomalies = exact_match(
                    data_a, data_b, rule.source_fields, rule.target_fields
                )
            elif rule.rule_type == "fuzzy_match":
                rule_matches, rule_anomalies = fuzzy_match(
                    data_a, data_b, rule.source_fields, rule.target_fields, rule.threshold
                )
            elif rule.rule_type == "semantic_match":
                rule_matches, rule_anomalies = semantic_match(
                    data_a, data_b, rule.source_fields, rule.target_fields, rule.threshold
                )
            else:
                raise ValueError(f"Unsupported rule type: {rule.rule_type}")
            
            # Add matches and anomalies
            for match in rule_matches:
                if match["source_index"] not in matched_a_indices and match["target_index"] not in matched_b_indices:
                    matches.append(match)
                    matched_a_indices.add(match["source_index"])
                    matched_b_indices.add(match["target_index"])
            
            for anomaly in rule_anomalies:
                if anomaly["source_index"] not in matched_a_indices and anomaly["target_index"] not in matched_b_indices:
                    anomalies.append(anomaly)
                    matched_a_indices.add(anomaly["source_index"])
                    matched_b_indices.add(anomaly["target_index"])
        
        # Find unmatched records
        for i, record in enumerate(data_a):
            if i not in matched_a_indices:
                unmatched.append({
                    "record": record,
                    "source": "a",
                    "index": i
                })
        
        for i, record in enumerate(data_b):
            if i not in matched_b_indices:
                unmatched.append({
                    "record": record,
                    "source": "b",
                    "index": i
                })
        
        # Calculate statistics
        total_records = len(data_a) + len(data_b)
        matched_records = len(matches) * 2  # Each match involves two records
        anomaly_records = len(anomalies) * 2  # Each anomaly involves two records
        unmatched_records = len(unmatched)
        match_rate = matched_records / total_records if total_records > 0 else 0
        
        # Group anomalies by type
        anomaly_details = {}
        for anomaly in anomalies:
            anomaly_type = anomaly.get("anomaly_type", "unknown")
            if anomaly_type not in anomaly_details:
                anomaly_details[anomaly_type] = 0
            anomaly_details[anomaly_type] += 1
        
        return {
            "total_records": total_records,
            "matched_records": matched_records,
            "anomaly_records": anomaly_records,
            "unmatched_records": unmatched_records,
            "match_rate": match_rate,
            "anomaly_details": anomaly_details,
            "result_data": {
                "matches": matches,
                "anomalies": anomalies,
                "unmatched": unmatched
            }
        }
    except Exception as e:
        logger.error(f"Error matching data sources: {e}")
        raise ValueError(f"Error matching data sources: {str(e)}")


def load_data_from_source(source: DataSource) -> List[Dict[str, Any]]:
    """
    Load data from a data source.
    
    Args:
        source: Data source model
        
    Returns:
        List of dictionaries representing the data
    """
    if source.source_type == "file":
        # Download file from GCS
        local_path = f"/tmp/{source.file_path.split('/')[-1]}"
        storage_client.download_file(source.file_path, local_path)
        
        # Read file based on type
        if source.file_type == "csv":
            df = pd.read_csv(local_path)
            return df.to_dict(orient="records")
        elif source.file_type == "excel":
            df = pd.read_excel(local_path)
            return df.to_dict(orient="records")
        elif source.file_type == "json":
            df = pd.read_json(local_path)
            return df.to_dict(orient="records")
        else:
            raise ValueError(f"Unsupported file type: {source.file_type}")
    elif source.source_type == "database":
        # Implement database connection logic
        raise NotImplementedError("Database source loading not implemented yet")
    elif source.source_type == "cloud_service":
        # Implement cloud service connection logic
        raise NotImplementedError("Cloud service source loading not implemented yet")
    else:
        raise ValueError(f"Unsupported source type: {source.source_type}")


def exact_match(
    data_a: List[Dict[str, Any]],
    data_b: List[Dict[str, Any]],
    source_fields: List[str],
    target_fields: List[str]
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Perform exact matching between two datasets.
    
    Args:
        data_a: First dataset
        data_b: Second dataset
        source_fields: Fields to match from the first dataset
        target_fields: Fields to match from the second dataset
        
    Returns:
        Tuple containing:
        - List of matches
        - List of anomalies
    """
    matches = []
    anomalies = []
    
    # Create a lookup dictionary for the second dataset
    lookup = {}
    for i, record in enumerate(data_b):
        key_parts = []
        for field in target_fields:
            if field in record:
                key_parts.append(str(record[field]))
            else:
                key_parts.append("")
        
        key = "|".join(key_parts)
        if key not in lookup:
            lookup[key] = []
        lookup[key].append((i, record))
    
    # Match records from the first dataset
    for i, record_a in enumerate(data_a):
        key_parts = []
        for field in source_fields:
            if field in record_a:
                key_parts.append(str(record_a[field]))
            else:
                key_parts.append("")
        
        key = "|".join(key_parts)
        if key in lookup and lookup[key]:
            # Exact match found
            j, record_b = lookup[key][0]  # Take the first match
            matches.append({
                "source_record": record_a,
                "target_record": record_b,
                "source_index": i,
                "target_index": j,
                "confidence": 1.0,
                "matched_fields": dict(zip(source_fields, target_fields))
            })
            
            # Remove the matched record from lookup to avoid duplicates
            lookup[key].pop(0)
        else:
            # Try to find a partial match
            best_match = None
            best_score = 0
            
            for lookup_key, lookup_records in lookup.items():
                if not lookup_records:
                    continue
                
                # Count matching fields
                match_count = 0
                total_fields = len(source_fields)
                
                for idx, (source_field, target_field) in enumerate(zip(source_fields, target_fields)):
                    if source_field in record_a and target_field in lookup_records[0][1]:
                        source_value = str(record_a[source_field])
                        target_value = str(lookup_records[0][1][target_field])
                        
                        if source_value == target_value:
                            match_count += 1
                
                score = match_count / total_fields
                if score > best_score and score >= 0.5:  # At least 50% match
                    best_score = score
                    j, record_b = lookup_records[0]
                    best_match = {
                        "source_record": record_a,
                        "target_record": record_b,
                        "source_index": i,
                        "target_index": j,
                        "confidence": score,
                        "matched_fields": dict(zip(source_fields, target_fields)),
                        "anomaly_type": "partial_match"
                    }
            
            if best_match:
                anomalies.append(best_match)
                # Remove the matched record from lookup
                lookup_key = "|".join([str(record_b.get(field, "")) for field in target_fields])
                lookup[lookup_key].pop(0)
    
    return matches, anomalies


def fuzzy_match(
    data_a: List[Dict[str, Any]],
    data_b: List[Dict[str, Any]],
    source_fields: List[str],
    target_fields: List[str],
    threshold: float = 0.8
) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    """
    Perform fuzzy matching between two datasets.
    
    Args:
        data_a: First dataset
        data_b: Second dataset
        source_fields: Fields to match from the first dataset
        target_fields: Fields to match from the second dataset
        threshold: Confidence threshold for matches
        
    Returns:
        Tuple containing:
        - List of matches
        - List of anomalies
    """
    # For simplicity, we'll use a basic implementation
    # In a real application, you would use a more sophisticated fuzzy matching algorithm
    
    from difflib import SequenceMatcher
    
    matches = []
    anomalies = []
    
    # For each record in the first dataset
    for i, record_a in enumerate(data_a):
        best_match = None
        best_score = 0
        best_j = -1
        
        # Compare with each record in the second dataset
        for j, record_b in enumerate(data_b):
            # Calculate similarity for each field pair
            field_scores = []
            
            for source_field, target_field in zip(source_fields, target_fields):
                if source_field in record_a and target_field in record_b:
                    source_value = str(record_a[source_field])
                    target_value = str(record_b[target_field])
                    
                    # Calculate string similarity
                    similarity = SequenceMatcher(None, source_value, target_value).ratio()
                    field_scores.append(similarity)
                else:
                    field_scores.append(0)
            
            # Calculate overall score
            if field_scores:
                score = sum(field_scores) / len(field_scores)
                
                if score > best_score:
                    best_score = score
                    best_j = j
                    best_match = record_b
        
        # Check if we found a good match
        if best_match and best_score >= threshold:
            matches.append({
                "source_record": record_a,
                "target_record": best_match,
                "source_index": i,
                "target_index": best_j,
                "confidence": best_score,
                "matched_fields": dict(zip(source_fields, target_fields))
            })
        elif best_match and best_score >= 0.5:  # Partial match
            anomalies.append({
                "source_record": record_a,
                "target_record": best_match,
                "source_index": i,
                "target_index": best_j,
                "confidence": best_score,
                "matched_fields": dict(zip(source_fields, target_fields)),
                "anomaly_type": "fuzzy_match_below_threshold"
            })
    
    return matches, anomalies
