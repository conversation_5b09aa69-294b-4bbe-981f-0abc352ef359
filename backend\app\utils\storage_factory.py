import os
import logging
from typing import Union

from app.utils.local_storage import LocalStorageClient, storage_client as local_storage_client
from app.utils.gcp_storage import GCPStorageClient, storage_client as gcp_storage_client

logger = logging.getLogger(__name__)

def get_storage_client() -> Union[GCPStorageClient, LocalStorageClient]:
    """
    Get the appropriate storage client based on environment variables.
    
    Returns:
        Either GCP storage client or local storage client
    """
    # Check if GCP storage is configured
    if os.environ.get("USE_GCP_STORAGE", "").lower() == "true" and os.environ.get("GCP_STORAGE_BUCKET"):
        logger.info("Using GCP storage client")
        return gcp_storage_client
    else:
        logger.info("Using local storage client")
        return local_storage_client

# Create a singleton instance
storage_client = get_storage_client()
