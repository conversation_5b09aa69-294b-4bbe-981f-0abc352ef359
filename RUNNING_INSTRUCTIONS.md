# Financial Data Analysis System - Running Instructions

This document provides detailed instructions for running the Financial Data Analysis System, including troubleshooting common issues.

## Prerequisites

- Python 3.9+ with pip
- Node.js 16+ with npm
- Git (for cloning the repository)

## Step 1: Install Python Dependencies

First, install all required Python packages:

```bash
cd backend
pip install -r requirements.txt
pip install pandas numpy openpyxl
cd ..
```

## Step 2: Start the Backend Server

There are two ways to start the backend server:

### Option 1: Using the Simplified Starter Script (Recommended)

Run the following command from the project root directory:

```bash
python start_backend.py
```

### Option 2: Using uvicorn directly

If Option 1 doesn't work, try starting the server directly with uvicorn:

```bash
cd backend
python -m uvicorn app.main:app --reload --port 8002
```

The backend server should now be running at http://localhost:8002.

## Step 3: Start the Frontend Development Server

Open a new terminal window and run:

```bash
cd frontend
npm install
npm run dev
```

The frontend should now be running at http://localhost:4001.

## Step 4: Using the Application

1. Open your browser and navigate to http://localhost:4001
2. Upload an Excel or CSV file through the Data Analyzer page
3. After uploading, click on the file to view it in the Excel Analysis page
4. Use the "Categorize Transactions" button to navigate to the Transaction Categorization page

## Troubleshooting

### Backend Issues

1. **ModuleNotFoundError: No module named 'app'**
   - Make sure you're running the command from the correct directory (backend)
   - Try using the absolute import: `python -m uvicorn app.main:app --reload --port 8002`

2. **JSON Serialization Error**
   - This should be fixed in the latest code, but if you encounter it, check for NaN values in your data

3. **Database Errors**
   - Try initializing the database manually: `cd backend && python init_db.py`

### Frontend Issues

1. **CORS Errors**
   - Make sure both the frontend and backend are running
   - Check that the backend CORS settings are correct in `backend/app/main.py`

2. **API Connection Errors**
   - Verify that the backend is running on port 8002
   - Check that the frontend is configured to connect to the correct API URL

## Key Features

1. **Excel/CSV File Upload**: Upload financial data files
2. **Data Analysis**: View and analyze the uploaded data
3. **Transaction Categorization**: Automatically categorize transactions using AI
4. **Anomaly Detection**: Identify outliers and anomalies in the data

## Important Notes

- The application uses a local SQLite database (`app.db`) for storing data
- Uploaded files are stored in the `backend/storage` directory
- The transaction categorization feature uses a simple rule-based approach with AI fallback
